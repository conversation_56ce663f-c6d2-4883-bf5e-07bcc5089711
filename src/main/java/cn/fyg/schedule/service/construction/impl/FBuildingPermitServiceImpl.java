package cn.fyg.schedule.service.construction.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.construction.FBuildingPermitRepository;
import cn.fyg.schedule.pojo.construction.FBuildingPermit;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.construction.FBuildingPermitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.CriteriaBuilder;
import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class FBuildingPermitServiceImpl implements FBuildingPermitService {
    private final FBuildingPermitRepository r;
    public FBuildingPermitServiceImpl(FBuildingPermitRepository r) {
        this.r = r;
    }
    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FBuildingPermit data) {
        BaseResponse response = BaseService.save(data, r, FBuildingPermit.class);
        // 如果保存成功，预加载懒加载的集合，避免序列化时的LazyInitializationException
        if (response.getCode() == 0 && response.getResult() instanceof FBuildingPermit) {
            FBuildingPermit savedPermit = (FBuildingPermit) response.getResult();
            preloadLazyCollections(savedPermit);
        }
        return response;
    }

    @Override
    public BaseResponse batchSave(List<FBuildingPermit> data, String creator) {
        BaseResponse response = BaseResponse.initialize();
        Integer errCount = this.save(data, creator);
        response.setMsg("导入结束：总计 " + data.size() + " 条数据，失败 " + errCount + " 条");
        return response;
    }
    private Integer save(List<FBuildingPermit> data, String creator) {
        Integer count = 0;
        for (FBuildingPermit obj : data) {
            try {
                obj.setCreator(creator);
                r.save(obj);
            } catch (Exception e) {
                log.info(e.getMessage());
                count++;
            }
        }
        return count;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse findById(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Optional<FBuildingPermit> data = r.findById(id);
            if (data.isPresent()) {
                FBuildingPermit permit = data.get();
                // 预加载懒加载的items集合，避免LazyInitializationException
                preloadLazyCollections(permit);
                baseResponse.setResult(permit);
            }
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse getCompanyHistoryList() {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<String> data = r.getCompanyHistoryList();
            baseResponse.setResult(data);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse listAll() {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FBuildingPermit> data = r.findAllByOrderBySetUpDateDesc();
            // 预加载懒加载的items集合，避免LazyInitializationException
            data.forEach(this::preloadLazyCollections);
            baseResponse.setResult(data);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse listCompanyIn(List<String> companies, String approvalStatus) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FBuildingPermit> data = !companies.isEmpty() ?r.listAllDataByCompanyIn(companies, approvalStatus):r.listAllData(approvalStatus);
            // 预加载懒加载的items集合，避免LazyInitializationException
            data.forEach(this::preloadLazyCollections);
            baseResponse.setResult(data);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    /**
     * 预加载懒加载的集合，避免LazyInitializationException
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    private void preloadLazyCollections(FBuildingPermit permit) {
        if (permit != null) {
            // 触发懒加载
            if (permit.getItems() != null) {
                permit.getItems().size();
            }
        }
    }
}
