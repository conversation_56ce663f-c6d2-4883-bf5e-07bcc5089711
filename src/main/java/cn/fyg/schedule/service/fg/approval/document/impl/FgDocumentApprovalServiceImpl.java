package cn.fyg.schedule.service.fg.approval.document.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.fg.approval.document.FgDocumentApprovalRepository;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.core.JsonQuery;
import cn.fyg.schedule.pojo.cp.approval.CpApprovalTemplate;
import cn.fyg.schedule.pojo.dto.cp.approval.CpApplyInfoDto;
import cn.fyg.schedule.pojo.dto.fy.approval.document.DocumentApprovalDTO;
import cn.fyg.schedule.pojo.dto.fy.approval.document.FgDocumentApprovalDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.pojo.fg.approval.document.FgDocumentApproval;
import cn.fyg.schedule.pojo.fg.approval.document.FgDocumentApprovalItem;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.cp.approval.CpApprovalTemplateService;
import cn.fyg.schedule.service.fg.approval.document.FgDocumentApprovalConvertService;
import cn.fyg.schedule.service.fg.approval.document.FgDocumentApprovalItemService;
import cn.fyg.schedule.service.fg.approval.document.FgDocumentApprovalService;
import cn.fyg.schedule.specification.fg.approval.document.FgDocumentApprovalSpecification;
import cn.fyg.schedule.utils.GsonUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@Slf4j
public class FgDocumentApprovalServiceImpl implements FgDocumentApprovalService {
    private final FgDocumentApprovalRepository r;
    private final CpApprovalTemplateService cpApprovalTemplateService;
    private final FgDocumentApprovalItemService itemService;
    private final FgDocumentApprovalConvertService convertService;

    public FgDocumentApprovalServiceImpl(FgDocumentApprovalRepository r, CpApprovalTemplateService cpApprovalTemplateService, FgDocumentApprovalItemService itemService, FgDocumentApprovalConvertService convertService) {
        this.r = r;
        this.cpApprovalTemplateService = cpApprovalTemplateService;
        this.itemService = itemService;
        this.convertService = convertService;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FgDocumentApprovalDto dto) {
        BaseResponse response = BaseResponse.initialize();
        try {
            FgDocumentApproval data = FgDocumentApproval.initialized(dto);
            FgDocumentApproval result = BaseService.saveWithR(data, r, FgDocumentApproval.class);
            response.setResult(result);
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        } catch (Exception e) {
            response.setCode(500);
            response.setMsg("系统错误");
        }

        return response;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        try {
            BaseService.deleteWithR(id, r);
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse update(FgDocumentApprovalDto dto) {
        BaseResponse response = BaseResponse.initialize();
        try {
            Integer count = update(dto.getTitle(), dto.getDraftedDate(), dto.getDraftedDepartment(),
                    dto.getContentAbstract(), dto.getAttachment(), dto.getId());
            response.setResult(count);
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }

    //审批单号更新
    @Override
    public BaseResponse update(String spNo, Integer id) {
        BaseResponse response = BaseResponse.initialize();
        try {
            Integer count = updateSpNo(spNo, id);
            response.setResult(count);
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }

    private Integer updateSpNo(String spNo, Integer id) throws MyException {
        try {
            return r.update(spNo, id);
        } catch (JpaSystemException e) {
            log.error(e.getMessage());
            throw new MyException(500, "操作失败");
        }
    }

    //是否提交审批状态更新
    @Override
    public BaseResponse update(Integer isApplied, Integer id) {
        BaseResponse response = BaseResponse.initialize();
        try {
            Integer count = updateApplyStatus(isApplied, id);
            response.setResult(count);
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }

    private Integer updateApplyStatus(Integer isApplied, Integer id) throws MyException {
        try {
            return r.update(isApplied, id);
        } catch (JpaSystemException e) {
            log.error(e.getMessage());
            throw new MyException(500, "操作失败");
        }
    }

    //数据更新
    private Integer update(String title, Long draftedDate, String draftedDepartment, String contentAbstract, String attachment, Integer id) throws MyException {
        try {
            return r.update(title, draftedDate == null ? null : DateUtil.date(draftedDate), draftedDepartment, contentAbstract, attachment, id);
        } catch (JpaSystemException e) {
            log.error(e.getMessage());
            throw new MyException(500, "操作失败");
        }
    }

    @Override
    public BaseResponse list(Pagination pagination) {
        BaseResponse response = BaseResponse.initialize();
        try {
            Sort sort = Sort.unsorted();
            if (!StrUtil.isEmpty(pagination.getColumnKey())) {
                if (!pagination.getOrder().equals("false")) {
                    sort = Sort.by(pagination.getOrder().equals("ascend") ? Sort.Direction.ASC : Sort.Direction.DESC, pagination.getColumnKey());
                }
            }
            Pageable pageable = PageRequest.of(pagination.getCurrentPage(), pagination.getPageSize(), sort);
            response.setResult(convert(getData(pageable)));
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        } catch (Exception e) {
            response.setCode(500);
            response.setMsg("系统错误");
        }
        return response;
    }

    /**
     * find data by id
     *
     * @param id Integer
     * @return BaseResponse
     */
    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        try {
            response.setResult(convertDTO(getById(id)));
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        } catch (Exception e) {
            response.setCode(500);
            response.setMsg("系统错误。");
        }
        return response;
    }

    @Override
    public BaseResponse findBySpNo(String spNo) {
        BaseResponse response = BaseResponse.initialize();
        try {
            FgDocumentApproval data = getBySpNo(spNo);
            if (data != null) {
                response.setResult(convertDTO(data));
            } else {
                response.setCode(401);
                response.setMsg("未查到审批编号:" + spNo);
            }

        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        } catch (Exception e) {
            response.setCode(500);
            response.setMsg("系统错误。");
        }
        return response;
    }

    @Override
    public BaseResponse synchronize(String spNo, String templateId, String contents) {
        return synchronizeCommon(spNo, templateId, contents, null);
    }

    @Override
    public BaseResponse synchronize(String spNo, String templateId, String contents, String records) {
        return synchronizeCommon(spNo, templateId, contents, records);
    }

    @Override
    public BaseResponse filter(FgDocumentApprovalDto dto, Pagination pagination) {
        BaseResponse response = BaseResponse.initialize();
        try {
            Sort sort = Sort.unsorted();
            if (!StrUtil.isEmpty(pagination.getColumnKey())) {
                if (!pagination.getOrder().equals("false")) {
                    sort = Sort.by(pagination.getOrder().equals("ascend") ? Sort.Direction.ASC : Sort.Direction.DESC, pagination.getColumnKey());
                }
            }
            Pageable pageable = PageRequest.of(pagination.getCurrentPage(), pagination.getPageSize(), sort);
            Specification<FgDocumentApproval> specification = getSpecification(dto);
            Page<FgDocumentApproval> data = r.findAll(specification, pageable);
            response.setResult(data);
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        } catch (Exception e) {
            log.info(e.getMessage());
            response.setCode(1);
            response.setMsg("查询出错");
        }
        return response;
    }

    private Specification<FgDocumentApproval> getSpecification(FgDocumentApprovalDto dto) throws MyException {
        try {
            Specification<FgDocumentApproval> specification = Specification.where(StrUtil.isEmpty(dto.getSpNo()) ? null : FgDocumentApprovalSpecification.ifContains("spNo", dto.getSpNo()));
            if (!StrUtil.isEmpty(dto.getJsonQuery())) {
               specification = setJsonQuery(specification, dto.getJsonQuery());
            }
            return specification;
        } catch (MyException e) {
            throw new MyException(e.getCode(), e.getErrMsg());
        } catch (Exception e) {
            throw new MyException(500, "系统错误");
        }
    }

    private Specification<FgDocumentApproval> setJsonQuery(Specification<FgDocumentApproval> specification, String jsonQuery) throws MyException {
        try {
            List<JsonQuery> queries = GsonUtil.fromJsonList(jsonQuery, JsonQuery.class);
            Specification<FgDocumentApproval> finalSpecification = specification;
            for (JsonQuery query : queries) {
                finalSpecification = finalSpecification.and(StrUtil.isEmpty(query.getKey()) ? null : FgDocumentApprovalSpecification.ifFieldContains(query.getKey(), query.getColumn(), query.getValue()));
            }
            return finalSpecification;
        } catch (Exception e) {
            throw new MyException(500, "处理json出错");
        }
    }

    private void saveItem(Integer id, String jsonStr) throws MyException {
        try {
            FgDocumentApprovalItem data = new FgDocumentApprovalItem(id, jsonStr);
            itemService.saveItem(data);
        } catch (MyException e) {
            throw new MyException(500, "新增记录失败");
        }
    }
    private BaseResponse synchronizeCommon(String spNo, String templateId, String contents, String records) {
        BaseResponse response = BaseResponse.initialize();
        try {
            CpApprovalTemplate cpApprovalTemplate = cpApprovalTemplateService.getByTemplateId(templateId, 0);
            if (cpApprovalTemplate == null) {
                response.setCode(101);
                response.setMsg("没有相关模板");
                return response;
            }

            JSONObject jsonObject = JSONUtil.parseObj(cpApprovalTemplate.getTemplateStructure());
            CpApplyInfoDto template = jsonObject.getBean("applyData", CpApplyInfoDto.class);
            List<CpApplyInfoDto.Control> templateContents = template.getContents();
            List<CpApplyInfoDto.Control> targetContents = GsonUtil.fromJsonList(contents, CpApplyInfoDto.Control.class);

            FgDocumentApproval result = getBySpNo(spNo);
            if (result == null) {
                FgDocumentApproval data = contentsDTO(templateContents, targetContents);
                data.setSpNo(spNo);
                data.setIsApplied(1);
                data.setCurrentRecord(records);
                data = BaseService.saveWithR(data, r, FgDocumentApproval.class);
                response.setResult(data);
                if (records != null) {
                    saveItem(data.getId(), records);
                }
            } else {
                if (records != null) {
                    saveItem(result.getId(), records);
                } else {
                    response.setCode(1);
                    response.setMsg("审批编号已存在。" + spNo);
                }
            }
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        } catch (Exception e) {
            response.setCode(500);
            response.setMsg("系统错误");
        }
        return response;
    }

    private FgDocumentApproval contentsDTO(List<CpApplyInfoDto.Control> templateContents, List<CpApplyInfoDto.Control> targetContents) throws MyException {
        FgDocumentApproval data = new FgDocumentApproval();

        // 将 targetContents 转换为 Map 以提高搜索效率
        Map<String, CpApplyInfoDto.Control> targetContentsMap = new HashMap<>();
        for (CpApplyInfoDto.Control targetContent : targetContents) {
            targetContentsMap.put(targetContent.getId(), targetContent);
        }

        for (CpApplyInfoDto.Control templateContent : templateContents) {
            CpApplyInfoDto.Control targetContent = targetContentsMap.get(templateContent.getId());
            if (targetContent != null) {
                try {
                    switch (templateContent.getControl()) {
                        case "Number":
                            ReflectUtil.setFieldValue(data, templateContent.getKey(), targetContent.getValue().getNewNumber());
                            break;
                        case "Money":
                            ReflectUtil.setFieldValue(data, templateContent.getKey(), targetContent.getValue().getNewMoney());
                            break;
                        case "Date":
                            handleDateType(data, templateContent, targetContent);
                            break;
                        case "Text":
                        case "Textarea":
                            ReflectUtil.setFieldValue(data, templateContent.getKey(), targetContent.getValue().getText());
                            break;
                    }
                } catch (Exception e) {
                    // 处理异常，如日志记录
                    log.error(e.getMessage());
                    throw new MyException(500, "数据转换出错");
                }
            }
        }
        return data;
    }

    private void handleDateType(FgDocumentApproval data, CpApplyInfoDto.Control templateContent, CpApplyInfoDto.Control targetContent) {
        if (!StrUtil.isEmpty(targetContent.getValue().getDate().getTimestamp())) {
            String timestampStr = targetContent.getValue().getDate().getTimestamp();
            if ("day".equals(targetContent.getValue().getDate().getType())) {
                timestampStr += "000";
            }
            try {
                ReflectUtil.setFieldValue(data, templateContent.getKey(), Long.valueOf(timestampStr));
            } catch (NumberFormatException e) {
                // 日志记录或其他异常处理
                e.printStackTrace();
            }
        }
    }

    private FgDocumentApproval getBySpNo(String spNo) throws MyException {
        try {
            Optional<FgDocumentApproval> optional = r.findBySpNo(spNo);
            return optional.orElse(null);
        } catch (JpaSystemException e) {
            throw new MyException(500, "操作出错。");
        }
    }

    private FgDocumentApproval getById(Integer id) throws MyException {
        try {
            return BaseService.getById(id, r);
        } catch (MyException e) {
            throw new MyException(e.getCode(), e.getErrMsg());
        }
    }

    private Page<DocumentApprovalDTO> convert(Page<FgDocumentApproval> pageA) {
        return pageA.map(this::convertDTO);
    }
    private DocumentApprovalDTO convertDTO(FgDocumentApproval data) {
        return convertService.convertToDTO(data);
    }

    private Page<FgDocumentApproval> getData(Pageable pageable) throws MyException {
        try {
            return r.findAll(pageable);
        } catch (JpaSystemException e) {
            log.error(e.getMessage());
            throw new MyException(500, "查询出错");
        }
    }
}
