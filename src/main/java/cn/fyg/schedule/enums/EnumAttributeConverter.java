package cn.fyg.schedule.enums;

import javax.persistence.AttributeConverter;
import javax.persistence.Convert;
import java.util.Arrays;

@Convert
public abstract class EnumAttributeConverter <E extends Enum<E> & BaseEnum<T>, T> implements AttributeConverter<E, T> {
    private final Class<E> clazz;

    protected EnumAttributeConverter(Class<E> clazz) {
        this.clazz = clazz;
    }

    @Override
    public T convertToDatabaseColumn(E attribute) {
        if (attribute == null) {
            return null;
        }
        return attribute.getValue();
    }

    @Override
    public E convertToEntityAttribute(T dbData) {
        if (dbData == null) {
            return null;
        }
        return Arrays.stream(clazz.getEnumConstants())
                .filter(e -> e.getValue().equals(dbData))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Unknown database value: " + dbData));
    }
}
