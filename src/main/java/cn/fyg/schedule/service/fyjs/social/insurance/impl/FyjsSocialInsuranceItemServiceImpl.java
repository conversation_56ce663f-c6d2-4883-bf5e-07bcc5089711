package cn.fyg.schedule.service.fyjs.social.insurance.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.fyjs.social.insurance.FyjsSocialInsuranceItemRepository;
import cn.fyg.schedule.pojo.fyjs.social.insurance.FyjsSocialInsuranceItem;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.fyjs.social.insurance.FyjsSocialInsuranceItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;

@Service
@Slf4j
public class FyjsSocialInsuranceItemServiceImpl implements FyjsSocialInsuranceItemService {
    private final FyjsSocialInsuranceItemRepository r;

    public FyjsSocialInsuranceItemServiceImpl(FyjsSocialInsuranceItemRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FyjsSocialInsuranceItem data) {
        return BaseService.save(data, r, FyjsSocialInsuranceItem.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse deleteByParentId(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        try {
            r.deleteByParentId(id);
        } catch (Exception e) {
            response.setCode(1);
            response.setMsg("操作失败");
        }
        return response;
    }

    @Override
    public BaseResponse findById(Integer id) {
        return null;
    }

    @Override
    public BaseResponse findByParentId(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        List<FyjsSocialInsuranceItem> result = getItemsByParentId(id);
        if (result != null) {
            response.setResult(result);
        } else {
            response.setCode(1);
            response.setMsg("查询失败或没有数据。");
        }
        return response;
    }

    private List<FyjsSocialInsuranceItem> getItemsByParentId(Integer id) {
        try {
            List<FyjsSocialInsuranceItem> list = r.findByParentId(id);
            if (!list.isEmpty()) {
                return list;
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }
}
