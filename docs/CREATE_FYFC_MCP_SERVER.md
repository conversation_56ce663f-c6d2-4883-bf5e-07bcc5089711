# 创建 FYFC MCP 服务器指南

## 🎯 **项目概述**

请帮我创建一个完整的 MCP (Model Context Protocol) 服务器，用于 FYFC 评价系统和 OSS 文件管理的 AI 工具集成。

## 📋 **项目要求**

### **技术栈**
- **构建工具**: Vite + pnpm + TypeScript
- **运行环境**: Node.js 18+
- **协议**: Model Context Protocol (MCP)
- **依赖**: @modelcontextprotocol/sdk, zod, axios, form-data

### **项目结构**
```
fyfc-mcp-server/
├── package.json
├── vite.config.ts
├── tsconfig.json
├── src/
│   ├── index.ts                 # 入口文件
│   ├── server.ts               # MCP 服务器实现
│   ├── tools/                  # MCP 工具定义
│   │   ├── evaluation-tools.ts # 评价相关工具
│   │   ├── oss-tools.ts        # OSS 相关工具
│   │   └── index.ts            # 工具导出
│   ├── services/               # 业务服务层
│   │   ├── fyfc-api.ts         # FYFC API 客户端
│   │   ├── oss-api.ts          # OSS API 客户端
│   │   └── types.ts            # 类型定义
│   └── utils/                  # 工具函数
│       ├── http-client.ts      # HTTP 客户端
│       └── validation.ts       # 参数验证
└── README.md
```

## 🔧 **功能需求**

### **评价管理工具 (evaluation-tools)**

#### **1. search_evaluations**
- **功能**: 搜索评价列表，支持分页和筛选
- **参数**:
  - name (可选): 员工姓名模糊查询
  - department (可选): 部门精确查询
  - status (可选): 评价状态 (PENDING|SELF|COLLEAGUE|MANAGER|COMPLETED)
  - startDate (可选): 开始日期 (yyyy-MM-dd)
  - endDate (可选): 结束日期 (yyyy-MM-dd)
  - page (可选): 页码，默认0
  - size (可选): 每页大小，默认10
- **API**: POST /api/fyfc/evaluation/admin/search (JSON body)

#### **2. get_evaluation_detail**
- **功能**: 获取评价详情信息
- **参数**:
  - evaluationId (必需): 评价ID
  - username (可选): 查询用户名，用于权限验证
- **API**: GET /api/fyfc/evaluation/common/{evaluationId} 或 GET /api/fyfc/evaluation/staff/detail/{evaluationId}?username={username}

#### **3. get_pending_evaluations**
- **功能**: 获取用户的待评价列表
- **参数**:
  - username (必需): 用户名
  - role (必需): 用户角色 (staff|manager)
- **API**:
  - Staff: GET /api/fyfc/evaluation/staff/pending?userName={username}
  - Manager: POST /api/fyfc/evaluation/manager/pending?managerName={username} (JSON body)

#### **4. submit_evaluation_score**
- **功能**: 提交评价评分
- **参数**:
  - evaluationId (必需): 评价ID
  - evaluatorUsername (必需): 评价人用户名
  - evaluatorType (必需): 评价人类型 (EMPLOYEE|COLLEAGUE|MANAGER)
  - score (必需): 评分 (0-100)
  - comment (可选): 评价意见
- **API**:
  - Self/Colleague: POST /api/fyfc/evaluation/staff/score/self 或 /staff/score/colleague
  - Manager: POST /api/fyfc/evaluation/manager/score

#### **5. get_evaluation_scores**
- **功能**: 获取评价的所有评分记录
- **参数**:
  - evaluationId (必需): 评价ID
- **API**: GET /api/fyfc/evaluation/common/scores/{evaluationId}

### **OSS 文件管理工具 (oss-tools)**

#### **1. upload_file**
- **功能**: 上传文件到 OSS，支持中文文件名
- **参数**:
  - fileName (必需): 文件名
  - fileContent (必需): Base64 编码的文件内容
  - evaluationId (必需): 关联的评价ID
  - uploadBy (必需): 上传人用户名
  - bucketName (可选): OSS Bucket 名称
- **API**: POST /api/fyfc/oss/upload (multipart/form-data)

#### **2. get_attachments**
- **功能**: 获取评价的附件列表
- **参数**:
  - evaluationId (必需): 评价ID
  - bucketName (可选): OSS Bucket 名称
- **API**: GET /api/fyfc/oss/attachments/{evaluationId}?bucketName={bucketName}

#### **3. delete_file**
- **功能**: 删除 OSS 文件
- **参数**:
  - fileKey (必需): 文件键
  - evaluationId (必需): 关联的评价ID
  - operatorName (必需): 操作人用户名
  - bucketName (可选): OSS Bucket 名称
- **API**: DELETE /api/fyfc/oss/delete?fileKey={fileKey}&evaluationId={evaluationId}&operatorName={operatorName}&bucketName={bucketName}

#### **4. get_download_url**
- **功能**: 获取文件下载链接
- **参数**:
  - fileKey (必需): 文件键
  - expireSeconds (可选): 链接过期时间，默认3600秒
  - bucketName (可选): OSS Bucket 名称
- **API**: GET /api/fyfc/oss/url?fileKey={fileKey}&expireSeconds={expireSeconds}&bucketName={bucketName}

#### **5. preview_file**
- **功能**: 获取文件预览链接
- **参数**:
  - fileKey (必需): 文件键
  - bucketName (可选): OSS Bucket 名称
- **API**: GET /api/fyfc/oss/preview?fileKey={fileKey}&bucketName={bucketName}

## 📊 **数据类型定义**

### **Java DTO 结构 (与 TypeScript 类型完全匹配)**

#### **1. 核心评价 DTO**

```java
// FyfcEvaluationDto.java
public class FyfcEvaluationDto {
    private Integer id;
    private String department;
    private String name;
    private Long reviewDate;                    // 时间戳
    private String colleagueName;
    private String managerName;
    private List<FyfcEvaluationScoreDto> scores;
    private BigDecimal additionalScore;
    private BigDecimal score;
    private String comment;
    private Long createdAt;                     // 时间戳
    private String createdBy;
    private String updatedBy;
    private String status;
    private List<FyfcAttachmentDto> attachments; // 附件列表
}

// FyfcEvaluationDetailDto.java
public class FyfcEvaluationDetailDto {
    private Integer id;
    private String department;
    private String name;
    private Long reviewDate;                    // 时间戳
    private String colleagueName;
    private String managerName;
    private BigDecimal score;
    private BigDecimal additionalScore;
    private String status;
    private String comment;
    private String createdBy;
    private Long createdAt;                     // 时间戳
    private String updatedBy;
    private Long updatedAt;                     // 时间戳
    private List<FyfcEvaluationScoreDto> scores;
    private List<FyfcEvaluationStatusHistoryDto> statusHistory;
    private List<FyfcAttachmentDto> attachments;
}
```

#### **2. 评分相关 DTO**

```java
// FyfcEvaluationScoreDto.java
public class FyfcEvaluationScoreDto {
    private Integer id;
    private Integer evaluationId;
    private String evaluator;                   // 评价人姓名
    private String type;                        // 评价人角色类型 (UserRole)
    private BigDecimal performanceScore;        // 工作业绩得分(0-60分)
    private BigDecimal attitudeScore;           // 工作态度得分(0-10分)
    private BigDecimal abilityScore;            // 工作能力得分(0-10分)
    private BigDecimal growthScore;             // 个人成长得分(0-10分)
    private BigDecimal score;                   // 小计得分
    private String signature;                   // 评价人签名
}

// FyfcScoreFormDto.java (用于提交评分)
public class FyfcScoreFormDto {
    private Integer evaluationId;
    private String evaluator;
    private String type;
    private BigDecimal performanceScore;
    private BigDecimal attitudeScore;
    private BigDecimal abilityScore;
    private BigDecimal growthScore;
    private String signature;
    private BigDecimal evaluationScore;         // 评价总分（用于更新evaluation表的score字段）
}
```

#### **3. 附件相关 DTO**

```java
// FyfcAttachmentDto.java
public class FyfcAttachmentDto {
    private String id;                          // 附件ID（用于前端标识）
    private String fileName;                    // 原始文件名
    private String fileKey;                     // OSS 存储的文件键
    private String bucketName;                  // OSS Bucket 名称
    private Long fileSize;                      // 文件大小（字节）
    private String fileType;                    // 文件类型/MIME类型
    private String fileUrl;                     // 文件URL（用于下载/预览）
    private Long uploadTime;                    // 上传时间戳
    private String uploadBy;                    // 上传人
}
```

#### **4. API 响应 DTO**

```java
// FyfcApiResponseDto.java
public class FyfcApiResponseDto<T> {
    private Integer code;                       // 响应状态码
    private String message;                     // 响应消息
    private T data;                            // 响应数据
    private Boolean success;                    // 是否成功
    private Long timestamp;                     // 时间戳

    // 静态工厂方法
    public static <T> FyfcApiResponseDto<T> success(T data);
    public static <T> FyfcApiResponseDto<T> success(T data, String message);
    public static <T> FyfcApiResponseDto<T> error(Integer code, String message);
}

// FyfcPaginatedResponseDto.java
public class FyfcPaginatedResponseDto<T> {
    private List<T> data;                       // 数据列表
    private Integer page;                       // 当前页码
    private Integer size;                       // 每页大小
    private Long total;                         // 总记录数
    private Integer totalPages;                 // 总页数
    private Boolean hasNext;                    // 是否有下一页
    private Boolean hasPrevious;                // 是否有上一页
    private Boolean isFirst;                    // 是否为第一页
    private Boolean isLast;                     // 是否为最后一页
}
```

#### **5. 查询和更新 DTO**

```java
// FyfcEvaluationQueryDto.java
public class FyfcEvaluationQueryDto {
    private Integer page = 1;                   // 页码
    private Integer size = 20;                  // 页大小
    private String department;                  // 部门
    private String name;                        // 被评价人姓名
    private String status;                      // 评价状态
    private String createdBy;                   // 创建人
    private String colleagueName;               // 同事姓名（查询待评价的）
    private String managerName;                 // 主管姓名（查询待评价的）
    private Long reviewDateStart;               // 评价日期开始(时间戳)
    private Long reviewDateEnd;                 // 评价日期结束(时间戳)
    private Long createdAtStart;                // 创建时间开始(时间戳)
    private Long createdAtEnd;                  // 创建时间结束(时间戳)
    private String queryType = "all";           // 查询类型：all-全部，pending-待办，completed-已完成
}

// FyfcEvaluationUpdateDto.java
public class FyfcEvaluationUpdateDto {
    private Integer id;                         // 评价ID（必填）
    private String department;
    private String name;
    private Long reviewDate;                    // 时间戳
    private String colleagueName;
    private String managerName;
    private BigDecimal additionalScore;
    private BigDecimal score;
    private String comment;
    private String updateReason;                // 更新原因
    private Boolean sendNotification = false;   // 是否发送通知
}
```

#### **6. 状态历史 DTO**

```java
// FyfcEvaluationStatusHistoryDto.java
public class FyfcEvaluationStatusHistoryDto {
    private Integer id;
    private Integer evaluationId;               // 评价主表ID
    private String previousStatus;              // 之前状态
    private String newStatus;                   // 新状态
    private String changedBy;                   // 状态变更人
    private Long changedAt;                     // 变更时间(时间戳)
    private String remark;                      // 变更备注
}
```

### **TypeScript 类型定义 (与 Java DTO 匹配)**

```typescript
// 评价状态和角色类型
type EvaluationStatus = 'PENDING' | 'SELF' | 'COLLEAGUE' | 'MANAGER' | 'COMPLETED';
type EvaluatorType = 'EMPLOYEE' | 'COLLEAGUE' | 'MANAGER';

// 核心评价类型
interface FyfcEvaluationDto {
  id: number;
  department: string;
  name: string;
  reviewDate: number;                         // 时间戳
  colleagueName?: string;
  managerName?: string;
  scores?: FyfcEvaluationScoreDto[];
  additionalScore?: number;
  score?: number;
  comment?: string;
  createdAt: number;                          // 时间戳
  createdBy?: string;
  updatedBy?: string;
  status: string;
  attachments: FyfcAttachmentDto[];
}

interface FyfcEvaluationDetailDto extends FyfcEvaluationDto {
  updatedAt?: number;                         // 时间戳
  statusHistory?: FyfcEvaluationStatusHistoryDto[];
}

// 评分类型
interface FyfcEvaluationScoreDto {
  id: number;
  evaluationId: number;
  evaluator: string;                          // 评价人姓名
  type: string;                               // 评价人角色类型
  performanceScore: number;                   // 工作业绩得分
  attitudeScore: number;                      // 工作态度得分
  abilityScore: number;                       // 工作能力得分
  growthScore: number;                        // 个人成长得分
  score: number;                              // 小计得分
  signature?: string;                         // 评价人签名
}

// 附件类型
interface FyfcAttachmentDto {
  id: string;
  fileName: string;
  fileKey: string;
  bucketName?: string;
  fileSize: number;
  fileType: string;
  fileUrl?: string;
  uploadTime: number;
  uploadBy: string;
}

// API 响应类型
interface FyfcApiResponse<T> {
  code?: number;
  message: string;
  data?: T;
  success: boolean;
  timestamp?: number;
}

interface FyfcPaginatedResponse<T> {
  data: T[];
  page: number;
  size: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
  isFirst: boolean;
  isLast: boolean;
}
```

### **重要说明：Java 与 TypeScript 类型对应关系**

| Java 类型 | TypeScript 类型 | 说明 |
|-----------|----------------|------|
| `Integer` | `number` | 整数类型 |
| `Long` | `number` | 时间戳，Java 使用 Long，TS 使用 number |
| `BigDecimal` | `number` | 金额/评分，Java 使用 BigDecimal，TS 使用 number |
| `String` | `string` | 字符串类型 |
| `Boolean` | `boolean` | 布尔类型 |
| `List<T>` | `T[]` | 数组类型 |
| `Date` | `number` | 时间类型，统一使用时间戳 |

**关键注意事项**：
1. **时间字段**：Java 端使用 `Long` 存储时间戳，TypeScript 使用 `number`
2. **数值字段**：Java 端使用 `BigDecimal` 处理精度，TypeScript 使用 `number`
3. **可选字段**：TypeScript 中用 `?` 标记的字段在 Java 中可以为 null
4. **枚举类型**：Java 枚举在 DTO 中转换为 `String`，TypeScript 使用联合类型

### **重要字段映射说明**

#### **评分提交字段映射**
| 前端字段 | Java DTO 字段 | 说明 |
|----------|---------------|------|
| `evaluatorUsername` | `evaluator` | 评价人姓名 |
| `evaluatorType` | `type` | 评价人类型 |
| `score` | `evaluationScore` | 评价总分（用于更新主表） |

#### **查询参数字段映射**
| 前端字段 | Java DTO 字段 | 说明 |
|----------|---------------|------|
| `startDate` | `reviewDateStart` | 评价日期开始 |
| `endDate` | `reviewDateEnd` | 评价日期结束 |
| `page` | `page` | 页码（Java从1开始，前端可能从0开始） |

#### **响应数据字段映射**
| Java DTO 字段 | TypeScript 字段 | 说明 |
|---------------|-----------------|------|
| `colleagueName` | `colleagueName` | 同事姓名 |
| `managerName` | `managerName` | 经理姓名 |
| `additionalScore` | `additionalScore` | 附加分 |
| `performanceScore` | `performanceScore` | 工作业绩得分 |
| `attitudeScore` | `attitudeScore` | 工作态度得分 |
| `abilityScore` | `abilityScore` | 工作能力得分 |
| `growthScore` | `growthScore` | 个人成长得分 |

## 🔧 **实现要求**

### **1. 项目配置**
- 使用 Vite 作为构建工具
- 配置 TypeScript 严格模式
- 支持 ESM 模块格式
- 配置路径别名 `@/` 指向 `src/`

### **2. HTTP 客户端**
- 创建通用的 HTTP 客户端类
- 支持请求/响应拦截器
- 支持文件上传 (multipart/form-data)
- 统一错误处理
- 请求日志记录

### **3. 参数验证**
- 使用 Zod 进行参数验证
- 为每个工具定义验证 Schema
- 提供详细的验证错误信息

### **4. MCP 服务器**
- 实现标准的 MCP 协议
- 支持工具列表和工具调用
- 统一的错误处理和响应格式
- 支持 stdio 传输

### **5. 错误处理**
- 网络错误重试机制
- API 错误统一处理
- 参数验证错误提示
- 详细的错误日志

## 🌐 **API 配置**

### **基础配置**
- **Base URL**: `https://localhost:7001/fyschedule2`
- **超时时间**: 30秒
- **Content-Type**: `application/json` (除文件上传外)

### **环境变量**
- `FYFC_BASE_URL`: FYFC 服务基础URL，默认 `https://localhost:7001/fyschedule2`

### **API 方法修正说明**
根据实际的 Controller 实现，以下 API 使用特定的 HTTP 方法：

#### **POST 方法的 API**
- `search_evaluations`: POST /api/fyfc/evaluation/admin/search (JSON body)
- `get_pending_evaluations` (manager): POST /api/fyfc/evaluation/manager/pending (JSON body)
- `submit_evaluation_score`: POST (不同评价类型使用不同端点)
- `upload_file`: POST /api/fyfc/oss/upload (multipart/form-data)

#### **GET 方法的 API**
- `get_evaluation_detail`: GET /api/fyfc/evaluation/common/{id}
- `get_pending_evaluations` (staff): GET /api/fyfc/evaluation/staff/pending
- `get_evaluation_scores`: GET /api/fyfc/evaluation/common/scores/{id}
- `get_attachments`: GET /api/fyfc/oss/attachments/{evaluationId}
- `get_download_url`: GET /api/fyfc/oss/url (查询参数)
- `preview_file`: GET /api/fyfc/oss/preview (查询参数)

#### **DELETE 方法的 API**
- `delete_file`: DELETE /api/fyfc/oss/delete (查询参数)

## 📦 **构建和部署**

### **开发脚本**
```json
{
  "scripts": {
    "dev": "tsx src/index.ts",
    "build": "vite build",
    "start": "node dist/index.js",
    "test": "vitest",
    "type-check": "tsc --noEmit"
  }
}
```

### **构建配置**
- 输出格式: ESM 和 CommonJS
- 目标环境: Node.js 18+
- 外部依赖: MCP SDK, axios, form-data, zod
- 输出目录: `dist/`

## 🎯 **使用示例**

### **启动服务器**
```bash
# 开发模式
pnpm dev

# 生产模式
pnpm build && pnpm start
```

### **在 Augment 中配置**
```json
{
  "mcpServers": {
    "fyfc": {
      "command": "node",
      "args": ["./fyfc-mcp-server/dist/index.js"],
      "env": {
        "FYFC_BASE_URL": "https://localhost:7001/fyschedule2"
      }
    }
  }
}
```

### **工具调用示例**
```typescript
// 搜索评价 (POST 请求，使用 FyfcEvaluationQueryDto 结构)
await callTool('search_evaluations', {
  department: '技术部',
  status: 'PENDING',
  page: 1,                    // 注意：Java 端页码从1开始
  size: 10,
  queryType: 'pending'
});

// 获取待评价列表 (根据角色使用不同API)
await callTool('get_pending_evaluations', {
  username: 'zhangsan',
  role: 'staff'  // 或 'manager'
});

// 提交评分 (使用 FyfcScoreFormDto 结构)
await callTool('submit_evaluation_score', {
  evaluationId: 6,
  evaluator: 'zhangsan',              // 注意：字段名是 evaluator 不是 evaluatorUsername
  type: 'EMPLOYEE',                   // 注意：字段名是 type 不是 evaluatorType
  performanceScore: 50,               // 工作业绩得分(0-60分)
  attitudeScore: 8,                   // 工作态度得分(0-10分)
  abilityScore: 9,                    // 工作能力得分(0-10分)
  growthScore: 8,                     // 个人成长得分(0-10分)
  signature: 'zhangsan',
  evaluationScore: 85                 // 评价总分（用于更新evaluation表）
});

// 上传文件 (multipart/form-data)
await callTool('upload_file', {
  fileName: '自评报告.docx',
  fileContent: 'base64EncodedContent...',
  evaluationId: 6,
  uploadBy: '张三'
});

// 删除文件 (查询参数)
await callTool('delete_file', {
  fileKey: 'fyfc/evaluation/6/2025/06/06/file.docx',
  evaluationId: 6,
  operatorName: 'admin'
});

// 获取评价详情 (返回 FyfcEvaluationDetailDto)
await callTool('get_evaluation_detail', {
  evaluationId: 6,
  username: 'zhangsan'  // 可选，用于权限验证
});
```

## 📝 **文档要求**

### **README.md**
- 项目介绍和功能特性
- 安装和使用说明
- 工具列表和参数说明
- 配置示例
- 故障排除指南

### **代码注释**
- 所有公共方法添加 JSDoc 注释
- 复杂逻辑添加行内注释
- 类型定义添加说明注释

## 🎉 **完成标准**

项目完成后应该能够：

1. ✅ **成功构建**: `pnpm build` 无错误
2. ✅ **类型检查**: `pnpm type-check` 通过
3. ✅ **启动运行**: `pnpm start` 正常启动
4. ✅ **工具注册**: 所有10个工具正确注册
5. ✅ **API 调用**: 能够成功调用 FYFC 和 OSS API
6. ✅ **错误处理**: 网络错误和参数错误正确处理
7. ✅ **文档完整**: README 和代码注释完整

## 🚀 **开始创建**

请按照以上要求创建完整的 FYFC MCP 服务器项目。如果有任何疑问或需要澄清的地方，请随时询问。

**重要提示**: 
- 确保所有文件使用 UTF-8 编码
- 遵循 TypeScript 严格模式
- 使用现代 ES 语法特性
- 保持代码整洁和可维护性
