package cn.fyg.schedule.service.news.awards.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.news.awards.FPersonalAwardsR;
import cn.fyg.schedule.pojo.news.awads.FPersonalAwards;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.news.awards.FPersonalAwardsS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class FPersonalAwardsSI implements FPersonalAwardsS {
    private final FPersonalAwardsR r;

    public FPersonalAwardsSI(FPersonalAwardsR r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FPersonalAwards data) {
        BaseResponse response = BaseService.save(data, r, FPersonalAwards.class);
        // 如果保存成功，预加载懒加载的集合，避免序列化时的LazyInitializationException
        if (response.getCode() == 0 && response.getResult() instanceof FPersonalAwards) {
            FPersonalAwards savedAward = (FPersonalAwards) response.getResult();
            preloadLazyCollections(savedAward);
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse findById(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Optional<FPersonalAwards> data = r.findById(id);
            if (data.isPresent()) {
                FPersonalAwards award = data.get();
                // 预加载懒加载的items集合，避免LazyInitializationException
                preloadLazyCollections(award);
                baseResponse.setResult(award);
            }
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse getCompanyHistoryList() {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<String> data = r.getCompanyHistoryList();
            baseResponse.setResult(data);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse listAll() {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FPersonalAwards> data = r.findAllByOrderByAwardDateDesc();
            // 预加载懒加载的items集合，避免LazyInitializationException
            data.forEach(this::preloadLazyCollections);
            baseResponse.setResult(data);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse listByAwardDate(Date begin, Date end) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FPersonalAwards> data = r.findByAwardDateBetweenOrderByAwardDateDesc(begin, end);
            // 预加载懒加载的items集合，避免LazyInitializationException
            data.forEach(this::preloadLazyCollections);
            baseResponse.setResult(data);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse listCompanyIn(List<String> companies) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FPersonalAwards> data = companies.size() > 0?r.findAllByCompanyInOrderByAwardDateDesc(companies):r.findAllByOrderByAwardDateDesc();
            // 预加载懒加载的items集合，避免LazyInitializationException
            data.forEach(this::preloadLazyCollections);
            baseResponse.setResult(data);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    /**
     * 预加载懒加载的集合，避免LazyInitializationException
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    private void preloadLazyCollections(FPersonalAwards award) {
        if (award != null) {
            // 触发懒加载 - 我们只需要触发加载，不需要使用返回值
            if (award.getItems() != null) {
                award.getItems().size(); // 触发懒加载
            }
        }
    }
}
