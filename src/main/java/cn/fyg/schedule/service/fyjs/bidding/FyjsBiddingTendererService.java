package cn.fyg.schedule.service.fyjs.bidding;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.fyjs.bidding.FyjsBiddingTendererDto;

public interface FyjsBiddingTendererService {
    BaseResponse save(FyjsBiddingTendererDto dto);
    BaseResponse delete(Integer id);
    BaseResponse findById(Integer id);
    BaseResponse findByParentId(Integer parentId);
    BaseResponse findByParentIdAndTendererId(Integer parentId, String tendererId);
    BaseResponse deleteByParentIdAndTendererId(Integer parentId, String tendererId);

    BaseResponse findByParentIdAndTendererIdAndWinner(Integer parentId, String tendererId, Integer winner);
}
