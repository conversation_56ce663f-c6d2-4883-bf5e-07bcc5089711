package cn.fyg.schedule.service.sheets.xincai.notification.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.xincai.notification.XcNotiRecordItemRepository;
import cn.fyg.schedule.pojo.project.xincai.noti.XcNotiRecordItem;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.sheets.xincai.notification.XcNotiRecordItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class XcNotiRecordItemServiceImpl implements XcNotiRecordItemService {
    private final XcNotiRecordItemRepository r;

    public XcNotiRecordItemServiceImpl(XcNotiRecordItemRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(XcNotiRecordItem data) {
        return BaseService.save(data, r, XcNotiRecordItem.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        XcNotiRecordItem data = getDataById(id);
        if (data != null) {
            response.setResult(data);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    private XcNotiRecordItem getDataById(Integer id) {
        try {
            Optional<XcNotiRecordItem> optional = r.findById(id);
            if (optional.isPresent()) {
                return optional.get();
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    @Override
    public BaseResponse findByParentId(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        List<XcNotiRecordItem> data = getItems(id);
        if (data != null) {
            response.setResult(data);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    private List<XcNotiRecordItem> getItems(Integer id) {
        try {
            List<XcNotiRecordItem> items = r.findByParentIdOrderByCreateDateDesc(id);
            if (!items.isEmpty()) {
                return items;
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }
}
