package cn.fyg.schedule.service.fg.approval.document;

import cn.fyg.schedule.pojo.dto.fy.approval.document.DocumentApprovalDTO;
import cn.fyg.schedule.pojo.dto.fy.approval.document.DocumentApprovalItemDTO;
import cn.fyg.schedule.pojo.fg.approval.document.FgDocumentApproval;
import cn.fyg.schedule.pojo.fg.approval.document.FgDocumentApprovalItem;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;
@Service
public class FgDocumentApprovalConvertService {
    public DocumentApprovalDTO convertToDTO(FgDocumentApproval data) {
        List<DocumentApprovalItemDTO> itemDTOs = data.getItems().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        return DocumentApprovalDTO.convertToDTO(data, itemDTOs);
    }

    public DocumentApprovalItemDTO convertToDTO(FgDocumentApprovalItem data) {
        return DocumentApprovalItemDTO.convertToDTO(data);
    }
}
