package cn.fyg.schedule.controller.functions;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.service.OSSService;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("oss")
public class OSSSController {
    @Autowired
    private OSSService oss;
    @PostMapping("files")
    public BaseResponse uploadFiles(HttpServletRequest request, @RequestParam("file") MultipartFile files) {
        return oss.upload(files, request.getParameter("bucket"),
                Integer.valueOf(request.getParameter("id")),
                request.getParameter("type"),
                request.getParameter("schema"));
    }

    @PostMapping("upload")
    public BaseResponse uploadFile(HttpServletRequest request, @RequestParam("file") MultipartFile file) {
        return oss.upload(file, request.getParameter("bucket"));
    }

    @PostMapping("delete")
    public BaseResponse delete(String bucket, String key) {
        return oss.deleteObject(key, bucket);
    }

    @PostMapping("get_url")
    public BaseResponse getUrl(String bucket, String objectName, Integer period) {
        return oss.getObjectUrl(bucket, objectName, period);
    }
}
