package cn.fyg.schedule.service.fyjs.staff;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.fyjs.staff.FyjsStaffBaseInfoDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;

public interface FyjsStaffBaseInfoService {
    BaseResponse save(FyjsStaffBaseInfoDto dto);
    BaseResponse delete(FyjsStaffBaseInfoDto dto);
    BaseResponse findById(Integer id);
    BaseResponse filter(Pagination pagination, FyjsStaffBaseInfoDto dto);
    BaseResponse findByIdCardAndStatus(String idCard, Integer dataStatus);
    BaseResponse updateAvatar(Integer id, String avatar);
}
