package cn.fyg.schedule.specification;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.function.BiFunction;

public class BaseSpecification {
    /**
     * Creates an equals specification for any entity attribute to compare it against a given value.
     * @param <T> The type of the entity to which the specification will apply.
     * @param <V> The type of the value used for comparison.
     * @param attribute the entity attribute to perform the equals operation on
     * @param value the value to compare the attribute against
     * @return a specification for the equals operation
     */
    public static <T, V> Specification<T> equals(String attribute, V value) {
        return (Root<T> root, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
            if (value == null) {
                return cb.isNull(root.get(attribute));  // Generate condition for null if value is null
            }
            return cb.equal(root.get(attribute), value);  // Generate equality condition
        };
    }

    /**
     * Creates a LIKE specification for any entity attribute where the search string can be matched in a case-insensitive manner.
     * @param <T> The type of the entity to which the specification will apply.
     * @param attribute the entity attribute to perform the LIKE operation on
     * @param value the search string to use in the LIKE operation
     * @return a specification for the LIKE operation, or null if the input value is null or empty
     */
    public static <T> Specification<T> like(String attribute, String value) {
        return (Root<T> root, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
            if (value == null || value.isEmpty()) {
                return null;  // Return no predicate if the input value is invalid
            }
            String pattern = "%" + value.toLowerCase() + "%";  // Prepare the LIKE pattern
            return cb.like(cb.lower(root.get(attribute)), pattern);  // Perform a case-insensitive LIKE operation
        };
    }

    /**
     * Creates a specification used to check if the attribute's value is within the specified values.
     * @param attribute the attribute of the entity to check
     * @param values the values to check against
     * @return the specification
     */
    public static <T> Specification<T> isIn(String attribute, Object... values) {
        return (Root<T> root, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
            if (values == null || values.length == 0) {
                return cb.disjunction();  // Returns false predicate if values are empty
            }
            CriteriaBuilder.In<Object> in = cb.in(root.get(attribute));
            for (Object value : values) {
                in.value(value);  // Add each value to the in clause
            }
            return in;
        };
    }

    /**
     * Creates a specification for filtering between two double numbers, handling cases where either number can be null.
     * @param attribute the entity attribute to filter on
     * @param min the minimum value of the range (inclusive), or null
     * @param max the maximum value of the range (inclusive), or null
     * @return a specification for the given range
     */
    public static <T> Specification<T> byRange(String attribute, Double min, Double max) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (min != null && max != null) {
                // Both bounds are provided, use between
                predicates.add(cb.between(root.get(attribute), min, max));
            } else if (min != null) {
                // Only the minimum is provided, use greater than or equal
                predicates.add(cb.greaterThanOrEqualTo(root.get(attribute), min));
            } else if (max != null) {
                // Only the maximum is provided, use less than or equal
                predicates.add(cb.lessThanOrEqualTo(root.get(attribute), max));
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * Creates a specification for filtering between two timestamps, handling cases where either timestamp can be null.
     * @param attribute the entity attribute to filter on
     * @param startTime the minimum value of the timestamp range (inclusive), or null
     * @param endTime the maximum value of the timestamp range (inclusive), or null
     * @return a specification for the given range
     */
    public static <T> Specification<T> byTimestampRange(String attribute, Long startTime, Long endTime) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (startTime != null && endTime != null) {
                // Both timestamps are provided, use between
                predicates.add(cb.between(root.get(attribute), new java.util.Date(startTime), new java.util.Date(endTime)));
            } else if (startTime != null) {
                // Only the start time is provided, use greater than or equal
                predicates.add(cb.greaterThanOrEqualTo(root.get(attribute), new java.util.Date(startTime)));
            } else if (endTime != null) {
                // Only the end time is provided, use less than or equal
                predicates.add(cb.lessThanOrEqualTo(root.get(attribute), new java.util.Date(endTime)));
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }

    public static <T, V> Specification<T> addSpecification(Specification<T> spec, String attribute, V value, BiFunction<String, V, Specification<T>> specFunction) {
        if (value != null && !(value instanceof String && StrUtil.isEmpty((String) value))) {
            spec = spec.and(specFunction.apply(attribute, value));
        }
        return spec;
    }

    public static <T, V, E extends Enum<E>> Specification<T> addSpecification(
            Specification<T> spec,
            String attribute,
            V value,
            Class<E> enumClass,
            BiFunction<String, V, Specification<T>> specFunction) {
        if (value != null && !(value instanceof String && StrUtil.isEmpty((String) value))) {
            spec = spec.and(specFunction.apply(attribute, value));
        }
        return spec;
    }



    public static <T> Specification<T> addSpecificationFromJson(Specification<T> spec, String attribute, String jsonValues, BiFunction<String, String, Specification<T>> jsonSpecFunction) {
        if (StrUtil.isNotEmpty(jsonValues)) {
            spec = spec.and(jsonSpecFunction.apply(attribute, jsonValues));
        }
        return spec;
    }

    // ==================== 使用 Hutool 增强的新方法 ====================

    /**
     * 使用 Hutool 的字符串工具创建模糊查询 Specification
     */
    public static <T> Specification<T> likeIgnoreCase(String attribute, String value) {
        return (Root<T> root, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
            if (StrUtil.isBlank(value)) {
                return cb.conjunction(); // 返回 true 条件，不影响查询
            }
            String pattern = "%" + value.toLowerCase() + "%";
            return cb.like(cb.lower(root.get(attribute)), pattern);
        };
    }

    /**
     * 使用 Hutool 的集合工具创建 IN 查询 Specification
     */
    public static <T> Specification<T> inCollection(String attribute, Collection<?> values) {
        return (Root<T> root, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
            if (CollUtil.isEmpty(values)) {
                return cb.disjunction(); // 返回 false 条件
            }
            return root.get(attribute).in(values);
        };
    }

    /**
     * 使用 Hutool 的数字工具创建数值范围查询 Specification
     */
    public static <T> Specification<T> numberRange(String attribute, Number min, Number max) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (ObjectUtil.isNotNull(min)) {
                predicates.add(cb.ge(root.get(attribute), min));
            }
            if (ObjectUtil.isNotNull(max)) {
                predicates.add(cb.le(root.get(attribute), max));
            }
            if (predicates.isEmpty()) {
                return cb.conjunction(); // 返回 true 条件
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * 使用 Hutool 的日期工具创建日期范围查询 Specification
     */
    public static <T> Specification<T> dateRange(String attribute, Date startDate, Date endDate) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (ObjectUtil.isNotNull(startDate)) {
                predicates.add(cb.greaterThanOrEqualTo(root.get(attribute), startDate));
            }
            if (ObjectUtil.isNotNull(endDate)) {
                // 使用 Hutool 将结束日期设置为当天的最后一秒
                Date endOfDay = DateUtil.endOfDay(endDate);
                predicates.add(cb.lessThanOrEqualTo(root.get(attribute), endOfDay));
            }
            if (predicates.isEmpty()) {
                return cb.conjunction(); // 返回 true 条件
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * 使用 Hutool 的对象工具创建非空查询 Specification
     */
    public static <T> Specification<T> isNotNull(String attribute) {
        return (Root<T> root, CriteriaQuery<?> query, CriteriaBuilder cb) ->
            cb.isNotNull(root.get(attribute));
    }

    /**
     * 使用 Hutool 的对象工具创建空值查询 Specification
     */
    public static <T> Specification<T> isNull(String attribute) {
        return (Root<T> root, CriteriaQuery<?> query, CriteriaBuilder cb) ->
            cb.isNull(root.get(attribute));
    }

    /**
     * 使用 Hutool 增强的条件添加方法
     */
    public static <T, V> Specification<T> addSpecificationIfNotEmpty(
            Specification<T> spec,
            String attribute,
            V value,
            BiFunction<String, V, Specification<T>> specFunction) {
        if (ObjectUtil.isNotNull(value)) {
            if (value instanceof String && StrUtil.isNotBlank((String) value)) {
                Specification<T> newSpec = specFunction.apply(attribute, value);
                if (ObjectUtil.isNotNull(newSpec)) {
                    spec = ObjectUtil.isNull(spec) ? newSpec : spec.and(newSpec);
                }
            } else if (!(value instanceof String)) {
                Specification<T> newSpec = specFunction.apply(attribute, value);
                if (ObjectUtil.isNotNull(newSpec)) {
                    spec = ObjectUtil.isNull(spec) ? newSpec : spec.and(newSpec);
                }
            }
        }
        return spec;
    }

    /**
     * 使用 Hutool 的集合工具添加集合条件
     */
    public static <T> Specification<T> addSpecificationIfNotEmpty(
            Specification<T> spec,
            String attribute,
            Collection<?> values) {
        if (CollUtil.isNotEmpty(values)) {
            Specification<T> newSpec = inCollection(attribute, values);
            if (ObjectUtil.isNotNull(newSpec)) {
                spec = ObjectUtil.isNull(spec) ? newSpec : spec.and(newSpec);
            }
        }
        return spec;
    }
}
