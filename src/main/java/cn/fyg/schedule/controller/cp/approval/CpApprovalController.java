package cn.fyg.schedule.controller.cp.approval;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.cp.approval.CpApplyInfoDto;
import cn.fyg.schedule.pojo.dto.cp.approval.CpApprovalTemplateDto;
import cn.fyg.schedule.service.cp.approval.CpApprovalTemplateService;
import cn.fyg.schedule.utils.GsonUtil;
import org.apache.bcel.generic.BALOAD;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("cp/approval")
public class CpApprovalController {
    private final CpApprovalTemplateService service;

    public CpApprovalController(CpApprovalTemplateService service) {
        this.service = service;
    }

    @PostMapping("save")
    public BaseResponse save(CpApprovalTemplateDto dto) {
        return service.save(dto);
    }

    @PostMapping("filter")
    public BaseResponse filter(CpApprovalTemplateDto dto) {
        return service.filter(dto);
    }

    @PostMapping("exist")
    public BaseResponse ifExist(String templateId, Integer dataStatus) {
        return service.findByTemplateIdAndDataStatus(templateId, dataStatus);
    }

    @PostMapping("api/template/id")
    public BaseResponse getApiByTemplateId(String templateId) {
        return service.getApiByTemplateId(templateId);
    }

    @PostMapping("api/test")
    public BaseResponse getTestResponse(String templateId, String contents) {
       BaseResponse response = BaseResponse.initialize();
       CpApplyInfoDto cpApplyInfoDto = new CpApplyInfoDto();
       cpApplyInfoDto.setTemplateId(templateId);
       cpApplyInfoDto.setContents(GsonUtil.fromJsonList(contents, CpApplyInfoDto.Control.class));

       response.setResult(cpApplyInfoDto);
       return response;
    }

    @PostMapping("find/id")
    public BaseResponse findById(Integer id) {
        return service.findById(id);
    }
}
