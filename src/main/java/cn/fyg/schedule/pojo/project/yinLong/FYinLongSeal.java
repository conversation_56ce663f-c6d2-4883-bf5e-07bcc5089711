package cn.fyg.schedule.pojo.project.yinLong;

import cn.fyg.schedule.pojo.dto.project.yinLong.FYinLongSealDto;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Setter
@Getter
@ToString
@Table(name = "f_yinlong_seal")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class FYinLongSeal {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Column(name = "number")
    private String number;
    @Column(name = "creator")
    private String creator;
    @Column(name = "department_name")
    private String departmentName;
    @Column(name = "seal_type")
    private String sealType;
    @Column(name = "contacts")
    private String contacts;
    @Column(name = "phone_number")
    private String phoneNumber;
    @Column(name = "amount")
    private BigDecimal amount;
    @Column(name = "audit_dept")
    private String auditDept;
    @Column(name = "seal_content")
    private String sealContent;
    @Column(name = "transaction_date")
    @Temporal(TemporalType.DATE)
    private Date transactionDate;
    @Column(name = "create_date")
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @Column(name = "update_date")
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "seal_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    @ToString.Exclude
    List<FYinLongSignature> signatureList = new ArrayList<>();

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "seal_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    @ToString.Exclude
    List<FYinLongItem> items = new ArrayList<>();

    public static FYinLongSeal initialize(FYinLongSealDto dto) {
        FYinLongSeal data = new FYinLongSeal();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        data.setNumber(dto.getNumber());
        data.setDepartmentName(dto.getDepartmentName());
        data.setSealType(dto.getSealType());
        data.setContacts(dto.getContacts());
        data.setPhoneNumber(dto.getPhoneNumber());
        data.setAmount(dto.getAmount());
        data.setAuditDept(dto.getAuditDept());
        data.setSealContent(dto.getSealContent());
        data.setSealType(dto.getSealType());
        data.setCreator(dto.getCreator());
        if (dto.getTransactionDate() != null) {
            data.setTransactionDate(DateUtil.date(dto.getTransactionDate()));
        }
        if (dto.getCreateDate() != null) {
            data.setCreateDate(DateUtil.date(dto.getCreateDate()));
        }
        return data;
    }
}
