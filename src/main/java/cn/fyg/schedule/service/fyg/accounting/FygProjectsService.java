package cn.fyg.schedule.service.fyg.accounting;

import cn.fyg.schedule.pojo.dto.IntegerOptionObj;
import cn.fyg.schedule.pojo.dto.fyg.accounting.projects.ProjectDTO;
import cn.fyg.schedule.pojo.dto.fyg.accounting.projects.ProjectQuery;
import cn.fyg.schedule.reponse.BaseResponse;
import org.springframework.data.domain.Page;

import java.util.List;

public interface FygProjectsService extends FygBaseService<ProjectDTO, ProjectDTO, Page<ProjectDTO>, ProjectQuery> {
    BaseResponse<List<IntegerOptionObj>> listOptions(Integer dataStatus);
}
