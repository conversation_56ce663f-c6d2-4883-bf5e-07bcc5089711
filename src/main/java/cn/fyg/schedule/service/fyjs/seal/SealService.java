package cn.fyg.schedule.service.fyjs.seal;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.project.fyjs.sealManagement.FJsSealManagementDto;
import cn.fyg.schedule.pojo.project.fyjs.sealManagement.FJsSealManagement;

public interface SealService {
    BaseResponse save(FJsSealManagement data);
    BaseResponse delete(Integer id);
    BaseResponse findById(Integer id);
    BaseResponse filter(FJsSealManagementDto dto, Integer currentPage, Integer pageSize,
                        String order, String columnKey);
}
