package cn.fyg.schedule.service.core.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.core.FHistoryRepository;
import cn.fyg.schedule.pojo.core.FHistory;
import cn.fyg.schedule.service.core.FHistoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class FHistoryServiceImpl implements FHistoryService {
    private final FHistoryRepository r;

    public FHistoryServiceImpl(FHistoryRepository r) {
        this.r = r;
    }

    @Override
    public BaseResponse findHistory(String id, String name) {
        BaseResponse response = BaseResponse.initialize();
        List<FHistory> list = listHistory(id, name);
        if (list != null) {
            response.setResult(list);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    private List<FHistory> listHistory(String id, String name) {
        try {
            return r.findByHistoryIdAndTableNameOrderByCreateDateDesc(id, name);
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }
}
