package cn.fyg.schedule.service.sheets.xincai.progressCheck.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.xincai.progressCheck.XcProgressCheckRepository;
import cn.fyg.schedule.pojo.dto.project.yx.progressCheck.YxProgressCheckDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.pojo.project.xincai.progressCheck.XcProgressCheck;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.sheets.xincai.progressCheck.XcProgressCheckService;
import cn.fyg.schedule.specification.xincai.XcProgressCheckSpecification;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class XcProgressCheckServiceImpl implements XcProgressCheckService {
    private final XcProgressCheckRepository r;

    public XcProgressCheckServiceImpl(XcProgressCheckRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse save(YxProgressCheckDto dto) {
        BaseResponse response = BaseResponse.initialize();
        try {
            XcProgressCheck data = XcProgressCheck.initialize(dto);
            // 在事务内保存并立即预加载
            XcProgressCheck savedData = r.save(data);
            preloadLazyCollections(savedData);
            response.setResult(savedData);
        } catch (Exception e) {
            log.error(e.getMessage());
            response.setCode(1);
            response.setMsg("保存失败");
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        XcProgressCheck data = getById(id);
        if (data != null) {
            response.setResult(data);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    @Override
    public BaseResponse filter(YxProgressCheckDto dto, Pagination pagination) {
        BaseResponse response = BaseResponse.initialize();
        try {
            Sort sort = Sort.unsorted();
            if (!StrUtil.isEmpty(pagination.getColumnKey())) {
                if (!pagination.getOrder().equals("false")) {
                    sort = Sort.by(pagination.getOrder().equals("ascend") ? Sort.Direction.ASC : Sort.Direction.DESC, pagination.getColumnKey());
                }
            }
            Pageable pageable = PageRequest.of(pagination.getCurrentPage(), pagination.getPageSize(), sort);
            Specification<XcProgressCheck> specification = getSpecification(dto);
            Page<XcProgressCheck> data = r.findAll(specification, pageable);
            response.setResult(data);
        } catch (Exception e) {
            log.info(e.getMessage());
            response.setCode(1);
            response.setMsg("failed");
        }
        return response;
    }

    private Specification<XcProgressCheck> getSpecification(YxProgressCheckDto dto) {
        Specification<XcProgressCheck> specification = Specification.where(dto.getEventStatus() == null ? null : XcProgressCheckSpecification.ifEq("eventStatus", dto.getEventStatus()))
                .and(dto.getDataStatus() == null ? null : XcProgressCheckSpecification.ifEq("dataStatus", dto.getDataStatus()))
                .and(StrUtil.isEmpty(dto.getInspectionDepartment()) ? null : XcProgressCheckSpecification.ifContain("inspectionDepartment", dto.getInspectionDepartment()))
                .and(StrUtil.isEmpty(dto.getCreatorName()) ? null : XcProgressCheckSpecification.ifContain("creatorName", dto.getCreatorName()))
                .and(StrUtil.isEmpty(dto.getYxProjectName()) ? null : XcProgressCheckSpecification.ifContain("yxProjectName", dto.getYxProjectName()))
                .and(StrUtil.isEmpty(dto.getYxProblemDescription()) ? null : XcProgressCheckSpecification.ifContain("yxProblemDescription", dto.getYxProblemDescription()))
                .and(dto.getInspectionDate() == null ? null : XcProgressCheckSpecification.ifDateEq("inspectionDate", DateUtil.date(dto.getInspectionDate())));
        return specification;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public XcProgressCheck getById(Integer id) {
        try {
            Optional<XcProgressCheck> optional = r.findById(id);
            if (optional.isPresent()) {
                XcProgressCheck data = optional.get();
                // 预加载懒加载的集合，避免LazyInitializationException
                preloadLazyCollections(data);
                return data;
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    @Override
    public BaseResponse listInspectionDepartmentHistory() {
        BaseResponse response = BaseResponse.initialize();
        try {
            List<String> list = r.getInspectionDepartmentHistory();
            response.setResult(list);
        } catch (Exception e) {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    /**
     * 预加载懒加载的集合，避免LazyInitializationException
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    private void preloadLazyCollections(XcProgressCheck data) {
        if (data != null) {
            // 触发懒加载 - 我们只需要触发加载，不需要使用返回值
            if (data.getItems() != null) {
                data.getItems().size(); // 触发懒加载
            }
            if (data.getSignatures() != null) {
                data.getSignatures().size(); // 触发懒加载
            }
        }
    }
}
