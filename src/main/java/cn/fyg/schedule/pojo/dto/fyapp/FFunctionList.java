package cn.fyg.schedule.pojo.dto.fyapp;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

@Entity
@Setter
@Getter
@ToString
@Table(name = "f_function_list")
public class FFunctionList {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    private String funIcon;
    private String funName;
    private Integer funType;
    private String funTypeName;
    private String funAction;
    private String funOption;

    private String funCreatePage;

    private String funReviewPage;

    private Integer funStatus;

    private Integer funOrder;
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @Column(name = "update_date")
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;
}
