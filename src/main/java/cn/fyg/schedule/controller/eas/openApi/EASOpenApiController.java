package cn.fyg.schedule.controller.eas.openApi;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.service.wsvoucher.WSVoucherInfo;
import cn.fyg.schedule.service.wsvoucher.WSVoucherService;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("eas/open/api")
public class EASOpenApiController {
    private final WSVoucherService wsVoucherService;

    public EASOpenApiController(WSVoucherService wsVoucherService) {
        this.wsVoucherService = wsVoucherService;
    }

    @PutMapping("voucher/import")
    public BaseResponse voucherImport(WSVoucherInfo voucherInfo) {
       return wsVoucherService.importVoucher(voucherInfo, true, true, false);
    }
}
