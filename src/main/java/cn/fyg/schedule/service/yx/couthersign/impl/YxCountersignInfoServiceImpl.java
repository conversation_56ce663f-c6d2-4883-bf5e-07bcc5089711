package cn.fyg.schedule.service.yx.couthersign.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.yx.countersign.YxCountersignInfoRepository;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.pojo.dto.yx.countersign.*;
import cn.fyg.schedule.pojo.yx.countersign.*;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.yx.couthersign.*;
import cn.fyg.schedule.specification.yx.YxCountersignSpecification;
import cn.fyg.schedule.utils.GsonUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class YxCountersignInfoServiceImpl implements YxCounterSignInfoService {
    private final static Integer PROGRESS_END = 1;
    private final YxCountersignInfoRepository r;
    private final YxDepartmentFunctionService yxDepartmentFunctionService;
    private final YxCountersignDetailService yxCountersignDetailService;
    private final YxCountersignConvertService convertService;

    public YxCountersignInfoServiceImpl(YxCountersignInfoRepository r, YxDepartmentFunctionService yxDepartmentFunctionService, YxCountersignDetailService yxCountersignDetailService, YxFunctionalDepartmentService yxFunctionalDepartmentService, YxCountersignConvertService convertService) {
        this.r = r;
        this.yxDepartmentFunctionService = yxDepartmentFunctionService;
        this.yxCountersignDetailService = yxCountersignDetailService;
        this.convertService = convertService;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(YxCountersignInfoDto dto) {
        BaseResponse response = BaseResponse.initialize();
        try {
            Integer count = 0;
            YxCountersignInfo data = YxCountersignInfo.initialized(dto);
            YxCountersignInfo result = BaseService.saveWithR(data, r, YxCountersignInfo.class);
            response.setResult(result);
            if (!StrUtil.isEmpty(dto.getAuditorListStr())) {
                if (dto.getId() == null) {
                    count = initialize(result.getId(), dto.getAuditorListStr());
                } else {
                    count = update(dto.getId(), dto.getAuditorListStr());
                }
            }
            response.setMsg("操作" + count + "条数据");
        } catch (MyException e) {
            BaseService.setResponse(response, e.getCode(), e.getErrMsg());
        }
        return response;
    }
    private Integer update(Integer id, String auditors) {
        Integer count = 0;
        List<YxAuditor> auditorList = GsonUtil.fromJsonList(auditors, YxAuditor.class);
        for (YxAuditor auditor : auditorList) {
            if (!StrUtil.isEmpty(auditor.getAuditor())) {
                count += update(id, auditor);
            }

        }
        return count;
    }
    private Integer update(Integer id, YxAuditor auditor) {

        try {
            List<Integer> ids = yxDepartmentFunctionService.listDepartmentFunctionId(auditor.getId());
            if (!ids.isEmpty()) {
                int count = 0;
                for (Integer functionId : ids) {
                    Integer num = yxCountersignDetailService.updateAuditor(auditor.getAuditor(), id, functionId);
                    if (num == 0) {
                        count += initialize(id, auditor, functionId);
                    } else {
                        count += num;
                    }
                }
                return count;
            }
        } catch (MyException e) {
            log.error(e.getErrMsg());
        }
        return 0;
    }
    private Integer initialize(Integer id, YxAuditor auditor, Integer functionId) {
        YxCountersignDetailDto dto = new YxCountersignDetailDto();
        dto.setParentId(id);
        dto.setFunctionId(functionId);
        dto.setAuditorId(auditor.getAuditor());
        BaseResponse response = yxCountersignDetailService.save(dto);
        if (response.getCode().equals(0)) {
            return 1;
        }
        return 0;
    }
    private Integer initialize(Integer id, YxAuditor auditor) {

        try {
            Integer count = 0;
            List<YxDepartmentFunction> functions = yxDepartmentFunctionService.findByDepartmentId(auditor.getId());
            for (YxDepartmentFunction function : functions) {
                YxCountersignDetailDto dto = new YxCountersignDetailDto();
                dto.setParentId(id);
                dto.setFunctionId(function.getId());
                dto.setAuditorId(auditor.getAuditor());
                yxCountersignDetailService.save(dto);
                count ++;
            }
            return count;
        } catch (MyException e) {
            log.error(e.getErrMsg());
        }
        return 0;
    }
    private Integer initialize(Integer id, String auditors) {
        Integer count = 0;
        List<YxAuditor> auditorList = GsonUtil.fromJsonList(auditors, YxAuditor.class);
        for (YxAuditor auditor : auditorList) {
            if (!StrUtil.isEmpty(auditor.getAuditor())) {
                count += initialize(id, auditor);
            }
        }
        return count;
        /**
            List<YxFunctionalDepartment> departments = yxFunctionalDepartmentService.findAll();
            for (YxFunctionalDepartment department : departments) {
                List<YxDepartmentFunction> functions = yxDepartmentFunctionService.findByDepartmentId(department.getId());
                for (YxDepartmentFunction function : functions) {
                    YxCountersignDetailDto dto = new YxCountersignDetailDto();
                    YxAuditor auditor = findYxAuditor(department.getId(), auditorList);
                    dto.setParentId(id);
                    dto.setFunctionId(function.getId());
                    dto.setAuditorId(auditor.getAuditor());
                    yxCountersignDetailService.save(dto);
                }
            }
         **/
    }

    private YxAuditor findYxAuditor(Integer id, List<YxAuditor> auditors) {
        return auditors.stream().filter(obj -> Objects.equals(obj.getId(), id)).findFirst().orElse(null);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        try {
            BaseService.deleteWithR(id, r);
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }

    @Override
    public BaseResponse filter(Pagination pagination, YxCountersignInfoDto dto) {
        BaseResponse response = BaseResponse.initialize();
        try {
            Sort sort = Sort.unsorted();
            if (!StrUtil.isEmpty(pagination.getColumnKey())) {
                if (!pagination.getOrder().equals("false")) {
                    sort = Sort.by(pagination.getOrder().equals("ascend") ? Sort.Direction.ASC : Sort.Direction.DESC, pagination.getColumnKey());
                }
            }
            Pageable pageable = PageRequest.of(pagination.getCurrentPage(), pagination.getPageSize(), sort);
            Specification<YxCountersignInfo> specification = getSpecification(dto);
            response.setResult(getFilterData(specification, pageable));
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        } catch (Exception e) {
            response.setCode(1);
            response.setMsg("查询出错");
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse findById(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        try {
            YxCountersignInfo data = BaseService.getById(id, r);
            // 预加载懒加载的items集合，避免转换服务中的LazyInitializationException
            preloadLazyCollections(data);
            CountersignInfoDto dto = convertService.covertToDTO(data);
            response.setResult(dto);
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }

    @Override
    public BaseResponse update(String sign, Integer id) {
        BaseResponse response = BaseResponse.initialize();
        try {
            Integer count = updateSign(sign, id);
            response.setMsg("操作成功，影响数据数量：" + count);
            response.setResult(count);
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }

    @Override
    public BaseResponse update(String sign, String comments, Integer id) {
        BaseResponse response = BaseResponse.initialize();
        try {
            Integer count = updateSign(sign, comments, id);
            response.setMsg("操作成功，影响数据数量：" + count);
            response.setResult(count);
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }

    private Integer updateSign(String sign, String comments, Integer id) throws MyException {
        try {
            return r.updateSign(sign, comments, PROGRESS_END, id);
        } catch (JpaSystemException e) {
            log.info(e.getMessage());
            throw new MyException(500, "操作出错");
        }
    }
    private Integer updateSign(String sign, Integer id) throws MyException {
        try {
            return r.updateSign(sign, PROGRESS_END, id);
        } catch (JpaSystemException e) {
            log.info(e.getMessage());
            throw new MyException(500, "操作出错");
        }
    }

    private Specification<YxCountersignInfo> getSpecification(YxCountersignInfoDto dto) {
        return Specification.where(dto.getDataStatus() == null ? null : YxCountersignSpecification.ifEq("dataStatus", dto.getDataStatus()))
                .and(StrUtil.isEmpty(dto.getProjectName()) ? null : YxCountersignSpecification.ifContain("projectName", dto.getProjectName()))
                .and(StrUtil.isEmpty(dto.getContractor()) ? null : YxCountersignSpecification.ifContain("contractor", dto.getContractor()))
                .and(StrUtil.isEmpty(dto.getConstructionOrganization()) ? null : YxCountersignSpecification.ifContain("constructionOrganization", dto.getConstructionOrganization()))
                ;
    }

    private Page<YxCountersignInfo> getFilterData(Specification<YxCountersignInfo> specification, Pageable pageable) throws MyException {
        try {
            return r.findAll(specification, pageable);
        } catch (JpaSystemException e) {
            log.info(e.getMessage());
            throw new MyException(500, "查询出错");
        }
    }

    /**
     * 预加载懒加载的集合，避免LazyInitializationException
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    private void preloadLazyCollections(YxCountersignInfo info) {
        if (info != null) {
            // 触发懒加载 - 我们只需要触发加载，不需要使用返回值
            if (info.getItems() != null) {
                info.getItems().size(); // 触发懒加载
            }
        }
    }
}
