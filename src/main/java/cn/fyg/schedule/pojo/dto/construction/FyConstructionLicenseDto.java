package cn.fyg.schedule.pojo.dto.construction;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class FyConstructionLicenseDto {
    private Integer id;
    private String serialNumber;
    private Long licenseIssuingDate;
    private String licenseIssuingDateQuery;
    private String licenseIssuingAuthority;
    private String developmentOrganization; //建设单位
    private Integer projectId;
    private String projectName;
    private String constructionAddress;
    private String constructionScale;
    private Long contractBegin;
    private Long contractEnd;
    private String contractSchedule; //合同工期
    private BigDecimal contractAmountNum;
    private String contractAmountNumQuery;
    private String contractAmount; //合同价格
    private String surveyOrganization; //勘察单位
    private String surveyOrgPrincipal; //勘察单位项目负责人
    private String designOrganization; //设计单位
    private String designOrgPrincipal; //设计单位项目负责人
    private String constructionOrganization; //施工单位
    private String constructionOrgPrincipal; //施工单位项目负责人
    private String supervisingOrganization; //监理单位
    private String supervisor; //总监理工程师
    private String constructionMainOrg; //工程总承包单位
    private String constructionMainManager; //工程总承包单位项目经理
    private Integer dataStatus;
    private Integer eventStatus;
    private String creator;
    private String creatorName;
    private String lastEditor;
    private Long createDate;
    private String organization;
    private String remarks;
    private String managingDirector;
    private Long startDate;
    private Long endDate;
    private Long amountMin;
    private Long amountMax;
}
