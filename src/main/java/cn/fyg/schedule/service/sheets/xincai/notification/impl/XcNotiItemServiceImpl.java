package cn.fyg.schedule.service.sheets.xincai.notification.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.xincai.notification.XcNotiItemRepository;
import cn.fyg.schedule.pojo.project.xincai.noti.XcNotiItem;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.sheets.xincai.notification.XcNotiItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class XcNotiItemServiceImpl implements XcNotiItemService {
    private final XcNotiItemRepository r;

    public XcNotiItemServiceImpl(XcNotiItemRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(XcNotiItem data) {
        return BaseService.save(data, r, XcNotiItem.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        XcNotiItem data = getDataById(id);
        if (data != null) {
            response.setResult(data);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    private XcNotiItem getDataById(Integer id) {
        try {
            Optional<XcNotiItem> optional = r.findById(id);
            if (optional.isPresent()) {
                return optional.get();
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    @Override
    public BaseResponse findByParentId(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        List<XcNotiItem> data = getItems(id);
        if (data != null) {
            response.setResult(data);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    private List<XcNotiItem> getItems(Integer id) {
        try {
            List<XcNotiItem> items = r.findByParentIdOrderByCreateDateDesc(id);
            if (!items.isEmpty()) {
                return items;
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }
}
