package cn.fyg.schedule.pojo.construction;

import cn.fyg.schedule.pojo.dto.construction.FBuildingPermitDto;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Setter
@Getter
@ToString
@Table(name = "f_building_permit")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class FBuildingPermit {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    private String company;
    private String number;
    @Column(name = "set_up_date")
    @Temporal(TemporalType.DATE)
    private Date setUpDate;
    private String approvalStatus;
    @Column(name = "approval_date")
    @Temporal(TemporalType.DATE)
    private Date approvalDate;
    private String type;
    private String region;
    private String projectName;
    private String projectNano;
    private String principal;
    @Column(name = "business_manager")
    private String businessManager; //经营负责人（社保） 2024-07-25 8:34
    private Double contractValue;
    private Double coveredArea;
    private String performanceScale;
    private String operationMode;
    private Integer timeLimit;
    @Column(name = "biding_win_date")
    @Temporal(TemporalType.DATE)
    private Date bidingWinDate;
    @Temporal(TemporalType.DATE)
    private Date startingDate;
    @Temporal(TemporalType.DATE)
    private Date completionDate;
    private String projectManager;
    private String municipalManager;
    private String technicalDirector;
    private String qualityInspector;
    private String constructionCrew;
    private String safetySupervisor;
    private String otherMember;
    private String changingInfo;
    private String contacts;
    private String phone;
    @Column(name = "estimated_completion_date")
    @Temporal(TemporalType.DATE)
    private Date estimatedCompletionDate;
    private String constructorCreditTarget;
    private String highQualityTarget;
    private String safetyStandardisation;
    private String noPollutionConstruction;
    private String newTechnology;
    private String intelligence;
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;
    private String creator;

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "building_permit_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    @ToString.Exclude
    private List<FBuildingPermitItem> items = new ArrayList<>();

    public static FBuildingPermit initialize(FBuildingPermitDto dto) {
        FBuildingPermit data = new FBuildingPermit();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        data.setCompany(dto.getCompany());
        data.setCreator(dto.getCreator());
        if (dto.getCreateDate() != null) {
            data.setCreateDate(DateUtil.parse(dto.getCreateDate()));
        }
        data.setNumber(dto.getNumber());
        if (dto.getSetUpDate() != null) {
            data.setSetUpDate(DateUtil.date(dto.getSetUpDate()));
        }
        data.setApprovalStatus(dto.getApprovalStatus());
        if (dto.getApprovalDate() != null) {
            data.setApprovalDate(DateUtil.date(dto.getApprovalDate()));
        }
        data.setType(dto.getType());
        data.setRegion(dto.getRegion());
        data.setProjectName(dto.getProjectName());
        data.setProjectNano(dto.getProjectNano());
        data.setPrincipal(dto.getPrincipal());
        data.setBusinessManager(dto.getBusinessManager());
        data.setContractValue(dto.getContractValue());
        data.setCoveredArea(dto.getCoveredArea());
        data.setPerformanceScale(dto.getPerformanceScale());
        data.setOperationMode(dto.getOperationMode());
        data.setTimeLimit(dto.getTimeLimit());
        if (dto.getBidingWinDate() != null) {
            data.setBidingWinDate(DateUtil.date(dto.getBidingWinDate()));
        }
        if (dto.getStartingDate() != null) {
            data.setStartingDate(DateUtil.date(dto.getStartingDate()));
        }
        if (dto.getCompletionDate() != null) {
            data.setCompletionDate(DateUtil.date(dto.getCompletionDate()));
        }
        data.setProjectManager(dto.getProjectManager());
        data.setMunicipalManager(dto.getMunicipalManager());
        data.setTechnicalDirector(dto.getTechnicalDirector());
        data.setQualityInspector(dto.getQualityInspector());
        data.setConstructionCrew(dto.getConstructionCrew());
        data.setSafetySupervisor(dto.getSafetySupervisor());
        data.setOtherMember(dto.getOtherMember());
        data.setChangingInfo(dto.getChangingInfo());
        data.setContacts(dto.getContacts());
        data.setPhone(dto.getPhone());
        if (dto.getEstimatedCompletionDate() != null) {
            data.setEstimatedCompletionDate(DateUtil.date(dto.getEstimatedCompletionDate()));
        }
        data.setConstructorCreditTarget(dto.getConstructorCreditTarget());
        data.setHighQualityTarget(dto.getHighQualityTarget());
        data.setSafetyStandardisation(dto.getSafetyStandardisation());
        data.setNoPollutionConstruction(dto.getNoPollutionConstruction());
        data.setNewTechnology(dto.getNewTechnology());
        data.setIntelligence(dto.getIntelligence());
        return data;
    }
}
