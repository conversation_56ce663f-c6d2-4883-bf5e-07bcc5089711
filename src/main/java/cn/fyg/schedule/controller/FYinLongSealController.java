package cn.fyg.schedule.controller;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.project.yinLong.FYinLongSealDto;
import cn.fyg.schedule.pojo.project.yinLong.FYinLongItem;
import cn.fyg.schedule.pojo.project.yinLong.FYinLongSeal;
import cn.fyg.schedule.pojo.project.yinLong.FYinLongSignature;
import cn.fyg.schedule.service.sheets.yl.FYinLongSealService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("yl")
public class FYinLongSealController {
    private final FYinLongSealService s;
    public FYinLongSealController(FYinLongSealService s) {
        this.s = s;
    }

    @PostMapping("create/seal")
    public BaseResponse createSeal(FYinLongSealDto dto) {
        FYinLongSeal data = FYinLongSeal.initialize(dto);
        return s.saveSeal(data);
    }

    @PostMapping("delete/seal")
    public BaseResponse deleteSeal(Integer id) {
        return s.deleteSeal(id);
    }

    @PostMapping("list/seal")
    public BaseResponse listAllSeal() {
        return s.listAllSeal();
    }

    @PostMapping("find/seal/id")
    public BaseResponse findSealById(Integer id) {
        return s.findById(id);
    }

    @PostMapping("history/auditdept")
    public BaseResponse getAuditDeptList() {
        return s.getAuditDeptHistoryList();
    }

    @PostMapping("history/departmentname")
    public BaseResponse getDepartmentNameList() {
        return s.getDepartmentNameHistoryList();
    }

    @PostMapping("history/sealtype")
    public BaseResponse getSealTypeList() {
        return s.getSealTypeHistoryList();
    }

    @PostMapping("create/item")
    public BaseResponse createItem(FYinLongItem data) {
        return s.saveItem(data);
    }

    @PostMapping("delete/item")
    public BaseResponse deleteItem(Integer id) {
        return s.deleteItem(id);
    }

    @PostMapping("create/signature")
    public BaseResponse createSignature(FYinLongSignature data) {
        return s.saveSignature(data);
    }

    @PostMapping("delete/signature")
    public BaseResponse deleteSignature(Integer id) {
        return s.deleteSignature(id);
    }
}
