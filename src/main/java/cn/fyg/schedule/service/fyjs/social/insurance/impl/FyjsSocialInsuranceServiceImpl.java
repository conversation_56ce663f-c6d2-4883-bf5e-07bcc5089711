package cn.fyg.schedule.service.fyjs.social.insurance.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.fyjs.social.insurance.FyjsSocialInsuranceRepository;
import cn.fyg.schedule.pojo.dto.fyjs.social.insurance.FyjsSocialInsuranceDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.pojo.fyjs.social.insurance.FyjsSocialInsurance;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.fyjs.social.insurance.FyjsSocialInsuranceService;
import cn.fyg.schedule.specification.fyjs.social.insurance.FyjsSocialInsuranceSpecification;
import cn.fyg.schedule.specification.fyjs.staff.FyjsStaffBaseInfoSpecification;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.Optional;

@Service
@Slf4j
public class FyjsSocialInsuranceServiceImpl implements FyjsSocialInsuranceService {
    private final FyjsSocialInsuranceRepository r;

    public FyjsSocialInsuranceServiceImpl(FyjsSocialInsuranceRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FyjsSocialInsuranceDto dto) {
        FyjsSocialInsurance data = FyjsSocialInsurance.initialized(dto);
        return BaseService.save(data, r, FyjsSocialInsurance.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(FyjsSocialInsuranceDto dto) {
        return null;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse findById(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        FyjsSocialInsurance result = getById(id);
        if (result != null) {
            // 预加载懒加载的集合，避免LazyInitializationException
            preloadLazyCollections(result);
            response.setResult(result);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    private FyjsSocialInsurance getById(Integer id) {
        try {
            Optional<FyjsSocialInsurance> optional = r.findById(id);
            if (optional.isPresent()) {
                return optional.get();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse filter(Pagination pagination, FyjsSocialInsuranceDto dto) {
        BaseResponse response = BaseResponse.initialize();
        try {
            Sort sort = Sort.unsorted();
            if (!StrUtil.isEmpty(pagination.getColumnKey())) {
                if (!pagination.getOrder().equals("false")) {
                    sort = Sort.by(pagination.getOrder().equals("ascend") ? Sort.Direction.ASC : Sort.Direction.DESC, pagination.getColumnKey());
                }
            }
            Pageable pageable = PageRequest.of(pagination.getCurrentPage(), pagination.getPageSize(), sort);
            Specification<FyjsSocialInsurance> specification = getSpecification(dto);
            Page<FyjsSocialInsurance> data = r.findAll(specification, pageable);
            // 预加载懒加载的集合，避免LazyInitializationException
            data.getContent().forEach(this::preloadLazyCollections);
            response.setResult(data);
        } catch (Exception e) {
            log.info(e.getMessage());
            response.setCode(1);
            response.setMsg("查询出错");
        }
        return response;
    }

    private Specification<FyjsSocialInsurance> getSpecification(FyjsSocialInsuranceDto dto) {
        return Specification.where(dto.getDataStatus() == null ? null : FyjsSocialInsuranceSpecification.ifEq("dataStatus", dto.getDataStatus()))
                .and(dto.getId() == null ? null : FyjsSocialInsuranceSpecification.ifEq("id", dto.getId()))
                .and(StrUtil.isEmpty(dto.getCreatorName()) ? null : FyjsSocialInsuranceSpecification.ifContain("creatorName", dto.getCreatorName()))
                .and(StrUtil.isEmpty(dto.getCreator()) ? null : FyjsSocialInsuranceSpecification.ifContain("creator", dto.getCreator()))
                .and(StrUtil.isEmpty(dto.getStaffName()) ? null : FyjsSocialInsuranceSpecification.ifContain("staffName", dto.getStaffName()))
                .and(StrUtil.isEmpty(dto.getStaffIdCard()) ? null : FyjsSocialInsuranceSpecification.ifContain("staffIdCard", dto.getStaffIdCard()))
                .and(dto.getInitialEnrollmentStart() == null && dto.getInitialEnrollmentStop() == null ? null :
                        dto.getInitialEnrollmentStart() != null && dto.getInitialEnrollmentStop() == null ? FyjsSocialInsuranceSpecification.ifDateOverThan("initialEnrollment", DateUtil.date(dto.getInitialEnrollmentStart())) :
                                dto.getInitialEnrollmentStart() == null ? FyjsSocialInsuranceSpecification.ifDateLessThan("initialEnrollment", DateUtil.date(dto.getInitialEnrollmentStop())) :
                        FyjsSocialInsuranceSpecification.ifDateBetween("initialEnrollment", DateUtil.date(dto.getInitialEnrollmentStart()), DateUtil.date(dto.getInitialEnrollmentStop())))
                .and(dto.getInsuranceEndStart() == null && dto.getInsuranceEndStop() == null ? null :
                        dto.getInsuranceEndStart() != null && dto.getInsuranceEndStop() == null ? FyjsSocialInsuranceSpecification.ifDateOverThan("insuranceEnd", DateUtil.date(dto.getInsuranceEndStart())) :
                                dto.getInsuranceEndStart() == null ? FyjsSocialInsuranceSpecification.ifDateLessThan("insuranceEnd", DateUtil.date(dto.getInsuranceEndStop())) :
                                        FyjsSocialInsuranceSpecification.ifDateBetween("insuranceEnd", DateUtil.date(dto.getInsuranceEndStart()), DateUtil.date(dto.getInsuranceEndStop())))
                .and(dto.getPaymentDeadLineStart() == null && dto.getPaymentDeadLineStop() == null ? null :
                        dto.getPaymentDeadLineStart() != null && dto.getPaymentDeadLineStop() == null ? FyjsSocialInsuranceSpecification.ifDateOverThan("paymentDeadLine", DateUtil.date(dto.getPaymentDeadLineStart())) :
                                dto.getPaymentDeadLineStart() == null ? FyjsSocialInsuranceSpecification.ifDateLessThan("paymentDeadLine", DateUtil.date(dto.getPaymentDeadLineStop())) :
                                        FyjsSocialInsuranceSpecification.ifDateBetween("paymentDeadLine", DateUtil.date(dto.getPaymentDeadLineStart()), DateUtil.date(dto.getPaymentDeadLineStop())))
                .and(StrUtil.isEmpty(dto.getAcademicCareer()) ? null : FyjsSocialInsuranceSpecification.joinTableAndContain("academicCareer", dto.getAcademicCareer()))
                .and(dto.getAgeStart() == null && dto.getAgeStop() == null ? null :
                        dto.getAgeStart() != null && dto.getAgeStop() == null ? FyjsSocialInsuranceSpecification.joinTableAndNumberOverThan("age", dto.getAgeStart()) :
                                dto.getAgeStart() == null ? FyjsSocialInsuranceSpecification.joinTableAndNumberLessThan("age", dto.getAgeStop()) :
                                        FyjsSocialInsuranceSpecification.joinTableAndNumberBetween("age", dto.getAgeStart(), dto.getAgeStop()))
                .and(dto.getAgeInFyStart() == null && dto.getAgeInFyStop() == null ? null :
                        dto.getAgeInFyStart() != null && dto.getAgeInFyStop() == null ? FyjsSocialInsuranceSpecification.joinTableAndNumberOverThan("ageInFy", dto.getAgeInFyStart()) :
                                dto.getAgeStart() == null ? FyjsSocialInsuranceSpecification.joinTableAndNumberLessThan("ageInFy", dto.getAgeInFyStop()) :
                                        FyjsSocialInsuranceSpecification.joinTableAndNumberBetween("ageInFy", dto.getAgeInFyStart(), dto.getAgeInFyStop()))
                .and(StrUtil.isEmpty(dto.getHouseholdRegistration()) ? null : FyjsSocialInsuranceSpecification.joinTableAndContain("householdRegistration", dto.getHouseholdRegistration()))
                .and(StrUtil.isEmpty(dto.getHouseholdRegistrationType()) ? null : FyjsSocialInsuranceSpecification.joinTableAndContain("householdRegistrationType", dto.getHouseholdRegistrationType()))
                .and(StrUtil.isEmpty(dto.getPoliticsStatus()) ? null : FyjsSocialInsuranceSpecification.joinTableAndContain("politicsStatus", dto.getPoliticsStatus()))
                .and(StrUtil.isEmpty(dto.getPhoneNumber()) ? null : FyjsSocialInsuranceSpecification.joinTableAndContain("phoneNumber", dto.getPhoneNumber()))
                .and(StrUtil.isEmpty(dto.getStaffProperty()) ? null : FyjsSocialInsuranceSpecification.joinTableAndContain("staffProperty", dto.getStaffProperty()))
                .and(dto.getGender() == null ? null : FyjsSocialInsuranceSpecification.joinTableAndEq("gender", dto.getGender()))
                .and(dto.getIsOnPost() == null ? null : FyjsSocialInsuranceSpecification.joinTableAndEq("isOnPost", dto.getIsOnPost()))
                .and(StrUtil.isEmpty(dto.getBelongTo()) ? null :FyjsSocialInsuranceSpecification.joinTableAndContain("belongTo", dto.getBelongTo()))
                .and(StrUtil.isEmpty(dto.getStaffRemarks()) ? null : FyjsSocialInsuranceSpecification.joinTableAndContain("staffRemarks", dto.getStaffRemarks()))
                .and(dto.getBirthStart() == null && dto.getBirthStop() == null ? null :
                        dto.getBirthStart() != null && dto.getBirthStop() == null ? FyjsSocialInsuranceSpecification.joinTableAndDateOverThan("birth", DateUtil.date(dto.getBirthStart())) :
                                dto.getBirthStart() == null ? FyjsSocialInsuranceSpecification.joinTableAndDateLessThan("birth", DateUtil.date(dto.getBirthStop())) :
                                        FyjsSocialInsuranceSpecification.joinTableAndDateBetween("birth", DateUtil.date(dto.getBirthStart()), DateUtil.date(dto.getBirthStop())))
                .and(dto.getEntryDateStart() == null && dto.getEntryDateStop() == null ? null :
                        dto.getEntryDateStart() != null && dto.getEntryDateStop() == null ? FyjsSocialInsuranceSpecification.joinTableAndDateOverThan("entryDate", DateUtil.date(dto.getEntryDateStart())) :
                                dto.getEntryDateStart() == null ? FyjsSocialInsuranceSpecification.joinTableAndDateLessThan("entryDate", DateUtil.date(dto.getEntryDateStop())) :
                                        FyjsSocialInsuranceSpecification.joinTableAndDateBetween("entryDate", DateUtil.date(dto.getEntryDateStart()), DateUtil.date(dto.getEntryDateStop())))
                .and(dto.getDateBecomeRegularStart() == null && dto.getDateBecomeRegularStop() == null ? null :
                        dto.getDateBecomeRegularStart() != null && dto.getDateBecomeRegularStop() == null ? FyjsSocialInsuranceSpecification.joinTableAndDateOverThan("dateBecomeRegular", DateUtil.date(dto.getDateBecomeRegularStart())) :
                                dto.getDateBecomeRegularStart() == null ? FyjsSocialInsuranceSpecification.joinTableAndDateLessThan("dateBecomeRegular", DateUtil.date(dto.getDateBecomeRegularStop())) :
                                        FyjsSocialInsuranceSpecification.joinTableAndDateBetween("dateBecomeRegular", DateUtil.date(dto.getDateBecomeRegularStart()), DateUtil.date(dto.getDateBecomeRegularStop())))
                .and(StrUtil.isEmpty(dto.getInsuredCompany()) ? null : FyjsSocialInsuranceSpecification.ifContain("insuredCompany", dto.getInsuredCompany()))
                .and(StrUtil.isEmpty(dto.getInsuranceType()) ? null : FyjsSocialInsuranceSpecification.ifContain("insuranceType", dto.getInsuranceType()))
                .and(StrUtil.isEmpty(dto.getContactPerson()) ? null : FyjsSocialInsuranceSpecification.ifContain("contactPerson", dto.getContactPerson()))
                .and(StrUtil.isEmpty(dto.getContactPersonPhone()) ? null : FyjsSocialInsuranceSpecification.ifContain("contactPersonPhone", dto.getContactPersonPhone()))
                .and(dto.getInFullOrNormal() == null ? null : FyjsSocialInsuranceSpecification.ifEq("inFullOrNormal", dto.getInFullOrNormal()))
                .and(dto.getReFundStartDate() == null ? null : FyjsSocialInsuranceSpecification.ifDateEq("reFundStartDate", DateUtil.date(dto.getReFundStartDate())))
                .and(dto.getReFundEndDate() == null ? null : FyjsSocialInsuranceSpecification.ifDateEq("reFundEndDate", DateUtil.date(dto.getReFundEndDate())))
                .and(StrUtil.isEmpty(dto.getTemporaryParticipation()) ? null : FyjsSocialInsuranceSpecification.ifContain("temporaryParticipation", dto.getTemporaryParticipation()))
                .and(StrUtil.isEmpty(dto.getRemarks()) ? null : FyjsSocialInsuranceSpecification.ifContain("remarks", dto.getRemarks()))
                .and(dto.getPaymentBaseStart() == null && dto.getPaymentBaseStop() == null ? null :
                        dto.getPaymentBaseStart() != null && dto.getPaymentBaseStop() == null ? FyjsSocialInsuranceSpecification.ifNumberOverThan("paymentBase", dto.getPaymentBaseStart()) :
                        dto.getPaymentBaseStart() == null ? FyjsSocialInsuranceSpecification.ifNumberLessThan("paymentBase", dto.getPaymentBaseStop()) :
                        FyjsSocialInsuranceSpecification.ifNumberBetween("paymentBase", dto.getPaymentBaseStart(), dto.getPaymentBaseStop()))
                .and(dto.getEndowmentStart() == null && dto.getEndowmentStop() == null ? null :
                        dto.getEndowmentStart() != null && dto.getEndowmentStop() == null ? FyjsSocialInsuranceSpecification.ifNumberOverThan("endowment", dto.getEndowmentStart()) :
                                dto.getEndowmentStart() == null ? FyjsSocialInsuranceSpecification.ifNumberLessThan("endowment", dto.getEndowmentStop()) :
                                        FyjsSocialInsuranceSpecification.ifNumberBetween("endowment", dto.getEndowmentStart(), dto.getEndowmentStop()))
                .and(dto.getMedicareStart() == null && dto.getMedicareStop() == null ? null :
                        dto.getMedicareStart() != null && dto.getMedicareStop() == null ? FyjsSocialInsuranceSpecification.ifNumberOverThan("medicare", dto.getMedicareStart()) :
                                dto.getMedicareStart() == null ? FyjsSocialInsuranceSpecification.ifNumberLessThan("medicare", dto.getMedicareStop()) :
                                        FyjsSocialInsuranceSpecification.ifNumberBetween("medicare", dto.getMedicareStart(), dto.getMedicareStop()))
                .and(dto.getUnemploymentStart() == null && dto.getUnemploymentStop() == null ? null :
                        dto.getUnemploymentStart() != null && dto.getUnemploymentStop() == null ? FyjsSocialInsuranceSpecification.ifNumberOverThan("unemployment", dto.getUnemploymentStart()) :
                                dto.getUnemploymentStart() == null ? FyjsSocialInsuranceSpecification.ifNumberLessThan("unemployment", dto.getUnemploymentStop()) :
                                        FyjsSocialInsuranceSpecification.ifNumberBetween("unemployment", dto.getUnemploymentStart(), dto.getUnemploymentStop()))
                .and(dto.getEmploymentInjuryStart() == null && dto.getEmploymentInjuryStop() == null ? null :
                        dto.getEmploymentInjuryStart() != null && dto.getEmploymentInjuryStop() == null ? FyjsSocialInsuranceSpecification.ifNumberOverThan("employmentInjury", dto.getEmploymentInjuryStart()) :
                                dto.getEmploymentInjuryStart() == null ? FyjsSocialInsuranceSpecification.ifNumberLessThan("employmentInjury", dto.getEmploymentInjuryStop()) :
                                        FyjsSocialInsuranceSpecification.ifNumberBetween("employmentInjury", dto.getEmploymentInjuryStart(), dto.getEmploymentInjuryStop()))
                .and(dto.getBalanceStart() == null && dto.getBalanceStop() == null ? null :
                        dto.getBalanceStart() != null && dto.getBalanceStop() == null ? FyjsSocialInsuranceSpecification.ifNumberOverThan("balance", dto.getBalanceStart()) :
                                dto.getBalanceStart() == null ? FyjsSocialInsuranceSpecification.ifNumberLessThan("balance", dto.getBalanceStop()) :
                                        FyjsSocialInsuranceSpecification.ifNumberBetween("balance", dto.getBalanceStart(), dto.getBalanceStop()))
                .and(dto.getReservedFundStart() == null && dto.getReservedFundStop() == null ? null :
                        dto.getReservedFundStart() != null && dto.getReservedFundStop() == null ? FyjsSocialInsuranceSpecification.ifNumberOverThan("reservedFund", dto.getReservedFundStart()) :
                                dto.getReservedFundStart() == null ? FyjsSocialInsuranceSpecification.ifNumberLessThan("reservedFund", dto.getReservedFundStop()) :
                                        FyjsSocialInsuranceSpecification.ifNumberBetween("reservedFund", dto.getReservedFundStart(), dto.getReservedFundStop()))
                .and(dto.getLabourUnionFeesStart() == null && dto.getLabourUnionFeesStop() == null ? null :
                        dto.getLabourUnionFeesStart() != null && dto.getLabourUnionFeesStop() == null ? FyjsSocialInsuranceSpecification.ifNumberOverThan("labourUnionFees", dto.getLabourUnionFeesStart()) :
                                dto.getLabourUnionFeesStart() == null ? FyjsSocialInsuranceSpecification.ifNumberLessThan("labourUnionFees", dto.getLabourUnionFeesStop()) :
                                        FyjsSocialInsuranceSpecification.ifNumberBetween("labourUnionFees", dto.getLabourUnionFeesStart(), dto.getLabourUnionFeesStop()))
                .and(dto.getAccountReceivableStart() == null && dto.getAccountReceivableStop() == null ? null :
                        dto.getAccountReceivableStart() != null && dto.getAccountReceivableStop() == null ? FyjsSocialInsuranceSpecification.ifNumberOverThan("accountReceivable", dto.getAccountReceivableStart()) :
                                dto.getAccountReceivableStart() == null ? FyjsSocialInsuranceSpecification.ifNumberLessThan("accountReceivable", dto.getAccountReceivableStop()) :
                                        FyjsSocialInsuranceSpecification.ifNumberBetween("accountReceivable", dto.getAccountReceivableStart(), dto.getAccountReceivableStop()));
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse findFirstTop() {
        BaseResponse response = BaseResponse.initialize();
        FyjsSocialInsurance result = getFirstOne();
        if (result != null) {
            // 预加载懒加载的集合，避免LazyInitializationException
            preloadLazyCollections(result);
            response.setResult(result);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    private FyjsSocialInsurance getFirstOne() {
        try {
            Optional<FyjsSocialInsurance> optional = r.findTopByDataStatusOrderByCreateDateDesc(0);
            if (optional.isPresent()) {
                return optional.get();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    /**
     * 预加载懒加载的集合，避免LazyInitializationException
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    private void preloadLazyCollections(FyjsSocialInsurance insurance) {
        if (insurance != null) {
            // 触发懒加载 - 我们只需要触发加载，不需要使用返回值
            if (insurance.getStaffInfo() != null) {
                insurance.getStaffInfo().getId(); // 触发懒加载
            }
            if (insurance.getItems() != null) {
                insurance.getItems().size(); // 触发懒加载
            }
        }
    }
}
