package cn.fyg.schedule.service.fyapp;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.fyapp.FAppRegister;

import java.util.List;

public interface FAppRegisterS {
    BaseResponse save(FAppRegister data);
    BaseResponse findByGroupId(Integer id);
    BaseResponse delete(Integer id);

    BaseResponse deleteByAppIdAndGroupId(Integer appId, Integer groupId);
    BaseResponse deleteByGroupId(Integer groupId);
    List<Integer> getAppIdsByGroupId(Integer id);
}
