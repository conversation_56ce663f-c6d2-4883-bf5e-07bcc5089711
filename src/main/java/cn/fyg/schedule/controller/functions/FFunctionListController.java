package cn.fyg.schedule.controller.functions;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.service.fyapp.FFunctionListS;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("function")
public class FFunctionListController {
    private final FFunctionListS s;
    public FFunctionListController(FFunctionListS s) {
        this.s = s;
    }

    @PostMapping("list")
    public BaseResponse listAllFunction() {
        return s.listAll();
    }
}
