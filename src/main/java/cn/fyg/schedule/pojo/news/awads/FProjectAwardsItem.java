package cn.fyg.schedule.pojo.news.awads;

import cn.fyg.schedule.pojo.dto.news.awards.FAwardsItemDto;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.util.Date;

@Entity
@Setter
@Getter
@ToString
@Table(name = "fprojectawardsitem")
public class FProjectAwardsItem {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Column(name = "award_id")
    private Integer awardId;
    private Integer awardType; // 0 - personal; 1 - company
    private String type;
    private String name;
    private String content;
    @Column(name = "create_date")
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;

    public static FProjectAwardsItem initialize(FAwardsItemDto dto) {
        FProjectAwardsItem data = new FProjectAwardsItem();
        data.setAwardId(dto.getAwardId());
        data.setAwardType(dto.getAwardType());
        data.setType(dto.getType());
        data.setContent(dto.getContent());
        data.setName(dto.getName());
        return data;
    }
}
