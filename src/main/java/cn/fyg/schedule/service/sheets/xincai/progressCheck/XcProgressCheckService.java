package cn.fyg.schedule.service.sheets.xincai.progressCheck;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.project.yx.progressCheck.YxProgressCheckDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.pojo.project.xincai.progressCheck.XcProgressCheck;

public interface XcProgressCheckService {
    BaseResponse save(YxProgressCheckDto dto);
    BaseResponse delete(Integer id);
    BaseResponse findById(Integer id);
    BaseResponse filter(YxProgressCheckDto dto, Pagination pagination);
    XcProgressCheck getById(Integer id);

    BaseResponse listInspectionDepartmentHistory();
}
