package cn.fyg.schedule.enums;

import lombok.Getter;

@Getter
public enum AssignmentStatusEnum {
    DRAFT("草稿", "000", "DRAFT"),
    UNDONE("待办", "001", "UNDONE"),
    DONE("已办","002", "DONE"),
    CANCEL("取消", "003", "CANCEL");
    private final String name;
    private final String number;
    private final String value;
    AssignmentStatusEnum(String name, String number, String value) {
        this.name = name;
        this.number = number;
        this.value = value;
    }
}
