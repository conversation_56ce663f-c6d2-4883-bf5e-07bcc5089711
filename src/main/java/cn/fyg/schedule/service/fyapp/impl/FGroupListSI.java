package cn.fyg.schedule.service.fyapp.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.fyapp.FGroupListRepository;
import cn.fyg.schedule.pojo.fyapp.FGroupList;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.fyapp.FGroupListS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class FGroupListSI implements FGroupListS {
    private final FGroupListRepository r;
    public FGroupListSI(FGroupListRepository r) {
        this.r = r;
    }
    @Override
    public BaseResponse save(FGroupList data) {
        return BaseService.save(data, r, FGroupList.class);
    }

    @Override
    public BaseResponse listAll() {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FGroupList> list = r.findAllByOrderByName();
            baseResponse.setResult(list);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Optional<FGroupList> data = r.findById(id);
            data.ifPresent(baseResponse::setResult);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findByGroupKey(String groupKey) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            {
                Optional<FGroupList> data = r.findByGroupKey(groupKey);
                data.ifPresent(baseResponse::setResult);
            }
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }
}
