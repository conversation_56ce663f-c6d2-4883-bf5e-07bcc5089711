package cn.fyg.schedule.service.sheets.xincai.progressCheck;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.project.yx.progressCheck.YxProgressCheckRecordDto;
import cn.fyg.schedule.pojo.project.xincai.progressCheck.XcProgressCheckRecord;

import java.util.List;

public interface XcProgressCheckRecordService {
    BaseResponse save(YxProgressCheckRecordDto dto);
    BaseResponse delete(Integer id);
    BaseResponse findById(Integer id);
    BaseResponse findByParentId(Integer id);
    List<XcProgressCheckRecord> listRecordByParentId(Integer id, Integer mark);
}
