package cn.fyg.schedule.service.fyapp;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.fyapp.FAppList;

import java.util.List;

public interface FAppListS {
    BaseResponse save(FAppList data);
    BaseResponse listAll();
    BaseResponse findById(Integer id);
    BaseResponse delete(Integer id);

    BaseResponse findByInNotIn(List<Integer> ids);

    BaseResponse findByInIn(List<Integer> ids);
    BaseResponse listByGroupId(Integer groupId);
}
