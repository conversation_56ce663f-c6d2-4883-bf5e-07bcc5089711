package cn.fyg.schedule.controller.yuanxin;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.project.management.FProjectDto;
import cn.fyg.schedule.pojo.dto.project.management.FProjectObjectiveDto;
import cn.fyg.schedule.pojo.dto.project.management.FProjectProgressDto;
import cn.fyg.schedule.pojo.dto.project.management.FProjectStaffDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.pojo.project.management.*;
import cn.fyg.schedule.service.project.management.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("project/management")
public class FProjectManagementController {
    private final FProjectService service;
    private final FProjectItemService itemService;
    private final FProjectObjectiveService objectiveService;
    private final FProjectStaffService staffService;
    private final FProjectProgressService progressService;
    private final FProjectProgressItemService progressItemService;

    public FProjectManagementController(FProjectService service, FProjectItemService itemService, FProjectObjectiveService objectiveService, FProjectStaffService staffService, FProjectProgressService progressService, FProjectProgressItemService progressItemService) {
        this.service = service;
        this.itemService = itemService;
        this.objectiveService = objectiveService;
        this.staffService = staffService;
        this.progressService = progressService;
        this.progressItemService = progressItemService;
    }
    @PostMapping("filter/name")
    public BaseResponse filter(FProjectDto dto) {
        return service.filter(dto);
    }
    @PostMapping("filter")
    public BaseResponse filter(FProjectDto dto, Pagination pagination) {
        return service.filter(dto, pagination);
    }

    @PostMapping("find/id")
    public BaseResponse findById(Integer id) {
        return service.findById(id);
    }

    @PostMapping("find/name")
    public BaseResponse findByName(String name) {
        return service.findOneByName(name);
    }

    @PostMapping("save")
    public BaseResponse saveProject(FProjectDto dto) {
        return service.save(dto);
    }
    @PostMapping("delete")
    public BaseResponse delete(Integer id) {
        return service.delete(id);
    }
    @PostMapping("find/item")
    public BaseResponse findItems(Integer id) {
        return itemService.findByParentId(id);
    }

    @PostMapping("delete/item")
    public BaseResponse deleteItem(Integer id) {
        return itemService.delete(id);
    }

    @PostMapping("save/item")
    public BaseResponse saveItem(FProjectItem data) {
        return itemService.save(data);
    }

    @PostMapping("find/objective")
    public BaseResponse findObjective(Integer id) {
        return objectiveService.findByParentId(id);
    }

    @PostMapping("delete/objective")
    public BaseResponse deleteObjective(Integer id) {
        return objectiveService.delete(id);
    }

    @PostMapping("save/objective")
    public BaseResponse saveObjective(FProjectObjectiveDto dto) {
        return objectiveService.save(dto);
    }

    @PostMapping("find/staff")
    public BaseResponse findStaff(Integer id, Integer isActivated, Integer isSiteAdmin) {
        return staffService.findByParentIdAndIsSiteAdminAndIsActivated(id, isActivated, isSiteAdmin);
    }

    @PostMapping("delete/staff")
    public BaseResponse deleteStaff(Integer id) {
        return staffService.delete(id);
    }

    @PostMapping("save/staff")
    public BaseResponse saveStaff(FProjectStaffDto dto) {
        return staffService.save(dto);
    }

    @PostMapping("find/progress")
    public BaseResponse findProgress(Integer id) {
        return progressService.findByParentId(id);
    }

    @PostMapping("delete/progress")
    public BaseResponse deleteProgress(Integer id) {
        return progressService.delete(id);
    }

    @PostMapping("save/progress")
    public BaseResponse saveProgress(FProjectProgressDto dto) {
        return progressService.save(dto);
    }

    @PostMapping("find/progress/item")
    public BaseResponse findProgressItem(Integer id) {
        return progressItemService.findByParentId(id);
    }

    @PostMapping("delete/progress/item")
    public BaseResponse deleteProgressItem(Integer id) {
        return progressItemService.delete(id);
    }

    @PostMapping("save/progress/item")
    public BaseResponse saveProgressItem(FProjectProgressItem data) {
        return progressItemService.save(data);
    }
}
