package cn.fyg.schedule.pojo.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class FPurchaseDto extends BaseDto{
    private String number;
    private String departmentName;
    private String projectBelong;
    private String applyDate;
    private String storePlace;
    private String feeType;
    private String purposeExpain;
    private List<FPurchaseItemDto> infos;
}
