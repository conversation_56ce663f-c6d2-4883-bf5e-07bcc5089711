package cn.fyg.schedule.service.yx.couthersign;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.pojo.dto.yx.countersign.CountersignInfoDto;
import cn.fyg.schedule.pojo.dto.yx.countersign.YxCountersignInfoDto;
import cn.fyg.schedule.pojo.yx.countersign.YxCountersignInfo;

public interface YxCounterSignInfoService {
    BaseResponse save(YxCountersignInfoDto dto);
    BaseResponse delete(Integer id);
    BaseResponse filter(Pagination pagination, YxCountersignInfoDto dto);
    BaseResponse findById(Integer id);
    BaseResponse update(String sign, Integer id);
    BaseResponse update(String sign, String comments, Integer id);
}
