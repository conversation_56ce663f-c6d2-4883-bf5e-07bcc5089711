package cn.fyg.schedule.pojo.fyg.accounting;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import java.math.BigDecimal;

@Entity
@Setter
@Getter
@ToString
@Table(name = "fyg_projects")
public class FygProjects {
    // 主键ID，自动生成
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    // 公司名称
    @Column(name = "company", length = 120)
    private String company;

    // 项目名称
    @Column(name = "project_name", length = 255)
    private String projectName;

    // 项目位置
    @Column(name = "project_location", length = 255)
    private String projectLocation;

    // 项目类型
    @Column(name = "project_type", length = 100)
    private String projectType;

    // 拿地时间，存储日期
    @Column(name = "land_acquisition_date")
    @Temporal(TemporalType.DATE)
    private Date landAcquisitionDate;

    // 开盘时间，存储日期
    @Column(name = "opening_date")
    @Temporal(TemporalType.DATE)
    private Date openingDate;

    // 竣备时间，存储日期
    @Column(name = "completion_preparation_date")
    @Temporal(TemporalType.DATE)
    private Date completionPreparationDate;

    // 交房时间，存储日期
    @Column(name = "handover_date")
    @Temporal(TemporalType.DATE)
    private Date handoverDate;

    // 土地出让金，存储精确到两位小数的金额
    @Column(name = "land_transfer_fee", precision = 18, scale = 2)
    private BigDecimal landTransferFee;

    // 土地面积
    @Column(name = "land_area", precision = 18, scale = 2)
    private BigDecimal landArea;

    // 总建筑面积
    @Column(name = "total_construction_area", precision = 18, scale = 2)
    private BigDecimal totalConstructionArea;

    // 总投资金额
    @Column(name = "total_investment", precision = 18, scale = 2)
    private BigDecimal totalInvestment;

    // 已投入资金
    @Column(name = "funds_invested", precision = 18, scale = 2)
    private BigDecimal fundsInvested;

    // 项目贷款余额
    @Column(name = "project_loan_balance", precision = 18, scale = 2)
    private BigDecimal projectLoanBalance;

    // 可售面积
    @Column(name = "sellable_area", precision = 18, scale = 2)
    private BigDecimal sellableArea;

    // 已售面积
    @Column(name = "sold_area", precision = 18, scale = 2)
    private BigDecimal soldArea;

    // 预计总货值
    @Column(name = "estimated_total_value", precision = 18, scale = 2)
    private BigDecimal estimatedTotalValue;

    // 销售金额
    @Column(name = "sales_amount", precision = 18, scale = 2)
    private BigDecimal salesAmount;

    // 回购金额
    @Column(name = "buyback_amount", precision = 18, scale = 2)
    private BigDecimal buybackAmount;

    // 已回笼资金
    @Column(name = "funds_recovered", precision = 18, scale = 2)
    private BigDecimal fundsRecovered;

    // 工程进度，百分比表示
    @Column(name = "project_progress")
    private String projectProgress;

    // 可回笼资金
    @Column(name = "recoverable_funds", precision = 18, scale = 2)
    private BigDecimal recoverableFunds;

    // 方远实际股比
    @Column(name = "fangyuan_share_ratio", precision = 5, scale = 2)
    private BigDecimal fangyuanShareRatio;

    // 项目财务负责人
    @Column(name = "accountant", length = 60)
    private String accountant;

    // 数据状态，默认值为1，表示正常
    @Column(name = "data_status", columnDefinition = "tinyint(4) default 1")
    private Integer dataStatus;

    // 创建时间，自动填充为当前时间，只在第一次插入时设置
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at", updatable = false)
    private Date createdAt;

    // 更新时间，自动在每次更新时填充为当前时间
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at")
    private Date updatedAt;

    // 创建者
    @Column(name = "created_by", length = 60)
    private String createdBy;

    // 最后更新者
    @Column(name = "updated_by", length = 60)
    private String updatedBy;
}
