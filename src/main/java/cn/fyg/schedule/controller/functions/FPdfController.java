package cn.fyg.schedule.controller.functions;

import cn.fyg.schedule.config.SysConfig;
import cn.fyg.schedule.service.core.HtmlUrlToPdfConverter;

import org.springframework.core.env.Environment;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.InetAddress;

@RestController
@RequestMapping("pdf")
public class FPdfController {
    @Resource
    Environment environment;

    private final HtmlUrlToPdfConverter htmlUrlToPdfConverter;
    private final SysConfig sysConfig;

    public FPdfController(HtmlUrlToPdfConverter htmlUrlToPdfConverter, SysConfig sysConfig) {
        this.htmlUrlToPdfConverter = htmlUrlToPdfConverter;
        this.sysConfig = sysConfig;
    }

    @GetMapping("/download-pdf")
    public ResponseEntity<ByteArrayResource> downloadPdf() {
        String url = "http://localhost:7001/fyschedule2/preview/test";
        return htmlUrlToPdfConverter.downloadPdf(url);
    }

    @GetMapping("/yx/notification/download-pdf/{id}")
    public ResponseEntity<ByteArrayResource> downloadPdf(@PathVariable Integer id) {
        String url = sysConfig.getServer() + "/preview/yx/notification/" + id;
        return htmlUrlToPdfConverter.downloadPdf(url);
    }

    @GetMapping("/yx/progress/check/download-pdf/{id}")
    public ResponseEntity<ByteArrayResource> downloadPdf4ProgressCheck(@PathVariable Integer id) {
        String url = sysConfig.getServer() + "/preview/yx/progress/check/" + id;
        return htmlUrlToPdfConverter.downloadPdf(url);
    }

    @GetMapping("/xc/notification/download-pdf/{id}")
    public ResponseEntity<ByteArrayResource> downloadPdf4xcNoti(@PathVariable Integer id) {
        String url = sysConfig.getServer() + "/preview/xc/notification/" + id;
        return htmlUrlToPdfConverter.downloadPdf(url);
    }

    @GetMapping("/xc/progress/check/download-pdf/{id}")
    public ResponseEntity<ByteArrayResource> downloadPdf4XcProgressCheck(@PathVariable Integer id) {
        String url = sysConfig.getServer() + "/preview/xc/progress/check/" + id;
        return htmlUrlToPdfConverter.downloadPdf(url);
    }

    @GetMapping("/generate/{id}")
    public void htmlToPdf(HttpServletResponse response, @PathVariable String id) throws IOException {
        String hostIp = InetAddress.getLocalHost().getHostAddress();
        String property = environment.getProperty("server.port");

//        String url = "https://" + hostIp + ":" + property + "/fyschedule2/preview/test";
        String url = "https://weixin.fyg.cn/fyschedule2/preview/test";
        htmlUrlToPdfConverter.htmlToPdf(response, url);
    }

}
