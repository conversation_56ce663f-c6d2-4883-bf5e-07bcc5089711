package cn.fyg.schedule.service.fyg.accounting;

import cn.fyg.schedule.pojo.dto.IntegerOptionObj;
import cn.fyg.schedule.pojo.dto.fyg.accounting.bank.BankAccountDTO;
import cn.fyg.schedule.reponse.BaseResponse;

import java.util.List;

public interface FygBankAccountsService extends FygBaseService<BankAccountDTO, BankAccountDTO, Void, BankAccountDTO> {
    BaseResponse<List<IntegerOptionObj>> listOptions(Integer dataStatus, Integer projectId);
    BaseResponse<List<BankAccountDTO>> listBankAccount(Integer dataStatus, Integer projectId);
}
