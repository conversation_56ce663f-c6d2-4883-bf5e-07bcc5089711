package cn.fyg.schedule.service.core.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.construction.FBuildingPermitRepository;
import cn.fyg.schedule.pojo.construction.FBuildingPermit;
import cn.fyg.schedule.pojo.dto.construction.FBuildingPermitDto;
import cn.fyg.schedule.service.core.FImportService;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class FImportServiceImpl implements FImportService {
    private final FBuildingPermitRepository fBuildingPermitRepository;

    public FImportServiceImpl(FBuildingPermitRepository fBuildingPermitRepository) {
        this.fBuildingPermitRepository = fBuildingPermitRepository;
    }

    @Override
    public BaseResponse excelImport(MultipartFile file, String scheme, String creator, Integer start, Integer headerIndex) {
        BaseResponse response = BaseResponse.initialize();
        try {
            byte [] byteArr=file.getBytes();
            InputStream inputStream = new ByteArrayInputStream(byteArr);
            ExcelReader reader = ExcelUtil.getReader(inputStream, 0);
            response.setResult(saveToDatabase(reader, scheme, creator, start, headerIndex));
        } catch (Exception e) {
            log.info(e.getMessage());
            response.setCode(1);
            response.setMsg(e.getMessage());
        }

        return response;
    }

    @Override
    public BaseResponse excelToMap(MultipartFile file, String scheme, String creator, Integer start, Integer headerIndex) {
        BaseResponse response = BaseResponse.initialize();
        try {
            byte [] byteArr=file.getBytes();
            InputStream inputStream = new ByteArrayInputStream(byteArr);
            ExcelReader reader = ExcelUtil.getReader(inputStream, 0);
            response.setResult(reader.readAll());
        } catch (Exception e) {
            log.info(e.getMessage());
            response.setCode(1);
            response.setMsg(e.getMessage());
        }

        return response;
    }

    private Integer saveToDatabase(ExcelReader reader, String scheme,
                                   String creator, Integer start, Integer headerIndex) {
        Integer count = 0;
        switch (scheme) {
            case "fBuildingPermit":
                List<?> list = reader.read(headerIndex, start, FBuildingPermitDto.class);
                for (Object data: list) {
                    FBuildingPermit tmp = FBuildingPermit.initialize((FBuildingPermitDto) data);
                    try {
                        tmp.setCreator(creator);
                        fBuildingPermitRepository.save(tmp);
                    } catch (Exception e) {
                        log.info(e.getMessage());
                        count++;
                    }
                }
                break;
            case "fPersonalAwards":
                log.info("fPersonalAwards");
                break;
            default:
                log.info("failed");
                break;
        }
        return count;
    }
}
