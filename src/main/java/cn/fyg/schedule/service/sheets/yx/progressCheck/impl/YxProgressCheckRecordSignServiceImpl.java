package cn.fyg.schedule.service.sheets.yx.progressCheck.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.yuanxin.progressCheck.YxProgressCheckRecordSignRepository;
import cn.fyg.schedule.pojo.project.yuanXin.progressCheck.YxProgressCheckRecordSign;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.sheets.yx.progressCheck.YxProgressCheckRecordSignService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;

@Service
@Slf4j
public class YxProgressCheckRecordSignServiceImpl implements YxProgressCheckRecordSignService {
    private final YxProgressCheckRecordSignRepository r;

    public YxProgressCheckRecordSignServiceImpl(YxProgressCheckRecordSignRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(YxProgressCheckRecordSign data) {
        return BaseService.save(data, r, YxProgressCheckRecordSign.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findByParentId(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        List<YxProgressCheckRecordSign> data = getItems(id);
        if (data != null) {
            baseResponse.setResult(data);
        } else {
            baseResponse.setCode(1);
            baseResponse.setMsg("查询失败");
        }
        return baseResponse;
    }

    private List<YxProgressCheckRecordSign> getItems(Integer id) {
        try {
            List<YxProgressCheckRecordSign> items = r.findByParentId(id);
            if (!items.isEmpty()) {
                return items;
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse deleteByParentId(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            r.deleteByParentId(id);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg("发生错误:" + e.getMessage());
        }
        return baseResponse;
    }
}
