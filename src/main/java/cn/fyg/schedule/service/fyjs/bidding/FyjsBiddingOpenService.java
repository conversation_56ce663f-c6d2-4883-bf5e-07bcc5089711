package cn.fyg.schedule.service.fyjs.bidding;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.fyjs.bidding.FyjsBiddingOpenDto;

public interface FyjsBiddingOpenService {
    BaseResponse save(FyjsBiddingOpenDto dto);
    BaseResponse delete(Integer id);
    BaseResponse findByParentId(Integer parentId);

    BaseResponse findByParentIdAndThirdPartyId(Integer parentId, String thirdPartyId);

    BaseResponse deleteByParentIdAndThirdPartyId(Integer parentId, String thirdPartyId);
}
