package cn.fyg.schedule.controller.fyfc.review;

import cn.fyg.schedule.pojo.dto.fyfc.review.FyfcApiResponseDto;
import cn.fyg.schedule.pojo.dto.fyfc.review.FyfcAttachmentDto;
import cn.fyg.schedule.service.fyfc.review.IFyfcOssService;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * FYFC OSS 文件管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/fyfc/oss")
public class FyfcOssController {

    private final IFyfcOssService ossService;

    public FyfcOssController(IFyfcOssService ossService) {
        this.ossService = ossService;
    }

    /**
     * 上传单个文件
     */
    @PostMapping("/upload")
    public FyfcApiResponseDto<FyfcAttachmentDto> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam("evaluationId") Integer evaluationId,
            @RequestParam("uploadBy") String uploadBy,
            @RequestParam(value = "bucketName", required = false) String bucketName) {

        log.info("上传文件请求: fileName={}, evaluationId={}, uploadBy={}, bucket={}",
            file.getOriginalFilename(), evaluationId, uploadBy, bucketName);

        return ossService.uploadFile(file, evaluationId, uploadBy, bucketName);
    }

    /**
     * 批量上传文件
     */
    @PostMapping("/upload/batch")
    public FyfcApiResponseDto<List<FyfcAttachmentDto>> uploadFiles(
            @RequestParam("files") MultipartFile[] files,
            @RequestParam("evaluationId") Integer evaluationId,
            @RequestParam("uploadBy") String uploadBy,
            @RequestParam(value = "bucketName", required = false) String bucketName) {

        log.info("批量上传文件请求: fileCount={}, evaluationId={}, uploadBy={}, bucket={}",
            files.length, evaluationId, uploadBy, bucketName);

        return ossService.uploadFiles(files, evaluationId, uploadBy, bucketName);
    }

    /**
     * 删除文件
     */
    @DeleteMapping("/delete")
    public FyfcApiResponseDto<Boolean> deleteFile(
            @RequestParam("fileKey") String fileKey,
            @RequestParam("evaluationId") Integer evaluationId,
            @RequestParam("operatorName") String operatorName,
            @RequestParam(value = "bucketName", required = false) String bucketName) {

        log.info("删除文件请求: fileKey={}, evaluationId={}, operator={}, bucket={}",
            fileKey, evaluationId, operatorName, bucketName);

        return ossService.deleteFile(fileKey, evaluationId, operatorName, bucketName);
    }

    /**
     * 获取文件下载URL
     */
    @GetMapping("/url")
    public FyfcApiResponseDto<String> getFileUrl(
            @RequestParam("fileKey") String fileKey,
            @RequestParam(value = "expireSeconds", defaultValue = "3600") Integer expireSeconds,
            @RequestParam(value = "bucketName", required = false) String bucketName) {

        log.info("获取文件URL请求: fileKey={}, expireSeconds={}, bucket={}", fileKey, expireSeconds, bucketName);

        return ossService.getFileUrl(fileKey, expireSeconds, bucketName);
    }

    /**
     * 获取评价的所有附件
     */
    @GetMapping("/attachments/{evaluationId}")
    public FyfcApiResponseDto<List<FyfcAttachmentDto>> getEvaluationAttachments(
            @PathVariable("evaluationId") Integer evaluationId) {
        
        log.info("获取评价附件请求: evaluationId={}", evaluationId);
        
        return ossService.getEvaluationAttachments(evaluationId);
    }

    /**
     * 更新评价的附件列表
     */
    @PutMapping("/attachments/{evaluationId}")
    public FyfcApiResponseDto<Boolean> updateEvaluationAttachments(
            @PathVariable("evaluationId") Integer evaluationId,
            @RequestBody List<FyfcAttachmentDto> attachments,
            @RequestParam("operatorName") String operatorName) {
        
        log.info("更新评价附件请求: evaluationId={}, attachmentCount={}, operator={}", 
            evaluationId, attachments.size(), operatorName);
        
        return ossService.updateEvaluationAttachments(evaluationId, attachments, operatorName);
    }

    /**
     * 文件预览（重定向到OSS URL）
     */
    @GetMapping("/preview")
    public void previewFile(
            @RequestParam("fileKey") String fileKey,
            @RequestParam(value = "expireSeconds", defaultValue = "3600") Integer expireSeconds,
            @RequestParam(value = "bucketName", required = false) String bucketName,
            javax.servlet.http.HttpServletResponse response) {

        log.info("文件预览请求: fileKey={}, bucket={}", fileKey, bucketName);

        try {
            FyfcApiResponseDto<String> urlResult = ossService.getFileUrl(fileKey, expireSeconds, bucketName);
            if (urlResult.getSuccess()) {
                response.sendRedirect(urlResult.getData());
            } else {
                response.sendError(404, "文件不存在或获取URL失败");
            }
        } catch (Exception e) {
            log.error("文件预览失败", e);
            try {
                response.sendError(500, "文件预览失败");
            } catch (Exception ex) {
                log.error("发送错误响应失败", ex);
            }
        }
    }

    /**
     * 检查文件是否存在
     */
    @GetMapping("/exists")
    public FyfcApiResponseDto<Boolean> checkFileExists(
            @RequestParam("fileKey") String fileKey,
            @RequestParam(value = "bucketName", required = false) String bucketName) {
        log.info("检查文件存在请求: fileKey={}, bucket={}", fileKey, bucketName);

        try {
            if (StrUtil.isBlank(fileKey)) {
                return FyfcApiResponseDto.error(400, "文件键不能为空");
            }

            FyfcApiResponseDto<String> urlResult = ossService.getFileUrl(fileKey, 60, bucketName);
            return FyfcApiResponseDto.success(urlResult.getSuccess(),
                urlResult.getSuccess() ? "文件存在" : "文件不存在");

        } catch (Exception e) {
            log.error("检查文件存在失败", e);
            return FyfcApiResponseDto.error("检查文件失败: " + e.getMessage());
        }
    }
}
