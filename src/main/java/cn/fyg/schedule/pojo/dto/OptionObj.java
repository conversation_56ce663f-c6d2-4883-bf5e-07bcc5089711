package cn.fyg.schedule.pojo.dto;

import lombok.Data;

@Data
public class OptionObj<T> {
    private String label;
    private T value;

    public OptionObj(String label, T value) {
        this.label = label;
        this.value = value;
    }

    public OptionObj() {
        super();
    }

    public static <T> OptionObj<T> build(String label, T value) {
        return new OptionObj<>(label, value);
    }
}
