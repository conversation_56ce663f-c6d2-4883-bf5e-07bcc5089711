logging:
  level:
    org.hibernate.type.descriptor.sql.BasicBinder: trace
    org.springframework.web: INFO
    cn.fyg.schedule: DEBUG
    me.chanjar.weixin: DEBUG

spring:
  profiles:
    active: prod
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
server:
  ssl:
    key-store: fyg.cn.pfx
    key-store-password: qnjcvckz
    key-store-type: PKCS12

aliyun:
  face:
    accessKeyId: LTAI4Fy2z3rKSX8ZGXedbsCD
    accessKeySecret: ******************************
    regionId: cn-shanghai
    endpoint: ecs.aliyuncs.com

# FYFC OSS 上传配置
fyfc:
  oss:
    upload:
      # 单个文件大小限制（50MB）
      max-file-size: 52428800  # 50 * 1024 * 1024
      # 批量上传总大小限制（500MB）
      max-batch-total-size: 524288000  # 500 * 1024 * 1024
      # 单次上传文件数量限制
      max-file-count: 10
      # 是否启用文件类型检查
      enable-type-check: true
      # 是否启用文件大小检查
      enable-size-check: true
      # 允许的文件类型（空表示允许所有，除了禁止列表）
      allowed-mime-types: []
      # 允许的文件扩展名（空表示允许所有，除了禁止列表）
      allowed-extensions: []
      # 禁止的文件类型（安全考虑）
      forbidden-mime-types:
        - application/x-executable
        - application/x-msdownload
        - application/x-msdos-program
        - application/x-msi
        - application/x-bat
        - application/x-sh
        - text/x-script
      # 禁止的文件扩展名（安全考虑）
      forbidden-extensions:
        - .exe
        - .bat
        - .cmd
        - .com
        - .scr
        - .pif
        - .msi
        - .dll
        - .sh
        - .bash
        - .zsh
        - .fish
        - .ps1
        - .vbs
        - .js
        - .jar



