package cn.fyg.schedule.enums.fyg.accounting;

import cn.fyg.schedule.enums.BaseEnum;
import lombok.Getter;
@Getter
public enum BankAccountType implements BaseEnum<String> {
    JI_BEN_HU("基本户"),
    JIAN_GUAN_HU("监管户"),
    YI_BAN_HU("一般户"),
    DAI_KUAN_HU("贷款户");

    private final String value;

    BankAccountType(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return value;
    }
}
