package cn.fyg.schedule.config;


import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "aliyun.oss")
public class AliyunOssProperties {
	private String externalEndpoint;
	private String endpoint;
	private String accessKeyId;
	private String accessKeySecret;
	private String bucketName; 
	private String firstKey;
}
