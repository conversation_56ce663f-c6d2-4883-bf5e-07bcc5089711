package cn.fyg.schedule.pojo;

import cn.fyg.schedule.enums.AssignmentStatusEnum;
import cn.fyg.schedule.pojo.dto.FAssignmentDto;
import cn.hutool.core.date.DateUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

@Entity
@Setter
@Getter
@ToString
@Table(name = "fassignment")
public class FAssignment {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Column(name = "schedule_id")
    private Integer scheduleId;
    @Column(name = "voucher_id")
    private Integer voucherId;
    @Column(name = "creator")
    private String creator;
    @Column(name = "voucher_type")
    private Integer voucherType;
    @Column(name = "voucher_name")
    private String voucherName;
    @Column(name = "current_status")
    private String currentStatus;
    @Column(name = "create_date")
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @Column(name = "update_date")
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;

    public static FAssignment initialize(FAssignmentDto dto) {
        FAssignment fAssignment = new FAssignment();
        if (dto.getId() != null) {
            fAssignment.setId(dto.getId());
        }
        fAssignment.setCreator(dto.getCreator());
        fAssignment.setScheduleId(dto.getScheduleId());
        fAssignment.setVoucherId(dto.getVoucherId());
        fAssignment.setVoucherType(dto.getVoucherType());
        fAssignment.setVoucherName(dto.getVoucherName());
        if (dto.getCreateDate() != null) {
            fAssignment.setCreateDate(DateUtil.parse(dto.getCreateDate()));
        }
        if (dto.getValue() != null) {
            fAssignment.setCurrentStatus(AssignmentStatusEnum.valueOf(dto.getValue()).getName());
        }
        return fAssignment;
    }
}
