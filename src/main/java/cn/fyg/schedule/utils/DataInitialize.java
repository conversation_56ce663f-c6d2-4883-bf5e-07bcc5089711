package cn.fyg.schedule.utils;

import cn.fyg.schedule.pojo.dto.project.yx.security.notification.YxSecurityNotiRecordDto;
import cn.fyg.schedule.pojo.dto.project.yx.security.notification.YxSecurityNotificationDto;
import cn.hutool.core.date.DateUtil;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Date;

public class DataInitialize {
    public static <T> void mapDtoToObject(YxSecurityNotificationDto dto, T object) {
        if (dto.getId() != null) {
            invokeSetterMethod(object, "id", dto.getId());
        }
        invokeSetterMethod(object, "creator", dto.getCreator());
        invokeSetterMethod(object, "creatorName", dto.getCreatorName());
        invokeSetterMethod(object, "yxProjectName", dto.getYxProjectName());
        invokeSetterMethod(object, "yxTimeLimit", dto.getYxTimeLimit());
        invokeSetterMethod(object, "yxProblemDescription", dto.getYxProblemDescription());

        if (dto.getCreateDate() != null) {
            invokeSetterMethod(object, "createDate", DateUtil.date(dto.getCreateDate()).toJdkDate());
        }
        if (dto.getInspectionDate() != null) {
            Date date = DateUtil.date(dto.getInspectionDate()).toJdkDate();
            invokeSetterMethod(object, "inspectionDate", date);
        }

        invokeSetterMethod(object, "type", dto.getType());
        invokeSetterMethod(object, "yxEventName", dto.getYxEventName());
        invokeSetterMethod(object, "needSign", dto.getNeedSign());
        invokeSetterMethod(object, "dataStatus", dto.getDataStatus());
        invokeSetterMethod(object, "lastEditor", dto.getLastEditor());
        invokeSetterMethod(object, "eventStatus", dto.getEventStatus());
    }

    public static <T> void mapDtoToObject(YxSecurityNotiRecordDto dto, T object) {
        if (dto.getId() != null) {
            invokeSetterMethod(object, "id", dto.getId());
        }
        invokeSetterMethod(object, "parentId", dto.getParentId());
        invokeSetterMethod(object, "creator", dto.getCreator());
        invokeSetterMethod(object, "creatorName", dto.getCreatorName());
        invokeSetterMethod(object, "creatorPhone", dto.getCreatorPhone());
        invokeSetterMethod(object, "avatar", dto.getAvatar());
        invokeSetterMethod(object, "records", dto.getRecords());
        invokeSetterMethod(object, "recordStatus", dto.getRecordStatus());
        if (dto.getCreateDate() != null) {
            Date date = DateUtil.date(dto.getCreateDate()).toJdkDate();
            invokeSetterMethod(object, "createDate", date);
        }
    }

    @SuppressWarnings("unchecked")
    private static <T> void invokeSetterMethod(T object, String propertyName, Object value) {
        if (value != null) {
            try {
                Class<?> clazz = object.getClass();
                String setterMethodName = "set" + propertyName.substring(0, 1).toUpperCase() + propertyName.substring(1);
                Method setterMethod = clazz.getMethod(setterMethodName, value.getClass());
                setterMethod.invoke(object, value);
            } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
                // Handle the exceptions according to your requirements
                e.printStackTrace();
            }
        }
    }

}
