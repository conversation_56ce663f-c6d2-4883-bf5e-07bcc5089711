package cn.fyg.schedule.controller.fyg.accounting;

import cn.fyg.schedule.pojo.dto.IntegerOptionObj;
import cn.fyg.schedule.pojo.dto.fyg.accounting.bank.BankAccountDTO;
import cn.fyg.schedule.reponse.BaseResponse;
import cn.fyg.schedule.service.fyg.accounting.FygBankAccountsService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("fyg/accounting/bank-account")
public class FygBankAccountsController {
    private final FygBankAccountsService service;

    public FygBankAccountsController(FygBankAccountsService service) {
        this.service = service;
    }

    @PutMapping("save")
    public BaseResponse<BankAccountDTO> save(BankAccountDTO dto) {
        if (dto.getId() == null) {
            return service.save(dto);
        } else {
            return service.save(dto, dto.getId());
        }
    }

    @PostMapping("list")
    public BaseResponse<List<BankAccountDTO>> list(Integer dataStatus, Integer projectId) {
        return service.listBankAccount(dataStatus, projectId);
    }

    @PostMapping("list-options")
    public BaseResponse<List<IntegerOptionObj>> listOptions(Integer dataStatus, Integer projectId) {
        return service.listOptions(dataStatus, projectId);
    }

    @PutMapping("update-status")
    public BaseResponse<BankAccountDTO> updateStatus(Integer id, Integer dataStatus, String updatedBy) {
        return service.setDataStatus(id, dataStatus, updatedBy);
    }
}
