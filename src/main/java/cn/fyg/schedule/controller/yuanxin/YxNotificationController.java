package cn.fyg.schedule.controller.yuanxin;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.project.yx.security.notification.YxSecurityNotiRecordDto;
import cn.fyg.schedule.pojo.dto.project.yx.security.notification.YxSecurityNotificationDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.pojo.project.yuanXin.security.notification.YxSecurityNotiItem;
import cn.fyg.schedule.pojo.project.yuanXin.security.notification.YxSecurityNotiRecordItem;
import cn.fyg.schedule.pojo.project.yuanXin.security.notification.YxSecurityNotiRecordSign;
import cn.fyg.schedule.pojo.project.yuanXin.security.notification.YxSecurityNotiSign;
import cn.fyg.schedule.service.sheets.yx.security.notification.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("yx/notification")
public class YxNotificationController {
    private final YxSecurityNotificationService notificationService;
    private final YxSecurityNotiSignService signService;
    private final YxSecurityNotiItemService itemService;
    private final YxSecurityNotiRecordService recordService;
    private final YxSecurityNotiRecordItemService recordItemService;

    private final YxSecurityNotiRecordSignService recordSignService;

    public YxNotificationController(YxSecurityNotificationService notificationService, YxSecurityNotiSignService signService, YxSecurityNotiItemService itemService, YxSecurityNotiRecordService recordService, YxSecurityNotiRecordItemService recordItemService, YxSecurityNotiRecordSignService recordSignService) {
        this.notificationService = notificationService;
        this.signService = signService;
        this.itemService = itemService;
        this.recordService = recordService;
        this.recordItemService = recordItemService;
        this.recordSignService = recordSignService;
    }

    @PostMapping("projects")
    public BaseResponse getProjectNameList(Integer type) {
        return notificationService.getProjectNameList(type);
    }

    @PostMapping("events")
    public BaseResponse getEventNameList(Integer type) {
        return notificationService.getEventNameList(type);
    }

    @PostMapping("filter")
    public BaseResponse filter(YxSecurityNotificationDto dto, Pagination pagination) {
        return notificationService.filter(dto, pagination);
    }

    @PostMapping("list/record/creator/phone")
    public BaseResponse listNotificationByRecordCreatorPhone(Integer type, String creatorPhone) {
        BaseResponse baseResponse = BaseResponse.initialize();
        List<Integer> ids = recordService.listParentIdByCreatorPhone(creatorPhone);
        if (ids != null) {
            baseResponse = notificationService.findByIdIn(type, ids);
        } else {
            baseResponse.setCode(1);
            baseResponse.setMsg("没有查到相关记录");
        }
        return baseResponse;
    }
    @PostMapping("find/id")
    public BaseResponse findById(Integer id) {
        return notificationService.findById(id);
    }

    @PostMapping ("save")
    public BaseResponse saveNotification(YxSecurityNotificationDto dto) {
        return notificationService.save(dto);
    }

    @PostMapping("delete")
    public BaseResponse deleteNotification(Integer id) {
        return notificationService.delete(id);
    }

    @PostMapping("find/item")
    public BaseResponse findItems(Integer id) {
        return itemService.findByParentId(id);
    }

    @PostMapping("delete/item")
    public BaseResponse deleteItem(Integer id) {
        return itemService.delete(id);
    }

    @PostMapping("save/item")
    public BaseResponse saveItem(YxSecurityNotiItem data) {
        return itemService.save(data);
    }

    @PostMapping("find/signature")
    public BaseResponse findSignature(Integer id) {
        return signService.findByParentId(id);
    }

    @PostMapping("delete/signature")
    public BaseResponse deleteSignature(Integer id) {
        return signService.delete(id);
    }

    @PostMapping("save/signature")
    public BaseResponse saveSignature(YxSecurityNotiSign data) {
        return signService.save(data);
    }

    @PostMapping("save/record")
    public BaseResponse saveRecord(YxSecurityNotiRecordDto dto) {
        return recordService.save(dto);
    }

    @PostMapping("find/record")
    public BaseResponse findRecord(Integer id) {
        return recordService.findByParentId(id);
    }

    @PostMapping("find/record/status")
    public BaseResponse findRecordIfApproved(Integer id, Integer recordStatus) {
        return recordService.ifApproved(id, recordStatus);
    }

    @PostMapping("delete/record")
    public BaseResponse deleteRecord(Integer id) {
        return recordService.delete(id);
    }

    @PostMapping("find/record/item")
    public BaseResponse findRecordItem(Integer id) {
        return recordItemService.findByParentId(id);
    }

    @PostMapping("save/record/item")
    public BaseResponse saveRecordItem(YxSecurityNotiRecordItem data) {
        return recordItemService.save(data);
    }

    @PostMapping("delete/record/item")
    public BaseResponse deleteRecordItem(Integer id) {
        return recordItemService.delete(id);
    }

    @PostMapping("find/record/sign")
    public BaseResponse findRecordSign(Integer id) {
        return recordSignService.findByParentId(id);
    }

    @PostMapping("save/record/sign")
    public BaseResponse saveRecordSign(YxSecurityNotiRecordSign data) {
        return recordSignService.save(data);
    }

    @PostMapping("delete/record/sign")
    public BaseResponse deleteRecordSign(Integer id) {
        return recordSignService.deleteByParentId(id);
    }
}
