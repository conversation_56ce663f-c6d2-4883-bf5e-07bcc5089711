package cn.fyg.schedule.pojo.fyapp;

import cn.fyg.schedule.pojo.dto.fyapp.FGroupListDto;
import cn.hutool.core.date.DateUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

@Entity
@Setter
@Getter
@ToString
@Table(name = "f_group_list")
public class FGroupList {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    private String name;
    private String groupValue;
    private String groupKey;
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;

    public static FGroupList initialize(FGroupListDto dto) {
        FGroupList data = new FGroupList();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        data.setName(dto.getName());
        data.setGroupValue(dto.getGroupValue());
        data.setGroupKey(dto.getGroupKey());
        if (dto.getCreateDate() != null) {
            data.setCreateDate(DateUtil.parse(dto.getCreateDate()));
        }
        return data;
    }
}
