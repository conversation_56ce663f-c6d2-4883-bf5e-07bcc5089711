package cn.fyg.schedule.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum ApprovalStatusEnum {
    IN_PROGRESS("审批中", 1, "in_progress"),
    APPROVED("已通过", 2, "approved"),
    REJECTED("已驳回", 3, "rejected"),
    REVOKED("已撤销", 4, "revoked"),
    REVOKED_AFTER_APPROVAL("通过后撤销", 6, "revoked_after_approval"),
    DELETED("已删除", 7, "deleted"),
    PAID("已支付", 10, "paid");

    private final String description;
    private final Integer code;
    private final String englishDescription;

    ApprovalStatusEnum(String description, Integer code, String englishDescription) {
        this.description = description;
        this.code = code;
        this.englishDescription = englishDescription;
    }

    public static ApprovalStatusEnum findByCode(Integer code) {
        for (ApprovalStatusEnum statusEnum : values()) {
            if (Objects.equals(statusEnum.getCode(), code)) {
                return statusEnum;
            }
        }
        throw new IllegalArgumentException("No matching constant for [" + code + "]");
    }
}
