package cn.fyg.schedule.service.statement.capital.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.statement.capital.FCapitalStatementIncomeRepository;
import cn.fyg.schedule.pojo.statement.capital.FCapitalStatementIncome;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.statement.capital.FCapitalStatementIncomeService;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.HibernateException;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class FCapitalStatementIncomeServiceImpl implements FCapitalStatementIncomeService {
    private final FCapitalStatementIncomeRepository r;
    public FCapitalStatementIncomeServiceImpl(FCapitalStatementIncomeRepository r) {
        this.r = r;
    }
    @Override
    public BaseResponse save(FCapitalStatementIncome data) {
        return BaseService.save(data, r, FCapitalStatementIncome.class);
    }

    @Override
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse list(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FCapitalStatementIncome> result = r.findByCapitalStatementIdOrderByNumberDesc(id);
            baseResponse.setResult(result);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Optional<FCapitalStatementIncome> tmp = r.findById(id);
            baseResponse.setResult(tmp.get());
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public Double sumCurrentDayIncome(Integer id) {
        Double result = 0.00;
        try {
            List<FCapitalStatementIncome> list = r.findByCapitalStatementIdOrderByNumberDesc(id);
            for (FCapitalStatementIncome income : list) {
                result += income.getCurrentDayAmount();
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return result;
    }

    @Override
    public Double cumCurrentDayIncome(Integer id) {
        Double result = 0.00;
        try {
            List<FCapitalStatementIncome> list = r.findByCapitalStatementIdOrderByNumberDesc(id);
            for (FCapitalStatementIncome income : list) {
                result += income.getCurrentDayAmount() + income.getCumulativeAmount();
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return result;
    }

    @Override
    public BaseResponse getHistoryAmount(Integer id, String subject) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FCapitalStatementIncome> result = r.findByCapitalStatementIdAndCurrencySubject(id, subject);
            Double sum = 0.00;
            if (result.size() > 0) {
                for (FCapitalStatementIncome income : result) {
                    sum += income.getCumulativeAmount();
                }
            }
            baseResponse.setResult(sum);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }
}
