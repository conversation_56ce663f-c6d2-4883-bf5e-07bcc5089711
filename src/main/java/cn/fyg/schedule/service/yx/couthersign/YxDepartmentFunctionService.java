package cn.fyg.schedule.service.yx.couthersign;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.yx.countersign.YxDepartmentFunction;

import java.util.List;

public interface YxDepartmentFunctionService {
    BaseResponse save(YxDepartmentFunction data);
    BaseResponse delete(Integer id);
    BaseResponse deleteByDepartmentID(Integer departmentId);
    BaseResponse listByDepartmentId(Integer departmentId);
    BaseResponse listAll();
    List<YxDepartmentFunction> findByDepartmentId(Integer departmentId) throws MyException;
    List<Integer> listDepartmentFunctionId(Integer departmentId) throws MyException;
}
