package cn.fyg.schedule.specification;

import cn.fyg.schedule.pojo.project.fyjs.sealManagement.FJsSealManagement;
import cn.fyg.schedule.utils.SpecificationUtil;
import org.springframework.data.jpa.domain.Specification;

import java.util.Date;

public class FJsSealManagementSpecification {
    public static Specification<FJsSealManagement> ifContain(String column, String expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.like(root.get(column), SpecificationUtil.contains(expression)));
    }

    public static Specification<FJsSealManagement> ifDateEq(String column, Date expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }

    public static Specification<FJsSealManagement> ifNumberEq(String column, Integer expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }
}
