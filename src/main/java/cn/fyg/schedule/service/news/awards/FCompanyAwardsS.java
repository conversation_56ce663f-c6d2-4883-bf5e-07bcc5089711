package cn.fyg.schedule.service.news.awards;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.query.Base;
import cn.fyg.schedule.pojo.news.awads.FCompanyAwards;

import java.util.Date;
import java.util.List;

public interface FCompanyAwardsS {
    BaseResponse save(FCompanyAwards data);
    BaseResponse findById(Integer id);
    BaseResponse delete(Integer id);
    BaseResponse getCompanyHistoryList();
    BaseResponse listAll();
    BaseResponse listByAwardDate(Date begin, Date end);
    BaseResponse listCompanyIn(List<String> companies);
}
