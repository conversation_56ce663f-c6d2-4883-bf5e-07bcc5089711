package cn.fyg.schedule.service.news.awards.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.news.awards.FCompanyAwardsR;
import cn.fyg.schedule.pojo.news.awads.FCompanyAwards;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.news.awards.FCompanyAwardsS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class FCompanyAwardsSI implements FCompanyAwardsS {
    private final FCompanyAwardsR r;
    public FCompanyAwardsSI(FCompanyAwardsR r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FCompanyAwards data) {
        BaseResponse response = BaseService.save(data, r, FCompanyAwards.class);
        // 如果保存成功，预加载懒加载的集合，避免序列化时的LazyInitializationException
        if (response.getCode() == 0 && response.getResult() instanceof FCompanyAwards) {
            FCompanyAwards savedAward = (FCompanyAwards) response.getResult();
            preloadLazyCollections(savedAward);
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse findById(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Optional<FCompanyAwards> data = r.findById(id);
            if (data.isPresent()) {
                FCompanyAwards award = data.get();
                // 预加载懒加载的items集合，避免LazyInitializationException
                preloadLazyCollections(award);
                baseResponse.setResult(award);
            }
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse getCompanyHistoryList() {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<String> data = r.getCompanyHistoryList();
            baseResponse.setResult(data);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse listAll() {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FCompanyAwards> data = r.findAllByOrderByAwardDateDesc();
            // 预加载懒加载的items集合，避免LazyInitializationException
            data.forEach(this::preloadLazyCollections);
            baseResponse.setResult(data);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse listByAwardDate(Date begin, Date end) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FCompanyAwards> data = r.findByAwardDateBetweenOrderByAwardDateDesc(begin, end);
            // 预加载懒加载的items集合，避免LazyInitializationException
            data.forEach(this::preloadLazyCollections);
            baseResponse.setResult(data);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse listCompanyIn(List<String> companies) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FCompanyAwards> data = !companies.isEmpty() ?r.findAllByCompanyInOrderByAwardDateDesc(companies):r.findAllByOrderByAwardDateDesc();
            // 预加载懒加载的items集合，避免LazyInitializationException
            data.forEach(this::preloadLazyCollections);
            baseResponse.setResult(data);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    /**
     * 预加载懒加载的集合，避免LazyInitializationException
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    private void preloadLazyCollections(FCompanyAwards award) {
        if (award != null) {
            // 触发懒加载 - 我们只需要触发加载，不需要使用返回值
            if (award.getItems() != null) {
                award.getItems().size(); // 触发懒加载
            }
        }
    }

}
