package cn.fyg.schedule.service.fyfc.review.impl;

import cn.fyg.schedule.dao.fyfc.review.FyfcEvaluationRepository;
import cn.fyg.schedule.dao.fyfc.review.FyfcEvaluationScoreRepository;
import cn.fyg.schedule.dao.fyfc.review.FyfcEvaluationStatusHistoryRepository;
import cn.fyg.schedule.enums.fyfc.review.EvaluationStatus;
import cn.fyg.schedule.enums.fyfc.review.EvaluatorType;
import cn.fyg.schedule.pojo.dto.fyfc.review.*;
import cn.fyg.schedule.pojo.fyfc.review.FyfcEvaluation;
import cn.fyg.schedule.pojo.fyfc.review.FyfcEvaluationScore;
import cn.fyg.schedule.pojo.fyfc.review.FyfcEvaluationStatusHistory;
import cn.fyg.schedule.service.fyfc.review.IFyfcEvaluationCommonService;
import cn.fyg.schedule.service.fyfc.review.IFyfcEvaluationScoreService;
import cn.fyg.schedule.service.fyfc.review.IFyfcEvaluationStaffService;
import cn.fyg.schedule.util.fyfc.review.FyfcEvaluationConverter;
import cn.fyg.schedule.specification.fyfc.review.FyfcEvaluationSpecification;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * FYFC 评价员工服务实现类
 * 对应 staff/dashboard 和 staff/edit 页面的业务逻辑
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FyfcEvaluationStaffServiceImpl implements IFyfcEvaluationStaffService {

    private final FyfcEvaluationRepository evaluationRepository;
    private final FyfcEvaluationScoreRepository scoreRepository;
    private final FyfcEvaluationStatusHistoryRepository statusHistoryRepository;
    private final IFyfcEvaluationCommonService commonService;
    private final IFyfcEvaluationScoreService scoreService;
    private final FyfcEvaluationConverter evaluationConverter;

    // ==================== Staff Dashboard 相关方法 ====================

    @Override
    public FyfcApiResponseDto<FyfcPaginatedResponseDto<FyfcEvaluationDto>> getEvaluationHistory(String userName, FyfcEvaluationQueryDto queryDto) {
        log.info("员工查询评价历史: userName={}, query={}", userName, queryDto);
        
        try {
            if (StrUtil.isBlank(userName)) {
                return FyfcApiResponseDto.error(400, "用户名不能为空");
            }
            
            // 设置查询当前用户作为被评价人的评价历史
            queryDto.setName(userName);
            queryDto.setQueryType("all");
            
            // 构建查询条件
            Specification<FyfcEvaluation> spec = FyfcEvaluationSpecification.buildSpecification(queryDto);
            
            // 构建分页和排序
            Sort sort = buildSort(queryDto.getSortBy(), queryDto.getSortDirection());
            Pageable pageable = PageRequest.of(
                queryDto.getPage() - 1,
                queryDto.getSize(),
                sort
            );
            
            // 执行查询
            Page<FyfcEvaluation> page = evaluationRepository.findAll(spec, pageable);
            
            // 转换为DTO
            List<FyfcEvaluationDto> dtoList = page.getContent().stream()
                .map(evaluationConverter::convertToEvaluationDto)
                .collect(Collectors.toList());
            
            FyfcPaginatedResponseDto<FyfcEvaluationDto> paginatedResponse = 
                FyfcPaginatedResponseDto.of(dtoList, queryDto.getPage(), queryDto.getSize(), page.getTotalElements());
            
            return FyfcApiResponseDto.success(paginatedResponse, "查询成功");
        } catch (Exception e) {
            log.error("员工查询评价历史失败", e);
            return FyfcApiResponseDto.error("查询失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<List<FyfcEvaluationDto>> getPendingEvaluations(String userName) {
        log.info("查询员工待办评价: {}", userName);
        
        try {
            if (StrUtil.isBlank(userName)) {
                return FyfcApiResponseDto.error(400, "用户名不能为空");
            }
            
            Specification<FyfcEvaluation> spec = FyfcEvaluationSpecification.pendingForUser(userName);
            Sort sort = Sort.by(Sort.Direction.DESC, "createdAt");
            List<FyfcEvaluation> evaluations = evaluationRepository.findAll(spec, sort);
            
            List<FyfcEvaluationDto> dtoList = evaluations.stream()
                .map(evaluationConverter::convertToEvaluationDto)
                .collect(Collectors.toList());
            
            return FyfcApiResponseDto.success(dtoList, "查询成功");
        } catch (Exception e) {
            log.error("查询员工待办评价失败", e);
            return FyfcApiResponseDto.error("查询失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<FyfcEvaluationStatsDto> getEvaluationStats(String userName) {
        log.info("获取员工评价统计: {}", userName);
        
        try {
            if (StrUtil.isBlank(userName)) {
                return FyfcApiResponseDto.error(400, "用户名不能为空");
            }
            
            FyfcEvaluationStatsDto stats = new FyfcEvaluationStatsDto();
            
            // 查询与用户相关的评价
            Specification<FyfcEvaluation> spec = FyfcEvaluationSpecification.relatedToUser(userName);
            List<FyfcEvaluation> evaluations = evaluationRepository.findAll(spec);
            
            // 总评价数
            stats.setTotalEvaluations((long) evaluations.size());
            
            // 已完成评价数
            long completedCount = evaluations.stream()
                .filter(e -> e.getStatus() == EvaluationStatus.COMPLETED)
                .count();
            stats.setCompletedEvaluations(completedCount);
            
            // 进行中评价数
            stats.setInProgressEvaluations(stats.getTotalEvaluations() - completedCount);
            
            // 计算完成率
            if (stats.getTotalEvaluations() > 0) {
                BigDecimal completionRate = BigDecimal.valueOf(completedCount)
                    .divide(BigDecimal.valueOf(stats.getTotalEvaluations()), 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
                stats.setCompletionRate(completionRate);
            } else {
                stats.setCompletionRate(BigDecimal.ZERO);
            }
            
            // TODO: 实现其他统计数据
            
            return FyfcApiResponseDto.success(stats, "查询成功");
        } catch (Exception e) {
            log.error("获取员工评价统计失败", e);
            return FyfcApiResponseDto.error("查询失败: " + e.getMessage());
        }
    }

    // ==================== Staff Edit 相关方法 ====================

    @Override
    @Transactional
    public FyfcApiResponseDto<FyfcEvaluationDto> createEvaluation(FyfcEvaluationFormDto formDto, String creatorName) {
        log.info("创建新评价: form={}, creator={}", formDto, creatorName);
        
        try {
            if (ObjectUtil.isNull(formDto) || StrUtil.isBlank(creatorName)) {
                return FyfcApiResponseDto.error(400, "参数不能为空");
            }
            
            // 验证表单数据
            FyfcApiResponseDto<Object> validation = validateEvaluationForm(formDto);
            if (!validation.getSuccess()) {
                return FyfcApiResponseDto.error(validation.getMessage());
            }
            
            // 转换为实体
            FyfcEvaluation evaluation = new FyfcEvaluation();
            BeanUtil.copyProperties(formDto, evaluation);
            evaluation.setCreatedBy(creatorName);
            evaluation.setStatus(EvaluationStatus.SELF);
            
            // 处理时间戳转换
            if (ObjectUtil.isNotNull(formDto.getReviewDate())) {
                evaluation.setReviewDate(new Date(formDto.getReviewDate()));
            }
            
            // 保存评价
            evaluation = evaluationRepository.save(evaluation);
            
            // 记录状态变更历史
            recordStatusChange(evaluation.getId(), null, EvaluationStatus.SELF, creatorName, "创建评价");
            
            FyfcEvaluationDto dto = evaluationConverter.convertToEvaluationDto(evaluation);
            return FyfcApiResponseDto.success(dto, "评价创建成功");
        } catch (Exception e) {
            log.error("创建评价失败", e);
            return FyfcApiResponseDto.error("创建失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public FyfcApiResponseDto<Boolean> updateEvaluation(FyfcEvaluationUpdateDto updateDto, String updaterName) {
        log.info("更新评价: updateDto={}, updater={}", updateDto, updaterName);
        
        try {
            if (ObjectUtil.isNull(updateDto) || ObjectUtil.isNull(updateDto.getId()) || StrUtil.isBlank(updaterName)) {
                return FyfcApiResponseDto.error(400, "参数不能为空");
            }
            
            Optional<FyfcEvaluation> optionalEvaluation = evaluationRepository.findById(updateDto.getId());
            if (!optionalEvaluation.isPresent()) {
                return FyfcApiResponseDto.error(404, "评价记录不存在");
            }
            
            FyfcEvaluation evaluation = optionalEvaluation.get();
            
            // 检查权限
//            if (!canUserEditEvaluation(evaluation, updaterName)) {
//                return FyfcApiResponseDto.error(403, "您无权限编辑此评价");
//            }
            
            // 更新字段
            updateEvaluationFields(evaluation, updateDto);
            evaluation.setUpdatedBy(updaterName);
            
            // 保存更新
            evaluationRepository.save(evaluation);
            
            return FyfcApiResponseDto.success(true, "评价更新成功");
        } catch (Exception e) {
            log.error("更新评价失败", e);
            return FyfcApiResponseDto.error("更新失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<FyfcEvaluationDetailDto> getEvaluationDetail(Integer evaluationId, String userName) {
        log.info("获取评价详情: evaluationId={}, userName={}", evaluationId, userName);
        
        try {
            if (ObjectUtil.isNull(evaluationId) || StrUtil.isBlank(userName)) {
                return FyfcApiResponseDto.error(400, "参数不能为空");
            }
            
            Optional<FyfcEvaluation> optionalEvaluation = evaluationRepository.findById(evaluationId);
            if (!optionalEvaluation.isPresent()) {
                return FyfcApiResponseDto.error(404, "评价记录不存在");
            }
            
            FyfcEvaluation evaluation = optionalEvaluation.get();
            
            // 检查用户权限
            if (!isUserRelatedToEvaluation(evaluation, userName)) {
                return FyfcApiResponseDto.error(403, "您无权限查看此评价");
            }
            
            FyfcEvaluationDetailDto detailDto = evaluationConverter.convertToEvaluationDetailDto(evaluation);
            
            return FyfcApiResponseDto.success(detailDto, "查询成功");
        } catch (Exception e) {
            log.error("获取评价详情失败", e);
            return FyfcApiResponseDto.error("查询失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public FyfcApiResponseDto<Boolean> submitSelfScore(FyfcScoreFormDto scoreForm, String evaluatorName) {
        log.info("提交自评分数: form={}, evaluator={}", scoreForm, evaluatorName);
        
        try {
            // 验证是否为自评
            Optional<FyfcEvaluation> optionalEvaluation = evaluationRepository.findById(scoreForm.getEvaluationId());
            if (!optionalEvaluation.isPresent()) {
                return FyfcApiResponseDto.error(404, "评价记录不存在");
            }
            
            FyfcEvaluation evaluation = optionalEvaluation.get();
            if (!evaluatorName.equals(evaluation.getName())) {
                return FyfcApiResponseDto.error(403, "您只能提交自己的自评分数");
            }
            
            // 调用评分服务
            return scoreService.submitScore(scoreForm, evaluatorName);
        } catch (Exception e) {
            log.error("提交自评分数失败", e);
            return FyfcApiResponseDto.error("提交失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public FyfcApiResponseDto<Boolean> submitColleagueScore(FyfcScoreFormDto scoreForm, String evaluatorName) {
        log.info("提交同事评分: form={}, evaluator={}", scoreForm, evaluatorName);
        
        try {
            // 验证是否为同事评价
            Optional<FyfcEvaluation> optionalEvaluation = evaluationRepository.findById(scoreForm.getEvaluationId());
            if (!optionalEvaluation.isPresent()) {
                return FyfcApiResponseDto.error(404, "评价记录不存在");
            }
            
            FyfcEvaluation evaluation = optionalEvaluation.get();
            if (!evaluatorName.equals(evaluation.getColleagueName())) {
                return FyfcApiResponseDto.error(403, "您只能提交指定的同事评分");
            }
            
            // 调用评分服务
            return scoreService.submitScore(scoreForm, evaluatorName);
        } catch (Exception e) {
            log.error("提交同事评分失败", e);
            return FyfcApiResponseDto.error("提交失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public FyfcApiResponseDto<Boolean> deleteEvaluation(Integer evaluationId, String operatorName) {
        log.info("删除评价: evaluationId={}, operator={}", evaluationId, operatorName);
        
        try {
            if (ObjectUtil.isNull(evaluationId) || StrUtil.isBlank(operatorName)) {
                return FyfcApiResponseDto.error(400, "参数不能为空");
            }
            
            Optional<FyfcEvaluation> optionalEvaluation = evaluationRepository.findById(evaluationId);
            if (!optionalEvaluation.isPresent()) {
                return FyfcApiResponseDto.error(404, "评价记录不存在");
            }
            
            FyfcEvaluation evaluation = optionalEvaluation.get();
            
            // 检查权限（只有创建人可以删除）
            if (!operatorName.equals(evaluation.getCreatedBy())) {
                return FyfcApiResponseDto.error(403, "您只能删除自己创建的评价");
            }
            
            // 检查状态（只有自评状态可以删除）
            if (evaluation.getStatus() != EvaluationStatus.SELF) {
                return FyfcApiResponseDto.error(400, "只有自评状态的评价可以删除");
            }
            
            // 删除相关记录
            scoreRepository.deleteByEvaluationId(evaluationId);
            statusHistoryRepository.deleteByEvaluationId(evaluationId);
            evaluationRepository.deleteById(evaluationId);
            
            return FyfcApiResponseDto.success(true, "评价删除成功");
        } catch (Exception e) {
            log.error("删除评价失败", e);
            return FyfcApiResponseDto.error("删除失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<Boolean> checkUserPermission(Integer evaluationId, String userName) {
        log.info("检查用户权限: evaluationId={}, userName={}", evaluationId, userName);
        
        try {
            if (ObjectUtil.isNull(evaluationId) || StrUtil.isBlank(userName)) {
                return FyfcApiResponseDto.error(400, "参数不能为空");
            }
            
            Optional<FyfcEvaluation> optionalEvaluation = evaluationRepository.findById(evaluationId);
            if (!optionalEvaluation.isPresent()) {
                return FyfcApiResponseDto.error(404, "评价记录不存在");
            }
            
            boolean hasPermission = isUserRelatedToEvaluation(optionalEvaluation.get(), userName);
            
            return FyfcApiResponseDto.success(hasPermission, hasPermission ? "有权限" : "无权限");
        } catch (Exception e) {
            log.error("检查用户权限失败", e);
            return FyfcApiResponseDto.error("检查失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<Object> getFormInitData(String userName) {
        log.info("获取表单初始数据: {}", userName);
        
        try {
            if (StrUtil.isBlank(userName)) {
                return FyfcApiResponseDto.error(400, "用户名不能为空");
            }
            
            // 获取部门列表
            FyfcApiResponseDto<List<String>> departments = commonService.getDepartmentList();
            
            // 构建表单初始数据
            Object formInitData = buildFormInitData(userName, departments.getData());
            
            return FyfcApiResponseDto.success(formInitData, "查询成功");
        } catch (Exception e) {
            log.error("获取表单初始数据失败", e);
            return FyfcApiResponseDto.error("查询失败: " + e.getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 构建排序条件
     */
    private Sort buildSort(String sortBy, String sortDirection) {
        Sort.Direction direction = "asc".equalsIgnoreCase(sortDirection) 
            ? Sort.Direction.ASC 
            : Sort.Direction.DESC;
        
        String sortField = ObjectUtil.defaultIfNull(sortBy, "createdAt");
        return Sort.by(direction, sortField);
    }



    /**
     * 验证评价表单
     */
    private FyfcApiResponseDto<Object> validateEvaluationForm(FyfcEvaluationFormDto formDto) {
        if (StrUtil.isBlank(formDto.getName())) {
            return FyfcApiResponseDto.error(400, "被评价人姓名不能为空");
        }
        if (StrUtil.isBlank(formDto.getDepartment())) {
            return FyfcApiResponseDto.error(400, "部门不能为空");
        }
        if (ObjectUtil.isNull(formDto.getReviewDate())) {
            return FyfcApiResponseDto.error(400, "评价日期不能为空");
        }
        
        return FyfcApiResponseDto.success(true, "验证通过");
    }

    /**
     * 检查用户是否可以编辑评价
     */
    private boolean canUserEditEvaluation(FyfcEvaluation evaluation, String userName) {
        // 创建人可以编辑
        if (userName.equals(evaluation.getCreatedBy())) {
            return true;
        }
        
        // 根据状态判断
        EvaluationStatus status = evaluation.getStatus();
        switch (status) {
            case SELF:
                return userName.equals(evaluation.getName());
            case COLLEAGUE:
                return userName.equals(evaluation.getColleagueName());
            case MANAGER:
                return userName.equals(evaluation.getManagerName());
            default:
                return false;
        }
    }

    /**
     * 检查用户是否与评价相关
     */
    private boolean isUserRelatedToEvaluation(FyfcEvaluation evaluation, String userName) {
        return userName.equals(evaluation.getName()) ||
               userName.equals(evaluation.getColleagueName()) ||
               userName.equals(evaluation.getManagerName()) ||
               userName.equals(evaluation.getCreatedBy());
    }

    /**
     * 更新评价字段
     */
    private void updateEvaluationFields(FyfcEvaluation evaluation, FyfcEvaluationUpdateDto updateDto) {
        if (StrUtil.isNotBlank(updateDto.getDepartment())) {
            evaluation.setDepartment(updateDto.getDepartment());
        }
        if (StrUtil.isNotBlank(updateDto.getName())) {
            evaluation.setName(updateDto.getName());
        }
        if (ObjectUtil.isNotNull(updateDto.getReviewDate())) {
            evaluation.setReviewDate(new Date(updateDto.getReviewDate()));
        }
        if (StrUtil.isNotBlank(updateDto.getColleagueName())) {
            evaluation.setColleagueName(updateDto.getColleagueName());
        }
        if (StrUtil.isNotBlank(updateDto.getManagerName())) {
            evaluation.setManagerName(updateDto.getManagerName());
        }
        if (ObjectUtil.isNotNull(updateDto.getAdditionalScore())) {
            evaluation.setAdditionalScore(updateDto.getAdditionalScore());
        }
        if (ObjectUtil.isNotNull(updateDto.getScore())) {
            BigDecimal oldScore = evaluation.getScore();
            evaluation.setScore(updateDto.getScore());
            log.info("评价总分已更新: evaluationId={}, {} -> {}",
                evaluation.getId(), oldScore, updateDto.getScore());
        }
        if (StrUtil.isNotBlank(updateDto.getComment())) {
            evaluation.setComment(updateDto.getComment());
        }
    }

    /**
     * 记录状态变更历史
     */
    private void recordStatusChange(Integer evaluationId, EvaluationStatus oldStatus, EvaluationStatus newStatus, String operatorName, String remark) {
        FyfcEvaluationStatusHistory history = new FyfcEvaluationStatusHistory();
        history.setEvaluationId(evaluationId);
        history.setPreviousStatus(oldStatus);
        history.setNewStatus(newStatus);
        history.setChangedBy(operatorName);
        history.setRemark(remark);
        
        statusHistoryRepository.save(history);
    }

    /**
     * 构建表单初始数据
     */
    private Object buildFormInitData(String userName, List<String> departments) {
        // TODO: 实现表单初始数据构建逻辑
        return new Object();
    }


}
