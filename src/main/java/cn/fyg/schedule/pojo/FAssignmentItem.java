package cn.fyg.schedule.pojo;

import cn.fyg.schedule.pojo.dto.FAssignmentItemDto;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "fassignmentitem")
public class FAssignmentItem {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    private Integer assignmentId;
    private String type;
    private String content;
    @Column(name = "create_date")
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;

    public static FAssignmentItem initialize(FAssignmentItemDto dto) {
        FAssignmentItem data = new FAssignmentItem();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        data.setAssignmentId(dto.getAssignmentId());
        data.setType(dto.getType());
        data.setContent(dto.getContent());
        return data;
    }
}
