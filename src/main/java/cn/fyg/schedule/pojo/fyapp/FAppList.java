package cn.fyg.schedule.pojo.fyapp;

import cn.fyg.schedule.pojo.dto.fyapp.FAppListDto;
import cn.hutool.core.date.DateUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

@Entity
@Setter
@Getter
@ToString
@Table(name = "f_app_list")
public class FAppList {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    private String name;
    private String appValue;
    private String appSrc;
    private String appGroup;
    private Integer status;
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;

    public static FAppList initialize(FAppListDto dto) {
        FAppList data = new FAppList();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        data.setName(dto.getName());
        data.setAppValue(dto.getAppValue());
        data.setAppSrc(dto.getAppSrc());
        data.setAppGroup(dto.getAppGroup());
        data.setStatus(dto.getStatus());
        if (dto.getCreateDate() != null) {
            data.setCreateDate(DateUtil.parse(dto.getCreateDate()));
        }
        return data;
    }
}
