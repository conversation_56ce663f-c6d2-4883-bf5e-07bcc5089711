package cn.fyg.schedule.enums;

import lombok.Getter;

@Getter
public enum ScheduleStatusEnum {
    DRAFT("历史", "000", "DRAFT"),
    UNDONE("待办", "001", "UNDONE"),
    DONE("已办","002", "DONE"),
    PERSONAL("个人", "003", "PERSONAL");
    private final String name;
    private final String number;
    private final String value;
    ScheduleStatusEnum(String name, String number, String value) {
        this.name = name;
        this.number = number;
        this.value = value;
    }
}
