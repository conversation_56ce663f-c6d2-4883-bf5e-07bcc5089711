package cn.fyg.schedule.enums.fyfc.review;

import cn.fyg.schedule.enums.BaseEnum;
import lombok.Getter;

/**
 * 评价人角色类型枚举
 * 对应数据库中的 type 字段
 */
@Getter
public enum EvaluatorType implements BaseEnum<String> {
    UNKNOWN("unknown", "未知"),
    EMPL<PERSON><PERSON><PERSON>("employee", "员工自评"),
    COLLEAGUE("colleague", "同事评价"),
    MANAGER("manager", "主管评价"),
    ADMIN("admin", "管理员");

    private final String value;
    private final String description;

    EvaluatorType(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String toString() {
        return value;
    }
}
