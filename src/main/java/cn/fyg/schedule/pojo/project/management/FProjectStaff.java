package cn.fyg.schedule.pojo.project.management;

import cn.fyg.schedule.pojo.dto.project.management.FProjectStaffDto;
import cn.hutool.core.date.DateUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.util.Date;

@Entity
@Setter
@Getter
@ToString
@Table(name = "f_project_staff")
public class FProjectStaff {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Column(name = "parent_id")
    private Integer parentId;
    private String position;
    private String name;
    private String contactInfo;
    private Integer isActivated;
    private Integer isSiteAdmin;
    @Column(name = "create_date")
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;

    public static FProjectStaff initialized(FProjectStaffDto dto) {
        FProjectStaff data = new FProjectStaff();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        if (dto.getCreateDate() != null) {
            data.setCreateDate(DateUtil.date(dto.getCreateDate()));
        }
        data.setName(dto.getName());
        data.setParentId(dto.getParentId());
        data.setContactInfo(dto.getContactInfo());
        data.setIsActivated(dto.getIsActivated());
        data.setIsSiteAdmin(dto.getIsSiteAdmin());
        data.setPosition(dto.getPosition());
        return data;
    }
}
