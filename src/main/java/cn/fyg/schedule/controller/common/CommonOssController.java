package cn.fyg.schedule.controller.common;

import cn.fyg.schedule.pojo.dto.fyfc.review.FyfcApiResponseDto;
import cn.fyg.schedule.pojo.dto.fyfc.review.FyfcAttachmentDto;
import cn.fyg.schedule.service.common.ICommonOssService;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 通用 OSS 控制器
 * 提供基础的文件上传、下载、删除功能，不依赖具体业务
 */
@Slf4j
@RestController
@RequestMapping("/api/common/oss")
public class CommonOssController {

    private final ICommonOssService commonOssService;

    public CommonOssController(ICommonOssService commonOssService) {
        this.commonOssService = commonOssService;
    }

    /**
     * 上传单个文件
     */
    @PostMapping("/upload")
    public FyfcApiResponseDto<FyfcAttachmentDto> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam("folder") String folder,
            @RequestParam("uploadBy") String uploadBy,
            @RequestParam(value = "bucketName", required = false) String bucketName) {
        
        log.info("通用上传文件请求: fileName={}, folder={}, uploadBy={}, bucket={}", 
            file.getOriginalFilename(), folder, uploadBy, bucketName);
        
        return commonOssService.uploadFile(file, folder, uploadBy, bucketName);
    }

    /**
     * 批量上传文件
     */
    @PostMapping("/upload/batch")
    public FyfcApiResponseDto<List<FyfcAttachmentDto>> uploadFiles(
            @RequestParam("files") MultipartFile[] files,
            @RequestParam("folder") String folder,
            @RequestParam("uploadBy") String uploadBy,
            @RequestParam(value = "bucketName", required = false) String bucketName) {
        
        log.info("通用批量上传文件请求: fileCount={}, folder={}, uploadBy={}, bucket={}", 
            files.length, folder, uploadBy, bucketName);
        
        return commonOssService.uploadFiles(files, folder, uploadBy, bucketName);
    }

    /**
     * 删除文件
     */
    @DeleteMapping("/delete")
    public FyfcApiResponseDto<Boolean> deleteFile(
            @RequestParam("fileKey") String fileKey,
            @RequestParam(value = "bucketName", required = false) String bucketName) {
        
        log.info("通用删除文件请求: fileKey={}, bucket={}", fileKey, bucketName);
        
        return commonOssService.deleteFile(fileKey, bucketName);
    }

    /**
     * 获取文件下载URL
     */
    @GetMapping("/url")
    public FyfcApiResponseDto<String> getFileUrl(
            @RequestParam("fileKey") String fileKey,
            @RequestParam(value = "expireSeconds", defaultValue = "3600") Integer expireSeconds,
            @RequestParam(value = "bucketName", required = false) String bucketName) {
        
        log.info("通用获取文件URL请求: fileKey={}, expireSeconds={}, bucket={}", 
            fileKey, expireSeconds, bucketName);
        
        return commonOssService.getFileUrl(fileKey, expireSeconds, bucketName);
    }

    /**
     * 检查文件是否存在
     */
    @GetMapping("/exists")
    public FyfcApiResponseDto<Boolean> fileExists(
            @RequestParam("fileKey") String fileKey,
            @RequestParam(value = "bucketName", required = false) String bucketName) {
        
        log.info("通用检查文件存在请求: fileKey={}, bucket={}", fileKey, bucketName);
        
        return commonOssService.fileExists(fileKey, bucketName);
    }

    /**
     * 文件预览（重定向到OSS URL）
     */
    @GetMapping("/preview")
    public void previewFile(
            @RequestParam("fileKey") String fileKey,
            @RequestParam(value = "expireSeconds", defaultValue = "3600") Integer expireSeconds,
            @RequestParam(value = "bucketName", required = false) String bucketName,
            javax.servlet.http.HttpServletResponse response) {
        
        log.info("通用文件预览请求: fileKey={}, bucket={}", fileKey, bucketName);
        
        try {
            FyfcApiResponseDto<String> urlResult = commonOssService.getFileUrl(fileKey, expireSeconds, bucketName);
            if (urlResult.getSuccess()) {
                response.sendRedirect(urlResult.getData());
            } else {
                response.sendError(404, "文件不存在或获取URL失败");
            }
        } catch (Exception e) {
            log.error("通用文件预览失败", e);
            try {
                response.sendError(500, "文件预览失败");
            } catch (Exception ex) {
                log.error("发送错误响应失败", ex);
            }
        }
    }

    /**
     * 生成文件键
     */
    @GetMapping("/generate-key")
    public FyfcApiResponseDto<String> generateFileKey(
            @RequestParam("originalFileName") String originalFileName,
            @RequestParam("folder") String folder,
            @RequestParam(value = "businessId", required = false) String businessId) {
        
        log.info("生成文件键请求: originalFileName={}, folder={}, businessId={}", 
            originalFileName, folder, businessId);
        
        try {
            if (StrUtil.isBlank(originalFileName)) {
                return FyfcApiResponseDto.error(400, "原始文件名不能为空");
            }
            if (StrUtil.isBlank(folder)) {
                return FyfcApiResponseDto.error(400, "文件夹路径不能为空");
            }
            
            String fileKey = commonOssService.generateFileKey(originalFileName, folder, businessId);
            return FyfcApiResponseDto.success(fileKey, "文件键生成成功");
            
        } catch (Exception e) {
            log.error("生成文件键失败", e);
            return FyfcApiResponseDto.error("生成文件键失败: " + e.getMessage());
        }
    }
}
