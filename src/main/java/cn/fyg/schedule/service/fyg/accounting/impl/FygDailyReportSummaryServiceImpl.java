package cn.fyg.schedule.service.fyg.accounting.impl;

import cn.fyg.schedule.dao.fyg.accounting.FygDailyReportSummaryRepository;
import cn.fyg.schedule.enums.fyg.accounting.BankAccountType;
import cn.fyg.schedule.enums.fyg.accounting.EquityType;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.dto.fyg.accounting.report.summary.DailyReportSummaryDTO;
import cn.fyg.schedule.pojo.dto.fyg.accounting.report.summary.DailyReportSummaryQuery;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.pojo.fyg.accounting.FygDailyReportSummary;
import cn.fyg.schedule.reponse.BaseResponse;
import cn.fyg.schedule.service.fyg.accounting.FygDailyReportSummaryService;
import cn.fyg.schedule.specification.BaseSpecification;
import cn.fyg.schedule.utils.BigDecimalUtils;
import cn.fyg.schedule.utils.SpecificationUtil;
import cn.fyg.schedule.utils.ToEntityUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Optional;

@Slf4j
@Service
public class FygDailyReportSummaryServiceImpl implements FygDailyReportSummaryService {
    private static final String COUNTERPARTY = "方远房地产集团有限公司";
    private final FygDailyReportSummaryRepository r;
    public FygDailyReportSummaryServiceImpl(FygDailyReportSummaryRepository r) {
        this.r = r;
    }
    @Override
    public BaseResponse<DailyReportSummaryDTO> findById(Integer id) {
        return null;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse<DailyReportSummaryDTO> save(DailyReportSummaryDTO value) {
        BaseResponse<DailyReportSummaryDTO> resp = new BaseResponse<>();
        try {
            FygDailyReportSummary entity = toEntity(value);
            if (entity == null) {
                resp.initialize(5001, "数据转换失败", null);
            } else {
                FygDailyReportSummary data;
                if (entity.getId() == null) {
                   data = save(entity);
                } else {
                   data = update(value, entity.getId());
                }
                resp.initialize(200, "保存成功", toDto(data));
            }
        } catch (MyException e) {
            resp.initialize(e.getCode(), e.getErrMsg(), null);
        }
        return resp;
    }

    @Override
    public BaseResponse<Page<DailyReportSummaryDTO>> filter(DailyReportSummaryQuery query, Pagination pagination) {
        BaseResponse<Page<DailyReportSummaryDTO>> resp = new BaseResponse<>();
        try {
            Sort sort = getSort(pagination);
            Pageable pageable = PageRequest.of(pagination.getCurrentPage(), pagination.getPageSize(), sort);
            Specification<FygDailyReportSummary> specification = getSpecification(query);
            Page<FygDailyReportSummary> all = getData(specification, pageable);
            Page<DailyReportSummaryDTO> result = getPage(all);
            resp.initialize(200, "查询成功", result);
        } catch (MyException e) {
            resp.initialize(e.getCode(), e.getErrMsg(), null);
        }
        return resp;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse<DailyReportSummaryDTO> setDataStatus(Integer id, Integer dataStatus, String updatedBy) {
        BaseResponse<DailyReportSummaryDTO> resp = new BaseResponse<>();
        try {
            int i = r.updateDataStatus(id, dataStatus, updatedBy);
            if (i > 0) {
                resp.initialize(200, "删除成功", null);
            } else {
                resp.initialize(3001, "删除失败", null);
            }
        } catch (JpaSystemException e) {
            resp.initialize(3001, "删除失败", null);
        }
        return resp;
    }

    @Override
    public BaseResponse<DailyReportSummaryDTO> save(DailyReportSummaryDTO value, Integer id) {
        return null;
    }

    private FygDailyReportSummary toEntity(DailyReportSummaryDTO dto) {
        FygDailyReportSummary entity = new FygDailyReportSummary();
        return ToEntityUtil.DTOtoEntity(dto, entity);
    }

    private DailyReportSummaryDTO toDto(FygDailyReportSummary entity) {
        DailyReportSummaryDTO dto = new DailyReportSummaryDTO();
        return ToEntityUtil.entityToDTO(entity, dto);
    }

    private FygDailyReportSummary save(FygDailyReportSummary entity) throws MyException {
        try {
            return r.save(entity);
        } catch (JpaSystemException e) {
            throw new MyException(3001,"保存失败");
        }
    }

    private FygDailyReportSummary update(DailyReportSummaryDTO dto, Integer id) throws MyException {
        try {
            FygDailyReportSummary source = toEntity(dto);
            FygDailyReportSummary target = r.save(source);
            BeanUtils.copyProperties(source, target, "id", "createdAt", "createdBy");
            return r.save(target);
        } catch (JpaSystemException e) {
            log.error("更新失败", e);
            throw new MyException(3001, "更新失败");
        }
    }

    private FygDailyReportSummary getById(Integer id) throws MyException {
        try {
            return r.findById(id).orElseThrow(() -> new MyException(5001, "数据不存在"));
        } catch (JpaSystemException e) {
            throw new MyException(5001, "查询失败");}
    }

    private Sort getSort(Pagination pagination) {
        if (StrUtil.isEmpty(pagination.getColumnKey()) || "false".equals(pagination.getOrder())) {
            return Sort.unsorted();
        }
        Sort.Direction direction = "ascend".equalsIgnoreCase(pagination.getOrder()) ? Sort.Direction.ASC : Sort.Direction.DESC;
        return Sort.by(direction, pagination.getColumnKey());
    }

    private Specification<FygDailyReportSummary> getSpecification(DailyReportSummaryQuery query) {
        Specification<FygDailyReportSummary> specification = Specification.where(null);
        specification = BaseSpecification.addSpecification(specification, "dataStatus", query.getDataStatus(), BaseSpecification::equals);
        specification = BaseSpecification.addSpecification(specification, "fundBalance", query.getFundBalanceJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(specification, "projectId", query.getProjectIdJsonStr(), SpecificationUtil::isInFromJson);
        specification = BaseSpecification.addSpecificationFromJson(specification, "dueDate", query.getDueDateJsonStr(), SpecificationUtil::timestampRangeFromJson);
        specification = BaseSpecification.addSpecification(specification, "repaymentPlan", query.getRepaymentPlan(), BaseSpecification::like);
        specification = BaseSpecification.addSpecification(specification, "remarks", query.getRemarks(), BaseSpecification::like);
        specification = BaseSpecification.addSpecification(specification, "projectName", query.getProjectName(), BaseSpecification::like);
        specification = BaseSpecification.addSpecificationFromJson(specification, "pureEquityRatio", query.getPureEquityRatioJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(specification, "loanBalance", query.getLoanBalanceJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(specification, "realEstateRecovered", query.getRealEstateRecoveredJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(specification, "actualUsedRealEstateFunds", query.getActualUsedRealEstateFundsJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(specification, "partnerFundsUsed", query.getPartnerFundsUsedJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(specification, "fundBalance", query.getFundBalanceJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(specification, "supervisedAccountBalance", query.getSupervisedAccountBalanceJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(specification, "saleValueWithoutBuyback", query.getSaleValueWithoutBuybackJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(specification, "saleAmountWithoutBuyback", query.getSaleAmountWithoutBuybackJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(specification, "receivedSalesFundsWithoutBuyback", query.getReceivedSalesFundsWithoutBuybackJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(specification, "recoverableFundsWithoutBuyback", query.getRecoverableFundsWithoutBuybackJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(specification, "buybackValue", query.getBuybackValueJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(specification, "reachedBuybackNode", query.getReachedBuybackNodeJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(specification, "receivedBuybackFunds", query.getReceivedBuybackFundsJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(specification, "recoverableBuybackFunds", query.getRecoverableBuybackFundsJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(specification, "operatingExpenditure", query.getOperatingExpenditureJsonStr(), SpecificationUtil::rangeFromJson);
        return specification;
    }

    private Page<DailyReportSummaryDTO> getPage(Page<FygDailyReportSummary> page) {
        return page.map(this::toDto);
    }

    private Page<FygDailyReportSummary> getData(Specification<FygDailyReportSummary> specification, Pageable pageable) throws MyException {
        try {
            return r.findAll(specification, pageable);
        } catch (JpaSystemException e) {
            throw new MyException(5001, "查询失败");
        }
    }

    private DailyReportSummaryDTO update(DailyReportSummaryDTO dto) throws MyException {
        try {
            BigDecimal realEstateInput = safeGet("获取方远房产总收入", () -> getCounterpartyTotalIncome(dto.getProjectId()));
            BigDecimal realEstateRecovered = safeGet("获取方远房产总支出", () -> getCounterpartyTotalExpense(dto.getProjectId()));
            BigDecimal actualUsedRealEstateFunds = realEstateInput.subtract(realEstateRecovered);

            BigDecimal partnerTotalIncome = safeGet("获取合作股东总收入", () -> getExcludingCounterpartyTotalIncome(dto.getProjectId()));
            BigDecimal partnerTotalExpense = safeGet("获取合作股东总支出", () -> getExcludingCounterpartyTotalExpense(dto.getProjectId()));
            BigDecimal partnerFundsUsed = partnerTotalIncome.subtract(partnerTotalExpense);

            BigDecimal totalIncome = safeGet("获取项目总收入", () -> getTotalIncome(dto.getProjectId()));
            BigDecimal totalExpense = safeGet("获取项目总支出", () -> getTotalExpense(dto.getProjectId()));
            BigDecimal fundBalance = totalIncome.subtract(totalExpense);
            BigDecimal jghTotalIncome = safeGet("获取项目监管户总收入", () -> getJghTotalIncome(dto.getProjectId()));
            BigDecimal jghTotalExpense = safeGet("获取项目监管户总支出", () -> getJghTotalExpense(dto.getProjectId()));
            BigDecimal supervisedAccountBalance = jghTotalIncome.subtract(jghTotalExpense);

            BigDecimal loanBalance = calculateDimensionBalance(dto.getProjectId(), 6, 17);
            BigDecimal totalSaleValue = safeGet("获取销售收入", () -> getTotalIncomeByProjectIdAndFundDailyDimensionId(dto.getProjectId(), 1));
            BigDecimal receivedSalesFundsWithoutBuyback = totalSaleValue.subtract(BigDecimalUtils.safe(dto.getReceivedBuybackFunds()));
            BigDecimal recoverableFundsWithoutBuyback = BigDecimalUtils.safe(dto.getSaleAmountWithoutBuyback()).subtract(receivedSalesFundsWithoutBuyback);
            BigDecimal recoverableBuybackFunds = BigDecimalUtils.safe(dto.getReachedBuybackNode()).subtract(BigDecimalUtils.safe(dto.getReceivedBuybackFunds()));
            BigDecimal operatingExpenditure = safeGet("获取经营性支出", () -> getTotalExpenseByProjectIdAndFundDailyDimensionIds(dto.getProjectId()));
            dto.setPureEquityRatio(safeGet("获取方远房产纯股比", () -> getCounterpartyEquityRatio(dto.getProjectId())));
            dto.setRealEstateInput(realEstateInput);
            dto.setRealEstateRecovered(realEstateRecovered);
            dto.setActualUsedRealEstateFunds(actualUsedRealEstateFunds);
            dto.setPartnerFundsUsed(partnerFundsUsed);
            dto.setFundBalance(fundBalance);
            dto.setSupervisedAccountBalance(supervisedAccountBalance);
            dto.setLoanBalance(loanBalance);
            dto.setReceivedSalesFundsWithoutBuyback(receivedSalesFundsWithoutBuyback);
            dto.setRecoverableFundsWithoutBuyback(recoverableFundsWithoutBuyback);
            dto.setOperatingExpenditure(operatingExpenditure);
            dto.setRecoverableBuybackFunds(recoverableBuybackFunds);
            return dto;
        } catch (MyException e) {
            throw new MyException(e.getCode(), e.getErrMsg());
        }
    }


    @Override
    public BaseResponse<DailyReportSummaryDTO> updateDailyReportSummary(DailyReportSummaryDTO dto) {
        BaseResponse<DailyReportSummaryDTO> resp = new BaseResponse<>();
        try {

            resp.initialize(200, "更新成功", this.update(dto));
        } catch (MyException e) {
            resp.initialize(e.getCode(), e.getErrMsg(), null);
        }
        return resp;
    }


    @FunctionalInterface
    public interface CheckedSupplier<T> {
        T get() throws Exception;
    }


    /**
     * 安全获取数据，统一处理异常和空值
     *
     * @param supplier 数据获取逻辑
     * @return 返回安全的 BigDecimal 值
     */
    private BigDecimal safeGet(String operationDescription, CheckedSupplier<BigDecimal> supplier) throws MyException {
        try {
            return Optional.ofNullable(supplier.get()).orElse(BigDecimal.ZERO);
        } catch (Exception e) {
            throw new MyException(5001, String.format("操作失败: %s", operationDescription));
        }
    }

    /**
     * 计算特定维度的余额
     *
     * @param projectId 项目 ID
     * @param incomeDimensionId 维度 ID
     * @return 余额
     * @throws MyException 异常包装
     */
    private BigDecimal calculateDimensionBalance(Integer projectId, Integer incomeDimensionId, Integer expenseDimensionId) throws MyException {
        try {
            BigDecimal totalIncome = safeGet(
                    String.format("获取维度%d的总收入", incomeDimensionId),
                    () -> getTotalIncomeByProjectIdAndFundDailyDimensionId(projectId, incomeDimensionId)
            );
            BigDecimal totalExpense = safeGet(
                    String.format("获取维度%d的总支出", expenseDimensionId),
                    () -> getTotalExpenseByProjectIdAndFundDailyDimensionId(projectId, expenseDimensionId)
            );
            return totalIncome.subtract(totalExpense);
        } catch (MyException e) {
            throw new MyException(5002, String.format("计算维度余额失败, projectId: %d, incomeDimensionId: %d, expenseDimensionId: %d", projectId, incomeDimensionId, expenseDimensionId));
        }
    }



    /**
     * 获取方远房产纯股比
     * @param projectId
     * @return BigDecimal
     * @throws MyException
     */
    private BigDecimal getCounterpartyEquityRatio(Integer projectId) throws MyException {
        try {
            BigDecimal equityRatio = findEquityRatioByType(projectId, EquityType.AN_GU);
            if (equityRatio == null) {
                equityRatio = findEquityRatioByType(projectId, EquityType.MING_GU);
            }
            return equityRatio;
        } catch (JpaSystemException e) {
            throw new MyException(5001, String.format("查询方远房产纯股比失败, projectId: %d", projectId));
        }
    }

    private BigDecimal findEquityRatioByType(Integer projectId, EquityType equityType) throws JpaSystemException {
        return r.findEquityRatioByProjectIdAndCounterpartyAndType(projectId, COUNTERPARTY, equityType);
    }

    /**
     * 获取方远房产总收入
     * @param projectId
     * @return
     * @throws MyException
     */
    private BigDecimal getCounterpartyTotalIncome(Integer projectId) throws MyException {
        try {
            return r.findTotalIncomeByProjectIdAndCounterpartyAndDimension(projectId, COUNTERPARTY, 3);
        } catch (JpaSystemException e) {
            throw new MyException(5001, String.format("查询方远房产总收入, projectId: %d", projectId));
        }
    }

    /**
     * 获取方远房产总支出
     * @param projectId
     * @return
     * @throws MyException
     */
    private BigDecimal getCounterpartyTotalExpense(Integer projectId) throws MyException {
        try {
            return r.findTotalExpanseByProjectIdAndCounterpartyAndDimension(projectId, COUNTERPARTY, 14);
        } catch (JpaSystemException e) {
            throw new MyException(5001, String.format("查询方远房产总支出, projectId: %d", projectId));
        }
    }

    /**
     * 获取方远房产外股东总收入
     * @param projectId
     * @return
     * @throws MyException
     */
    private BigDecimal getExcludingCounterpartyTotalIncome(Integer projectId) throws MyException {
        try {
            return r.findTotalIncomeExcludingCounterpartyByProjectIdAndDimension(projectId, COUNTERPARTY, 3);
        } catch (JpaSystemException e) {
            throw new MyException(5001, String.format("查询其他股东总收入, projectId: %d", projectId));
        }
    }


    /**
     * 获取方远房产外股东总支出
     * @param projectId
     * @return
     * @throws MyException
     */
    private BigDecimal getExcludingCounterpartyTotalExpense(Integer projectId) throws MyException {
        try {
            return r.findTotalExpanseExcludingCounterpartyByProjectIdAndDimension(projectId, COUNTERPARTY, 14);
        } catch (JpaSystemException e) {
            throw new MyException(5001, String.format("查询其他股东总支出, projectId: %d", projectId));
        }
    }

    /**
     * 获取项目总收入
     * @param projectId
     * @return
     * @throws MyException
     */
    private BigDecimal getTotalIncome(Integer projectId) throws MyException {
        try {
            return r.findTotalIncomeByProjectId(projectId);
        } catch (JpaSystemException e) {
            throw new MyException(5001, String.format("查询总收入, projectId: %d", projectId));
        }
    }

    /**
     * 获取项目总支出
     * @param projectId
     * @return
     * @throws MyException
     */
    private BigDecimal getTotalExpense(Integer projectId) throws MyException {
        try {
            return r.findTotalExpanseByProjectId(projectId);
        } catch (JpaSystemException e) {
            throw new MyException(5001, String.format("查询总支出, projectId: %d", projectId));
        }
    }

    /**
     * 获取项目监管户总收入
     * @param projectId
     * @return
     * @throws MyException
     */
    private BigDecimal getJghTotalIncome(Integer projectId) throws MyException {
        try {
            return r.findTotalIncomeByProjectIdAndAccountType(projectId, BankAccountType.JIAN_GUAN_HU);
        } catch (JpaSystemException e) {
            throw new MyException(5001, String.format("查询监管户总收入, projectId: %d", projectId));
        }
    }

    /**
     * 获取项目监管户总支出
     * @param projectId
     * @return
     * @throws MyException
     */
    private BigDecimal getJghTotalExpense(Integer projectId) throws MyException {
        try {
            return r.findTotalExpanseByProjectIdAndAccountType(projectId, BankAccountType.JIAN_GUAN_HU);
        } catch (JpaSystemException e) {
            throw new MyException(5001, String.format("查询监管户总支出, projectId: %d", projectId));
        }
    }

    /**
     * 获取6 融资流入 1 销售收入
     * @param projectId
     * @param dimensionId
     * @return
     * @throws MyException
     */
    private BigDecimal getTotalIncomeByProjectIdAndFundDailyDimensionId(Integer projectId, Integer dimensionId) throws MyException {
        try {
            return r.findTotalIncomeByProjectIdAndFundDailyDimensionId(projectId, dimensionId);
        } catch (JpaSystemException e) {
            throw new MyException(5001, String.format("查询资金维度收入, dimensionId: %d, projectId: %d", dimensionId, projectId));
        }
    }

    /**
     * 获取 17 融资还贷支出
     * @param projectId
     * @param dimensionId
     * @return
     * @throws MyException
     */
    private BigDecimal getTotalExpenseByProjectIdAndFundDailyDimensionId(Integer projectId, Integer dimensionId) throws MyException {
        try {
            return r.findTotalExpanseByProjectIdAndFundDailyDimensionId(projectId, dimensionId);
        } catch (JpaSystemException e) {
            throw new MyException(5001, String.format("查询资金维度支出, dimensionId: %d, projectId: %d", dimensionId, projectId));
        }
    }

    /**
     * 获取 土地支出、工程支出、税金支出、销售费用、管理费用、财务费用、融资费用的累计数合计
     * @param projectId
     * @return
     * @throws MyException
     */
    private BigDecimal getTotalExpenseByProjectIdAndFundDailyDimensionIds(Integer projectId) throws MyException {
        try {
            return r.findTotalExpenseByProjectIdAndDimensions(projectId, Arrays.asList(7, 8, 9, 10, 11, 12, 18));
        } catch (JpaSystemException e) {
            throw new MyException(5001, String.format("查询土地支出、工程支出、税金支出、销售费用、管理费用、财务费用、融资费用的累计数合计失败, projectId: %d", projectId));
        }
    }
}
