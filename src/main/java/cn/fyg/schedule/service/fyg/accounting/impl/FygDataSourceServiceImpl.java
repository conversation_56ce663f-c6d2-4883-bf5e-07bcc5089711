package cn.fyg.schedule.service.fyg.accounting.impl;

import cn.fyg.schedule.dao.fyg.accounting.FygDataSourceRepository;
import cn.fyg.schedule.enums.fyg.accounting.CounterpartyAccountType;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.dto.fyg.accounting.DataSourceDTO;
import cn.fyg.schedule.pojo.dto.fyg.accounting.DataSourceQuery;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.pojo.fyg.accounting.FygDataSource;
import cn.fyg.schedule.reponse.BaseResponse;
import cn.fyg.schedule.service.fyg.accounting.FygDataSourceService;
import cn.fyg.schedule.specification.BaseSpecification;
import cn.fyg.schedule.utils.SpecificationUtil;
import cn.fyg.schedule.utils.ToEntityUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;

@Service
@Slf4j
public class FygDataSourceServiceImpl implements FygDataSourceService {
    private final FygDataSourceRepository r;
    public FygDataSourceServiceImpl(FygDataSourceRepository r) {
        this.r = r;
    }
    @Override
    public BaseResponse<DataSourceDTO> findById(Integer id) {
        return null;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse<DataSourceDTO> save(DataSourceDTO value) {
        BaseResponse<DataSourceDTO> resp = BaseResponse.initialized();
        try {
            FygDataSource entity = toEntity(value);
            if (entity == null) {
                resp.initialize(5001, "数据转换失败", null);
            } else {
                try {
                    FygDataSource data = save(entity);
                    resp.initialize(200, "保存成功", toDto(data));
                } catch (MyException e) {
                    resp.initialize(e.getCode(), e.getErrMsg(), null);
                }
            }
        } catch (Exception e) {
            resp.initialize(5001, "保存失败", null);
        }
        return resp;
    }

    @Override
    public BaseResponse<Page<DataSourceDTO>> filter(DataSourceQuery query, Pagination pagination) {
        BaseResponse<Page<DataSourceDTO>> resp = new BaseResponse<>();
        try {
            Sort sort = getSort(pagination);
            Pageable pageable = PageRequest.of(pagination.getCurrentPage(), pagination.getPageSize(), sort);
            Specification<FygDataSource> specification = getSpecification(query);
            Page<FygDataSource> all = getData(specification, pageable);
            Page<DataSourceDTO> result = getPage(all);
            resp.initialize(200, "查询成功", result);
        } catch (MyException e) {
            resp.initialize(e.getCode(), e.getErrMsg(), null);
        }
        return resp;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse<DataSourceDTO> setDataStatus(Integer id, Integer dataStatus, String updatedBy) {
        BaseResponse<DataSourceDTO> resp = BaseResponse.initialized();
        try {
            int i = r.updateDataStatus(id, dataStatus, updatedBy);
            if (i > 0) {
                resp.initialize(200, "删除成功", null);
            } else {
                resp.initialize(3001, "删除失败", null);
            }
        } catch (JpaSystemException e) {
            resp.initialize(3001, "删除失败", null);
        }
        return resp;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse<DataSourceDTO> save(DataSourceDTO value, Integer id) {
        BaseResponse<DataSourceDTO> resp = BaseResponse.initialized();
        try {
            FygDataSource data = update(value, id);
            resp.initialize(200, "更新成功", toDto(data));
        } catch (MyException e) {
            resp.initialize(e.getCode(), e.getErrMsg(), null);
        }
        return resp;
    }

    private FygDataSource toEntity(DataSourceDTO dto) {
        FygDataSource entity = new FygDataSource();
        return ToEntityUtil.DTOtoEntity(dto, entity);
    }

    private DataSourceDTO toDto(FygDataSource entity) {
        DataSourceDTO dto = new DataSourceDTO();
        return ToEntityUtil.entityToDTO(entity, dto);
    }

    private FygDataSource save(FygDataSource entity) throws MyException {
        try {
            return r.save(entity);
        } catch (JpaSystemException e) {
            throw new MyException(3001, "保存失败");
        }
    }

    private FygDataSource update(DataSourceDTO dto, Integer id) throws MyException {
        try {
            FygDataSource source = toEntity(dto);
            FygDataSource target = getById(id);
            BeanUtils.copyProperties(source, target, "id", "createdAt", "createdBy");
            return r.save(target);
        } catch (JpaSystemException e) {
            log.error("更新失败", e);
            throw new MyException(3001, "更新失败");
        }
    }

    private FygDataSource getById(Integer id) throws MyException {
        try {
            return r.findById(id).orElseThrow(() -> new MyException(5001, "数据不存在"));
        } catch (JpaSystemException e) {
            throw new MyException(5001, "查询失败");}
    }


    private Sort getSort(Pagination pagination) {
        if (StrUtil.isEmpty(pagination.getColumnKey()) || "false".equals(pagination.getOrder())) {
            return Sort.unsorted();
        }
        Sort.Direction direction = "ascend".equalsIgnoreCase(pagination.getOrder()) ? Sort.Direction.ASC : Sort.Direction.DESC;
        return Sort.by(direction, pagination.getColumnKey());
    }

    private Specification<FygDataSource> getSpecification(DataSourceQuery query) {
        Specification<FygDataSource> specification = Specification.where(null);
        specification = BaseSpecification.addSpecification(specification, "dataStatus", query.getDataStatus(), BaseSpecification::equals);
        specification = BaseSpecification.addSpecification(specification, "projectId", query.getProjectId(), BaseSpecification::equals);
        specification = BaseSpecification.addSpecification(specification, "bankAccountName", query.getBankAccountName(), BaseSpecification::like);
        specification = BaseSpecification.addSpecification(specification, "bankAccountNumber", query.getBankAccountNumber(), BaseSpecification::like);
        specification = BaseSpecification.addSpecification(specification, "bankAbbreviation", query.getBankAbbreviation(), BaseSpecification::like);
        specification = BaseSpecification.addSpecification(specification, "counterpartyName", query.getCounterpartyName(), BaseSpecification::like);
        specification = BaseSpecification.addSpecification(specification, "fundDailyDimensionId", query.getFundDailyDimensionIdJsonStr(), SpecificationUtil::isInFromJson);
        specification = BaseSpecification.addSpecification(specification, "taxDetail", query.getTaxDetail(), BaseSpecification::like);
        specification = BaseSpecification.addSpecification(specification, "transactionDate", query.getTransactionDateJsonStr(), SpecificationUtil::timestampRangeFromJson);
        specification = BaseSpecification.addSpecification(specification, "incomeAmount", query.getIncomeAmountJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecification(specification, "expenseAmount", query.getExpenseAmountJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecification(specification, "annualInterestRate", query.getAnnualInterestRateJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecification(
                specification,
                "counterpartyType",
                query.getCounterpartyTypeJsonStr(),
                CounterpartyAccountType.class,
                (attribute, value) -> SpecificationUtil.isInFromJson4Enum(attribute, value, CounterpartyAccountType.class)
        );
        specification = BaseSpecification.addSpecification(specification, "purposeDetail", query.getPurposeDetail(), BaseSpecification::like);
        specification = BaseSpecification.addSpecification(specification, "counterpartyAccountNumber", query.getCounterpartyAccountNumber(), BaseSpecification::like);
        specification = BaseSpecification.addSpecification(specification, "counterpartyBankName", query.getCounterpartyBankName(), BaseSpecification::like);
        specification = BaseSpecification.addSpecification(specification, "secondaryDetail", query.getSecondaryDetail(), BaseSpecification::like);
        specification = BaseSpecification.addSpecification(specification, "tertiaryDetail", query.getTertiaryDetail(), BaseSpecification::like);
        specification = BaseSpecification.addSpecification(specification, "remark", query.getRemark(), BaseSpecification::like);


        return specification;
    }

    private Page<DataSourceDTO> getPage(Page<FygDataSource> page) {
        return page.map(this::toDto);
    }

    private Page<FygDataSource> getData(Specification<FygDataSource> specification, Pageable pageable) throws MyException {
        try {
            return r.findAll(specification, pageable);
        } catch (JpaSystemException e) {
            throw new MyException(5001, "查询失败");
        }
    }
}
