package cn.fyg.schedule.service.statement.capital;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.statement.capital.FCapitalStatementBalance;

public interface FCapitalStatementBalanceService {
    BaseResponse save(FCapitalStatementBalance data);
    BaseResponse delete(Integer id);
    BaseResponse list(Integer id);
    BaseResponse findById(Integer id);
    Double sumCurrentDayIncome(Integer id);
    Double cumCurrentDayIncome(Integer id);
    BaseResponse getHistoryAmount(Integer id, String account, String subject);
    BaseResponse listAccount(Integer[] ids);
}
