package cn.fyg.schedule.controller.yuanxin;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.project.yx.progressCheck.YxProgressCheckDto;
import cn.fyg.schedule.pojo.dto.project.yx.progressCheck.YxProgressCheckRecordDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.pojo.project.yuanXin.progressCheck.YxProgressCheckItem;
import cn.fyg.schedule.pojo.project.yuanXin.progressCheck.YxProgressCheckRecordItem;
import cn.fyg.schedule.pojo.project.yuanXin.progressCheck.YxProgressCheckRecordSign;
import cn.fyg.schedule.pojo.project.yuanXin.progressCheck.YxProgressCheckSign;
import cn.fyg.schedule.service.sheets.yx.progressCheck.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("yx/progress/check")
public class YxProgressCheckController {
    private final YxProgressCheckService service;
    private final YxProgressCheckRecordService recordService;
    private final YxProgressCheckItemService itemService;
    private final YxProgressCheckSignService signService;
    private final YxProgressCheckRecordItemService recordItemService;
    private final YxProgressCheckRecordSignService recordSignService;

    public YxProgressCheckController(YxProgressCheckService service, YxProgressCheckRecordService recordService, YxProgressCheckItemService itemService, YxProgressCheckSignService signService, YxProgressCheckRecordItemService recordItemService, YxProgressCheckRecordSignService recordSignService) {
        this.service = service;
        this.recordService = recordService;
        this.itemService = itemService;
        this.signService = signService;
        this.recordItemService = recordItemService;
        this.recordSignService = recordSignService;
    }

    @PostMapping("filter")
    public BaseResponse filter(YxProgressCheckDto dto, Pagination pagination) {
        return service.filter(dto, pagination);
    }

    @PostMapping("list/record/creator/phone")
    public BaseResponse listProgressByRecordCreatorPhone(String creatorPhone) {
        BaseResponse baseResponse = BaseResponse.initialize();
        List<Integer> ids = recordService.listParentIdByCreatorPhone(creatorPhone);
        if (ids != null) {
            baseResponse = service.findByIdIn(ids);
        } else {
            baseResponse.setCode(1);
            baseResponse.setMsg("没有查到相关记录");
        }
        return baseResponse;
    }

    @PostMapping("history/inspection/department")
    public BaseResponse listHistoryInspectionDepartment() {
        return service.listInspectionDepartmentHistory();
    }

    @PostMapping("find/id")
    public BaseResponse findById(Integer id) {
        return service.findById(id);
    }

    @PostMapping ("save")
    public BaseResponse saveProgressCheck(YxProgressCheckDto dto) {
        return service.save(dto);
    }

    @PostMapping("delete")
    public BaseResponse deleteProgressCheck(Integer id) {
        return service.delete(id);
    }

    @PostMapping("find/item")
    public BaseResponse findItems(Integer id) {
        return itemService.findByParentId(id);
    }

    @PostMapping("delete/item")
    public BaseResponse deleteItem(Integer id) {
        return itemService.delete(id);
    }

    @PostMapping("save/item")
    public BaseResponse saveItem(YxProgressCheckItem data) {
        return itemService.save(data);
    }

    @PostMapping("find/signature")
    public BaseResponse findSignature(Integer id) {
        return signService.findByParentId(id);
    }

    @PostMapping("delete/signature")
    public BaseResponse deleteSignature(Integer id) {
        return signService.delete(id);
    }

    @PostMapping("save/signature")
    public BaseResponse saveSignature(YxProgressCheckSign data) {
        return signService.save(data);
    }

    @PostMapping("save/record")
    public BaseResponse saveRecord(YxProgressCheckRecordDto dto) {
        return recordService.save(dto);
    }

    @PostMapping("find/record")
    public BaseResponse findRecord(Integer id) {
        return recordService.findByParentId(id);
    }

    @PostMapping("delete/record")
    public BaseResponse deleteRecord(Integer id) {
        return recordService.delete(id);
    }

    @PostMapping("find/record/item")
    public BaseResponse findRecordItem(Integer id) {
        return recordItemService.findByParentId(id);
    }

    @PostMapping("save/record/item")
    public BaseResponse saveRecordItem(YxProgressCheckRecordItem data) {
        return recordItemService.save(data);
    }

    @PostMapping("delete/record/item")
    public BaseResponse deleteRecordItem(Integer id) {
        return recordItemService.delete(id);
    }

    @PostMapping("find/record/sign")
    public BaseResponse findRecordSign(Integer id) {
        return recordSignService.findByParentId(id);
    }

    @PostMapping("save/record/sign")
    public BaseResponse saveRecordSign(YxProgressCheckRecordSign data) {
        return recordSignService.save(data);
    }

    @PostMapping("delete/record/sign")
    public BaseResponse deleteRecordSign(Integer id) {
        return recordSignService.deleteByParentId(id);
    }
}
