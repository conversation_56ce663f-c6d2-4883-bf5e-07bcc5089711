package cn.fyg.schedule.controller.mini.app;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.FAssignmentStatus;
import cn.fyg.schedule.pojo.dto.FAssignmentStatusDto;
import cn.fyg.schedule.pojo.dto.query.FAssignmentQuery;
import cn.fyg.schedule.service.FAssignmentStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("assignmentstatus")
public class FAssignmentStatusController {
    @Autowired private FAssignmentStatusService service;
    @PostMapping("save")
    public BaseResponse save(FAssignmentStatusDto dto) {
        FAssignmentStatus status = FAssignmentStatus.initialize(dto);
        return service.save(status);
    }

    @PostMapping("delete")
    public BaseResponse delete(Integer id) {
        return service.delete(id);
    }

    @PostMapping("status")
    public BaseResponse getList(FAssignmentQuery query) {
        return service.findByAssignmentId(query.getAssignmentId());
    }

}
