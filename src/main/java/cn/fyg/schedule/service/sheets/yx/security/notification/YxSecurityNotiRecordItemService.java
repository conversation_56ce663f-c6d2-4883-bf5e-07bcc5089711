package cn.fyg.schedule.service.sheets.yx.security.notification;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.project.yuanXin.security.notification.YxSecurityNotiRecordItem;

public interface YxSecurityNotiRecordItemService {
    BaseResponse save(YxSecurityNotiRecordItem data);
    BaseResponse delete(Integer id);
    BaseResponse findById(Integer id);
    BaseResponse findByParentId(Integer id);
}
