package cn.fyg.schedule.specification;

import cn.fyg.schedule.pojo.FSchedule;
import org.springframework.data.jpa.domain.Specification;

public class FScheduleSpecification {
    public static Specification<FSchedule> ifContentEq(String column, String expression) {
        return ((root, query, builder) -> builder.equal(root.get(column), expression));
    }
    public static Specification<FSchedule> ifContentEq(String column, Integer expression) {
        return ((root, query, builder) -> builder.equal(root.get(column), expression));
    }
}
