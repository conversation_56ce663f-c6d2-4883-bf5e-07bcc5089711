package cn.fyg.schedule.utils;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.lang.reflect.Field;
import java.util.Date;

@Slf4j
public class ToEntityUtil {
    public static <T, D> T DTOtoEntity(D dto, T entity) {
        if (entity == null || dto == null) {
            log.warn("Entity or DTO is null");
            return null;
        }
        try {
            BeanUtils.copyProperties(dto, entity);

            // Special handling for Date fields in entity
            Field[] entityFields = entity.getClass().getDeclaredFields();
            Field[] dtoFields = dto.getClass().getDeclaredFields();

            for (Field entityField : entityFields) {
                if (entityField.getType().equals(Date.class)) {
                    entityField.setAccessible(true);
                    String fieldName = entityField.getName();
                    for (Field dtoField : dtoFields) {
                        if (dtoField.getName().equals(fieldName) && dtoField.getType().equals(Long.class)) {
                            dtoField.setAccessible(true);
                            Long timestamp = (Long) dtoField.get(dto);
                            if (timestamp != null) {
                                entityField.set(entity, DateUtil.date(timestamp));
                            }
                            break;
                        }
                    }
                } else if (entityField.getType().isEnum()) {
                    entityField.setAccessible(true);
                    String fieldName = entityField.getName();
                    for (Field dtoField : dtoFields) {
                        if (dtoField.getName().equals(fieldName) && dtoField.getType().equals(String.class)) {
                            dtoField.setAccessible(true);
                            String enumValue = (String) dtoField.get(dto);
                            if (enumValue != null) {
                                try {
                                    // 使用更为类型安全的泛型方式
                                    Class<?> enumTypeRaw = entityField.getType();
                                    if (enumTypeRaw.isEnum()) {
                                        @SuppressWarnings("unchecked")
                                        Class<? extends Enum<?>> enumType = (Class<? extends Enum<?>>) enumTypeRaw;
                                        Enum<?> enumConstant = Enum.valueOf(enumType.asSubclass(Enum.class), enumValue);
                                        entityField.set(entity, enumConstant);
                                        break;
                                    }
                                } catch (IllegalArgumentException e) {
                                    log.error("Invalid enum value: {} for field: {}", enumValue, fieldName);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error copying properties from DTO to Entity", e);
            return null;
        }
        return entity;
    }

    public static <D, T> D entityToDTO(T entity, D dto) {
        if (entity == null || dto == null) {
            log.warn("Entity or DTO is null");
            return null;
        }
        try {
            BeanUtils.copyProperties(entity, dto);

            // Special handling for Long fields in DTO
            Field[] entityFields = entity.getClass().getDeclaredFields();
            Field[] dtoFields = dto.getClass().getDeclaredFields();

            for (Field entityField : entityFields) {
                if (entityField.getType().equals(Date.class)) {
                    entityField.setAccessible(true);
                    String fieldName = entityField.getName();
                    Date date = (Date) entityField.get(entity);
                    if (date != null) {
                        for (Field dtoField : dtoFields) {
                            if (dtoField.getName().equals(fieldName) && dtoField.getType().equals(Long.class)) {
                                dtoField.setAccessible(true);
                                dtoField.set(dto, date.getTime());
                                break;
                            }
                        }
                    }
                } else if (entityField.getType().isEnum()) {
                    entityField.setAccessible(true);
                    String fieldName = entityField.getName();
                    Enum<?> enumValue = (Enum<?>) entityField.get(entity);
                    if (enumValue != null) {
                        for (Field dtoField : dtoFields) {
                            if (dtoField.getName().equals(fieldName) && dtoField.getType().equals(String.class)) {
                                dtoField.setAccessible(true);
                                dtoField.set(dto, enumValue.name());
                                break;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error copying properties from Entity to DTO", e);
            return null;
        }
        return dto;
    }
}
