package cn.fyg.schedule.service.fyapp.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.fyapp.FAppUserSettingRepository;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.dto.fyapp.FAppUserSettingDto;
import cn.fyg.schedule.pojo.fyapp.FAppUserSetting;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.fyapp.FAppUserSettingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
public class FAppUserSettingServiceImpl implements FAppUserSettingService {
    private final FAppUserSettingRepository r;

    public FAppUserSettingServiceImpl(FAppUserSettingRepository r) {
        this.r = r;
    }

    @Override
    public BaseResponse save(FAppUserSettingDto dto) {
        BaseResponse response = BaseResponse.initialize();
        try {
            FAppUserSetting data = FAppUserSetting.initialized(dto);
            if (dto.getId() == null) {
                FAppUserSetting tmp = getSetting(dto.getUserId(), dto.getAppKey(), dto.getAppRouter());
                if (tmp == null) {
                    response.setResult(BaseService.saveWithR(data, r, FAppUserSetting.class));
                } else {
                    response.setMsg("操作" + update(tmp.getId(), dto.getAppSetting()) + "条数据成功。");
                }
            } else {
                response.setMsg("操作" + update(dto.getId(), dto.getAppSetting()) + "条数据成功。");
            }
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        } catch (Exception e) {
            response.setMsg("系统错误");
            response.setCode(500);
        }
        return response;
    }

    @Override
    public BaseResponse ifHas(FAppUserSettingDto dto) {
        BaseResponse response = BaseResponse.initialize();
        try {
            FAppUserSetting data = getSetting(dto.getUserId(), dto.getAppKey(), dto.getAppRouter());
            if (data != null) {
                response.setResult(data);
            } else {
                response.setMsg("未查到配置");
                response.setCode(1);
            }
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }

    private Integer update(Integer id, String appSetting) throws MyException {
        try {
            return r.update(id, appSetting);
        } catch (JpaSystemException e) {
            throw new MyException(500, "更新错误");
        }
    }

    private FAppUserSetting getSetting(String userId, String appKey, String appRouter) throws MyException {
        try {
            Optional<FAppUserSetting> optional = r.findByUserIdAndAppKeyAndAppRouter(userId, appKey, appRouter);
            return optional.orElse(null);
        } catch (JpaSystemException e) {
            throw new MyException(500, "查询错误");
        }
    }
}
