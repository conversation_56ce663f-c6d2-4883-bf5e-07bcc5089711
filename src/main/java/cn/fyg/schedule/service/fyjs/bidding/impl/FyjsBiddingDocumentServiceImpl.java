package cn.fyg.schedule.service.fyjs.bidding.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.fyjs.bidding.FyjsBiddingDocumentRepository;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.fyjs.bidding.FyjsBiddingDocument;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.fyjs.bidding.FyjsBiddingDocumentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class FyjsBiddingDocumentServiceImpl implements FyjsBiddingDocumentService {
    private final FyjsBiddingDocumentRepository r;

    public FyjsBiddingDocumentServiceImpl(FyjsBiddingDocumentRepository r) {
        this.r = r;
    }

    @Override
    public BaseResponse save(FyjsBiddingDocument data) {
        return BaseService.save(data, r, FyjsBiddingDocument.class);
    }

    @Override
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findByParentId(Integer parentId) {
        BaseResponse response = BaseResponse.initialize();
        try {
            response.setResult(getByParentId(parentId));
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }

    private List<FyjsBiddingDocument> getByParentId(Integer parentId) throws MyException {
        try {
            return r.findByParentId(parentId);
        } catch (JpaSystemException e) {
            log.info(e.getMessage());
            throw new MyException(500, "查询失败");
        }
    }
}
