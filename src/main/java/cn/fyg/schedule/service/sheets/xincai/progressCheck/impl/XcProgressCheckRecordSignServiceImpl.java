package cn.fyg.schedule.service.sheets.xincai.progressCheck.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.xincai.progressCheck.XcProgressCheckRecordSignRepository;
import cn.fyg.schedule.pojo.project.xincai.progressCheck.XcProgressCheckRecordSign;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.sheets.xincai.progressCheck.XcProgressCheckRecordSignService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;

@Service
@Slf4j
public class XcProgressCheckRecordSignServiceImpl implements XcProgressCheckRecordSignService {
    private final XcProgressCheckRecordSignRepository r;

    public XcProgressCheckRecordSignServiceImpl(XcProgressCheckRecordSignRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(XcProgressCheckRecordSign data) {
        return BaseService.save(data, r, XcProgressCheckRecordSign.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findByParentId(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        List<XcProgressCheckRecordSign> data = getItems(id);
        if (data != null) {
            baseResponse.setResult(data);
        } else {
            baseResponse.setCode(1);
            baseResponse.setMsg("查询失败");
        }
        return baseResponse;
    }

    private List<XcProgressCheckRecordSign> getItems(Integer id) {
        try {
            List<XcProgressCheckRecordSign> items = r.findByParentId(id);
            if (!items.isEmpty()) {
                return items;
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse deleteByParentId(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            r.deleteByParentId(id);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg("发生错误:" + e.getMessage());
        }
        return baseResponse;
    }
}
