package cn.fyg.schedule.pojo.fyjs.social.insurance;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.util.Date;
@Entity
@Setter
@Getter
@ToString
@Table(name = "fyjs_social_insurance_item")
public class FyjsSocialInsuranceItem {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Column(name = "parent_id")
    private Integer parentId;
    private String type;
    private String name;
    private String content;
    private String label;
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
}
