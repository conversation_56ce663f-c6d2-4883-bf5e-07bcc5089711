package cn.fyg.schedule.utils;

import cn.fyg.schedule.specification.BaseSpecification;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.domain.Specification;

import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.lang.reflect.Type;
import java.text.MessageFormat;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class SpecificationUtil {
    public static String contains(String expression) {
        return MessageFormat.format("%{0}%", expression);
    }

    private static final Gson gson = new Gson();

    /**
     * Parses JSON string into a list of objects and creates a specification for the IN clause.
     *
     * @param attribute  the attribute of the entity to check
     * @param jsonValues the JSON string representing the list of values
     * @return the specification or null if there is an error in parsing
     */
    public static <T> Specification<T> isInFromJson(String attribute, String jsonValues) {
        if (isValidJson(jsonValues)) return null;
        try {
            Type listType = new TypeToken<List<Object>>() {
            }.getType();
            List<Object> values = gson.fromJson(jsonValues, listType);
            // 检查并调整类型
            Object[] adjustedValues = values.stream()
                    .map(SpecificationUtil::adjustType)
                    .toArray();

            return BaseSpecification.isIn(attribute, adjustedValues);
        } catch (Exception e) {
            log.error("Error processing JSON for specification", e);
            return null;
        }
    }

    public static <T, E extends Enum<E>> Specification<T> isInFromJson4Enum(String attribute, String jsonValues, Class<E> enumClass) {
        if (isValidJson(jsonValues)) return null;
        try {
            Type listType = new TypeToken<List<String>>() {}.getType();
            List<String> values = gson.fromJson(jsonValues, listType);

            E[] enumArray = values.stream()
                    .map(value -> Enum.valueOf(enumClass, value))
                    .toArray(size -> createGenericArray(enumClass, size));

            return BaseSpecification.isIn(attribute, (Object[]) enumArray);
        } catch (Exception e) {
            log.error("Error processing JSON for specification: {}", jsonValues, e);
            return null;
        }
    }

    @SuppressWarnings("unchecked")
    private static <E extends Enum<E>> E[] createGenericArray(Class<E> enumClass, int size) {
        return (E[]) Array.newInstance(enumClass, size);
    }


    private static Object adjustType(Object value) {
        // 示例：假设我们需要的是 Integer 类型，可以根据实际需求调整
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return value;
    }

    public static <T> Specification<T> isInFromJson(String attribute, String jsonValues, Class<T> entityClass) {
        if (isValidJson(jsonValues)) return null;
        try {
            Field field = entityClass.getDeclaredField(attribute);
            Class<?> fieldType = field.getType();

            Type listType = new TypeToken<List<Object>>() {
            }.getType();
            List<Object> values = gson.fromJson(jsonValues, listType);

            if (fieldType.equals(Integer.class)) {
                // Convert Double to Integer
                return BaseSpecification.isIn(attribute, values.stream()
                        .map(value -> ((Number) value).intValue()).toArray());
            } else if (fieldType.equals(String.class)) {
                return BaseSpecification.isIn(attribute, values.toArray());
            } else {
                throw new IllegalArgumentException("Unsupported attribute type for JSON conversion");
            }
        } catch (NoSuchFieldException | SecurityException e) {
            log.error("Field not found in entity class", e);
            return null;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public static <T> Specification<T> rangeFromJson(String attribute, String jsonValues) {
        if (isValidJson(jsonValues)) return null;

        try {
            List<Double> numbers = gson.fromJson(jsonValues, new TypeToken<List<Double>>() {
            }.getType());
            Double min = !numbers.isEmpty() ? numbers.get(0) : null;
            Double max = numbers.size() > 1 ? numbers.get(1) : null;
            return BaseSpecification.byRange(attribute, min, max);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }


    public static <T> Specification<T> timestampRangeFromJson(String attribute, String jsonValues) {
        if (isValidJson(jsonValues)) return null;
        try {
            List<Long> timestamps = gson.fromJson(jsonValues, new TypeToken<List<Long>>() {
            }.getType());
            Long startTime = !timestamps.isEmpty() ? timestamps.get(0) : null;
            Long endTime = timestamps.size() > 1 ? timestamps.get(1) : null;
            return BaseSpecification.byTimestampRange(attribute, startTime, endTime);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    private static boolean isValidJson(String jsonValues) {
        if (!isValidJsonArray(jsonValues)) {
            log.error("Invalid JSON array: {}", jsonValues);
            return true;
        }
        return false;
    }

    private static boolean isValidJsonArray(String json) {
        try {
            JsonElement jsonElement = JsonParser.parseString(json);
            return jsonElement.isJsonArray();
        } catch (Exception e) {
            return false;
        }
    }
}
