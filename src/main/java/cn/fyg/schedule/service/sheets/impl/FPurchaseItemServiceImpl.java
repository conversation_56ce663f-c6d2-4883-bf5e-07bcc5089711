package cn.fyg.schedule.service.sheets.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.FPurchaseItemRepository;
import cn.fyg.schedule.pojo.FPurchaseItem;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.sheets.FPurchaseItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class FPurchaseItemServiceImpl implements FPurchaseItemService {
    @Autowired private FPurchaseItemRepository r;
    @Override
    @Transactional
    public BaseResponse save(FPurchaseItem data) {
        return BaseService.save(data, r, FPurchaseItem.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse list(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FPurchaseItem> result = r.findByAppidOrderByNumber(id);
            baseResponse.setResult(result);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Optional<FPurchaseItem> result = r.findById(id);
            if (result.isPresent()) {
                baseResponse.setResult(result.get());
            }
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }
}
