package cn.fyg.schedule.service.wsvoucher.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.service.eas.openApi.EASService;
import cn.fyg.schedule.service.wsvoucher.WSRtnInfo;
import cn.fyg.schedule.service.wsvoucher.WSVoucherInfo;
import cn.fyg.schedule.service.wsvoucher.WSVoucherService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Optional;

@Service
public class WSVoucherServiceImpl implements WSVoucherService {
    private final EASService easService;

    public WSVoucherServiceImpl(EASService easService) {
        this.easService = easService;
    }

    @Override
    public BaseResponse importVoucher(WSVoucherInfo voucherInfo, boolean isTempSave, boolean isVerify, boolean hasCashFlow) {
        ArrayList<Object> param = new ArrayList<>();
        param.add(voucherInfo);
        param.add(true);
        param.add(true);
        param.add(false);

        WSRtnInfo ret = easService.callRetObject("WSVoucher-importVoucher", param, WSRtnInfo.class);
        BaseResponse response = new BaseResponse();
        if (ret != null) {
            response.setResult(ret);
        } else {
            response.setCode(401);
            response.setMsg("生成凭证后台出错");
        }

        return response;
    }
}
