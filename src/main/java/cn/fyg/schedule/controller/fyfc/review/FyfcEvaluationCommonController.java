package cn.fyg.schedule.controller.fyfc.review;

import cn.fyg.schedule.pojo.dto.fyfc.review.*;
import cn.fyg.schedule.service.fyfc.review.IFyfcEvaluationCommonService;
import cn.fyg.schedule.service.fyfc.review.IFyfcEvaluationScoreService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * FYFC 评价通用控制器
 * 提供各个模块共用的 API
 */
@Slf4j
@RestController
@RequestMapping("/api/fyfc/evaluation/common")
@RequiredArgsConstructor
public class FyfcEvaluationCommonController {

    private final IFyfcEvaluationCommonService commonService;
    private final IFyfcEvaluationScoreService scoreService;

    // ==================== 基础数据 API ====================

    /**
     * 根据ID获取评价基本信息
     */
    @GetMapping("/{id}")
    public FyfcApiResponseDto<FyfcEvaluationDto> getEvaluationById(@PathVariable Integer id) {
        log.info("获取评价基本信息: id={}", id);
        return commonService.getEvaluationById(id);
    }

    /**
     * 获取评价的所有得分记录
     */
    @GetMapping("/{id}/scores")
    public FyfcApiResponseDto<List<FyfcEvaluationScoreDto>> getEvaluationScores(@PathVariable Integer id) {
        log.info("获取评价得分记录: id={}", id);
        return commonService.getEvaluationScores(id);
    }

    /**
     * 获取评价的状态变更历史
     */
    @GetMapping("/{id}/history")
    public FyfcApiResponseDto<List<FyfcEvaluationStatusHistoryDto>> getEvaluationStatusHistory(@PathVariable Integer id) {
        log.info("获取评价状态历史: id={}", id);
        return commonService.getEvaluationStatusHistory(id);
    }

    /**
     * 获取部门列表
     */
    @GetMapping("/departments")
    public FyfcApiResponseDto<List<String>> getDepartmentList() {
        log.info("获取部门列表");
        return commonService.getDepartmentList();
    }

    /**
     * 获取评价状态选项
     */
    @GetMapping("/status-options")
    public FyfcApiResponseDto<List<FyfcEvaluationStatusDto>> getStatusOptions() {
        log.info("获取状态选项");
        return commonService.getStatusOptions();
    }

    /**
     * 获取用户角色选项
     */
    @GetMapping("/role-options")
    public FyfcApiResponseDto<List<FyfcUserRoleDto>> getUserRoleOptions() {
        log.info("获取角色选项");
        return commonService.getUserRoleOptions();
    }

    // ==================== 验证和检查 API ====================

    /**
     * 验证评价数据完整性
     */
    @PostMapping("/{id}/validate")
    public FyfcApiResponseDto<Boolean> validateEvaluationData(@PathVariable Integer id) {
        log.info("验证评价数据: id={}", id);
        return commonService.validateEvaluationData(id);
    }

    /**
     * 计算评价总分
     */
    @GetMapping("/{id}/total-score")
    public FyfcApiResponseDto<Object> calculateTotalScore(@PathVariable Integer id) {
        log.info("计算评价总分: id={}", id);
        return commonService.calculateTotalScore(id);
    }

    /**
     * 获取评价流程状态
     */
    @GetMapping("/{id}/process")
    public FyfcApiResponseDto<Object> getEvaluationProcess(@PathVariable Integer id) {
        log.info("获取评价流程状态: id={}", id);
        return commonService.getEvaluationProcess(id);
    }

    /**
     * 检查评价是否可以编辑
     */
    @GetMapping("/{id}/can-edit")
    public FyfcApiResponseDto<Boolean> canEditEvaluation(
            @PathVariable Integer id,
            @RequestParam String userName) {
        log.info("检查评价是否可编辑: id={}, userName={}", id, userName);
        return commonService.canEditEvaluation(id, userName);
    }

    /**
     * 获取评价相关的用户列表
     */
    @GetMapping("/{id}/related-users")
    public FyfcApiResponseDto<Object> getEvaluationRelatedUsers(@PathVariable Integer id) {
        log.info("获取评价相关用户: id={}", id);
        return commonService.getEvaluationRelatedUsers(id);
    }

    // ==================== 评分相关 API ====================

    /**
     * 获取用户对指定评价的评分记录
     */
    @GetMapping("/{id}/user-score")
    public FyfcApiResponseDto<FyfcEvaluationScoreDto> getUserScore(
            @PathVariable Integer id,
            @RequestParam String evaluator) {
        log.info("获取用户评分: evaluationId={}, evaluator={}", id, evaluator);
        return scoreService.getUserScore(id, evaluator);
    }

    /**
     * 检查用户是否已经评分
     */
    @GetMapping("/{id}/has-scored")
    public FyfcApiResponseDto<Boolean> hasUserScored(
            @PathVariable Integer id,
            @RequestParam String evaluator) {
        log.info("检查用户是否已评分: evaluationId={}, evaluator={}", id, evaluator);
        return scoreService.hasUserScored(id, evaluator);
    }

    /**
     * 计算评价的平均分
     */
    @GetMapping("/{id}/average-score")
    public FyfcApiResponseDto<Object> calculateAverageScore(@PathVariable Integer id) {
        log.info("计算平均分: evaluationId={}", id);
        return scoreService.calculateAverageScore(id);
    }

    /**
     * 获取评分统计信息
     */
    @GetMapping("/{id}/score-stats")
    public FyfcApiResponseDto<Object> getScoreStatistics(@PathVariable Integer id) {
        log.info("获取评分统计: evaluationId={}", id);
        return scoreService.getScoreStatistics(id);
    }

    /**
     * 验证评分数据
     */
    @PostMapping("/validate-score")
    public FyfcApiResponseDto<Object> validateScoreData(@RequestBody FyfcScoreFormDto scoreForm) {
        log.info("验证评分数据");
        return scoreService.validateScoreData(scoreForm);
    }

    /**
     * 获取评分模板
     */
    @GetMapping("/score-template")
    public FyfcApiResponseDto<Object> getScoreTemplate(@RequestParam String evaluatorType) {
        log.info("获取评分模板: type={}", evaluatorType);
        return scoreService.getScoreTemplate(evaluatorType);
    }

    /**
     * 检查评分权限
     */
    @GetMapping("/{id}/score-permission")
    public FyfcApiResponseDto<Object> checkScorePermission(
            @PathVariable Integer id,
            @RequestParam String evaluator) {
        log.info("检查评分权限: evaluationId={}, evaluator={}", id, evaluator);
        return scoreService.checkScorePermission(id, evaluator);
    }

    // ==================== 通知相关 API ====================

    /**
     * 发送评价通知
     */
    @PostMapping("/{id}/notification")
    public FyfcApiResponseDto<Boolean> sendEvaluationNotification(
            @PathVariable Integer id,
            @RequestParam String notificationType) {
        log.info("发送评价通知: id={}, type={}", id, notificationType);
        return commonService.sendEvaluationNotification(id, notificationType);
    }

    // ==================== 工具 API ====================

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public FyfcApiResponseDto<Object> healthCheck() {
        log.info("健康检查");
        return FyfcApiResponseDto.success("OK", "服务正常");
    }

    /**
     * 获取系统版本信息
     */
    @GetMapping("/version")
    public FyfcApiResponseDto<Object> getVersionInfo() {
        log.info("获取版本信息");
        return FyfcApiResponseDto.success("v1.0.0", "FYFC Review System");
    }

    /**
     * 获取API文档
     */
    @GetMapping("/api-docs")
    public FyfcApiResponseDto<Object> getApiDocs() {
        log.info("获取API文档");
        return FyfcApiResponseDto.success(null, "API文档功能开发中");
    }
}
