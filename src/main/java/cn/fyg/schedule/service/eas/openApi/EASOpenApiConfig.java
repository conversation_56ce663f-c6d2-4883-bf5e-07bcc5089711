package cn.fyg.schedule.service.eas.openApi;

import cn.fyg.schedule.config.EASConfig;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Map;

@Component
@Slf4j
public class EASOpenApiConfig {
    /**
     * 初始化值，标记token是否曾经从服务器取回
     */
    private static final String INIT_FLAG = "init_value";

    private final EASConfig easConfig;

    public EASOpenApiConfig(EASConfig easConfig) {
        this.easConfig = easConfig;
    }

    private String token = EASOpenApiConfig.INIT_FLAG;



    private synchronized String getToken() {
        return this.token;
    }

    /**
     * TODO 当用户名密码错误时会一直登录，可能代码有问题
     *
     * @param oldToken
     */
    private synchronized void refreshToken(String oldToken) {
        // 刷新线程时带上老的token,如果不一样，则说明有其它线程刷新了token，直接跳过
        if (!oldToken.equals(this.token)) {
            return;
        }

        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(easConfig.getBaseURL() + "/login");
        builder.queryParam("authPattern", "BaseDB");
        builder.queryParam("dcName", "eas85");
        builder.queryParam("isEncodePwd", "0");
        builder.queryParam("language", "l2");

        builder.queryParam("user", easConfig.getUser());
        builder.queryParam("password", easConfig.getPwd());

        RestTemplate restTemplate = new RestTemplate();

        try {
            String result = restTemplate.getForObject(builder.build().toString(), String.class);
            EASOpenApiReturn ret = JSONUtil.toBean(result, EASOpenApiReturn.class);
            if (ret.getErrCode() == 0) {
                JSONObject data = JSONUtil.parseObj(ret.getData());
                this.token = data.getStr("token");
                log.info("refreesh token:{}", this.token);
            } else {
                log.error("login eas server error:\n{}", ret.toString());
            }

        } catch (HttpClientErrorException e) {
            log.error(e.getResponseBodyAsString());
        } catch (Exception e) {
            log.error("all eas server error", e);
        }
    }

    public String callAPI(Map<String, Object> param) {

        RestTemplate restTemplate = new RestTemplate();
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(easConfig.getBaseURL() + "/api");
        HttpEntity<Map<String, Object>> entity = new HttpEntity<Map<String, Object>>(param);

        String token = this.getToken();
        try {
            String result = restTemplate.postForObject(builder.queryParam("token", token).build().toString(), entity,
                    String.class);
            EASOpenApiReturn ret = JSONUtil.toBean(result, EASOpenApiReturn.class);
            if (ret.getErrCode() == 0) {
                return ret.getData();
            } else {
                log.error("call eas server error:\n{}", ret.toString());
            }
        } catch (HttpClientErrorException e) {
            // 如果是用户登录失效，则重新登录
            if (HttpStatus.UNAUTHORIZED == e.getStatusCode()) {
                this.refreshToken(token);
                return this.callAPI(param);
            } else {
                log.error(e.getResponseBodyAsString());
            }
        } catch (Exception e) {
            log.error("unkonw eas server error", e);
        }
        return null;
    }

}
