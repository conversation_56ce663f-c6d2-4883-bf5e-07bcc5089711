package cn.fyg.schedule.service.fyfc.review;

import cn.fyg.schedule.pojo.dto.fyfc.review.*;

import java.util.List;

/**
 * FYFC 评价主管服务接口
 * 对应 manager/dashboard 页面的业务逻辑
 */
public interface IFyfcEvaluationManagerService {

    /**
     * 主管查询待评价的记录
     * @param managerName 主管姓名
     * @param queryDto 查询条件
     * @return 统一API响应格式的分页结果
     */
    FyfcApiResponseDto<FyfcPaginatedResponseDto<FyfcEvaluationDto>> getPendingEvaluations(String managerName, FyfcEvaluationQueryDto queryDto);

    /**
     * 主管查询已完成的评价
     * @param managerName 主管姓名
     * @param queryDto 查询条件
     * @return 统一API响应格式的分页结果
     */
    FyfcApiResponseDto<FyfcPaginatedResponseDto<FyfcEvaluationDto>> getCompletedEvaluations(String managerName, FyfcEvaluationQueryDto queryDto);

    /**
     * 主管提交评分
     * @param scoreForm 评分表单
     * @param managerName 主管姓名
     * @return 统一API响应格式
     */
    FyfcApiResponseDto<Boolean> submitManagerScore(FyfcScoreFormDto scoreForm, String managerName);

    /**
     * 主管批量审核评价
     * @param evaluationIds 评价ID列表
     * @param managerName 主管姓名
     * @return 统一API响应格式的处理结果
     */
    FyfcApiResponseDto<Integer> batchApproveEvaluations(List<Integer> evaluationIds, String managerName);

    /**
     * 获取主管评价统计
     * @param managerName 主管姓名
     * @return 统一API响应格式的统计数据
     */
    FyfcApiResponseDto<FyfcEvaluationStatsDto> getEvaluationStats(String managerName);

    /**
     * 获取主管负责的部门员工列表
     * @param managerName 主管姓名
     * @return 统一API响应格式的员工列表
     */
    FyfcApiResponseDto<List<String>> getManagedEmployees(String managerName);

    /**
     * 主管查看评价详情
     * @param evaluationId 评价ID
     * @param managerName 主管姓名
     * @return 统一API响应格式的评价详情
     */
    FyfcApiResponseDto<FyfcEvaluationDetailDto> getEvaluationDetail(Integer evaluationId, String managerName);

    /**
     * 主管驳回评价（退回到上一状态）
     * @param evaluationId 评价ID
     * @param managerName 主管姓名
     * @param reason 驳回原因
     * @return 统一API响应格式
     */
    FyfcApiResponseDto<Boolean> rejectEvaluation(Integer evaluationId, String managerName, String reason);

    /**
     * 获取主管待办事项统计
     * @param managerName 主管姓名
     * @return 统一API响应格式的待办统计
     */
    FyfcApiResponseDto<Object> getPendingTaskStats(String managerName);

    /**
     * 主管导出下属评价数据
     * @param managerName 主管姓名
     * @param queryDto 查询条件
     * @return 统一API响应格式的导出数据
     */
    FyfcApiResponseDto<List<FyfcEvaluationDto>> exportSubordinateEvaluations(String managerName, FyfcEvaluationQueryDto queryDto);
}
