package cn.fyg.schedule.utils;

import cn.fyg.schedule.specification.BaseSpecification;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.domain.Specification;

import java.util.Date;
import java.util.List;

/**
 * 使用 Hutool 增强的 Specification 工具类
 * 专门为 FYFC Review 系统设计
 */
@Slf4j
public class FyfcSpecificationUtil {

    /**
     * 使用 Hutool 的 JSON 工具解析 JSON 数组并创建 IN 查询
     */
    public static <T> Specification<T> isInFromJson(String attribute, String jsonValues) {
        if (StrUtil.isBlank(jsonValues)) {
            return null;
        }
        
        try {
            if (!JSONUtil.isJsonArray(jsonValues)) {
                log.warn("Invalid JSON array: {}", jsonValues);
                return null;
            }
            
            JSONArray jsonArray = JSONUtil.parseArray(jsonValues);
            List<Object> values = jsonArray.toList(Object.class);
            
            if (CollUtil.isEmpty(values)) {
                return null;
            }
            
            return BaseSpecification.inCollection(attribute, values);
        } catch (Exception e) {
            log.error("Error processing JSON for specification: {}", jsonValues, e);
            return null;
        }
    }

    /**
     * 使用 Hutool 解析枚举类型的 JSON 数组
     */
    public static <T, E extends Enum<E>> Specification<T> isInFromJsonForEnum(
            String attribute, String jsonValues, Class<E> enumClass) {
        if (StrUtil.isBlank(jsonValues)) {
            return null;
        }
        
        try {
            if (!JSONUtil.isJsonArray(jsonValues)) {
                log.warn("Invalid JSON array: {}", jsonValues);
                return null;
            }
            
            JSONArray jsonArray = JSONUtil.parseArray(jsonValues);
            List<String> stringValues = jsonArray.toList(String.class);
            
            if (CollUtil.isEmpty(stringValues)) {
                return null;
            }
            
            List<E> enumValues = CollUtil.newArrayList();
            for (String value : stringValues) {
                try {
                    E enumValue = Enum.valueOf(enumClass, value.toUpperCase());
                    enumValues.add(enumValue);
                } catch (IllegalArgumentException e) {
                    log.warn("Invalid enum value: {} for enum class: {}", value, enumClass.getSimpleName());
                }
            }
            
            return BaseSpecification.inCollection(attribute, enumValues);
        } catch (Exception e) {
            log.error("Error processing JSON for enum specification: {}", jsonValues, e);
            return null;
        }
    }

    /**
     * 使用 Hutool 解析数值范围 JSON
     */
    public static <T> Specification<T> numberRangeFromJson(String attribute, String jsonValues) {
        if (StrUtil.isBlank(jsonValues)) {
            return null;
        }
        
        try {
            if (!JSONUtil.isJsonArray(jsonValues)) {
                log.warn("Invalid JSON array: {}", jsonValues);
                return null;
            }
            
            JSONArray jsonArray = JSONUtil.parseArray(jsonValues);
            List<Number> numbers = jsonArray.toList(Number.class);
            
            Number min = CollUtil.isNotEmpty(numbers) ? numbers.get(0) : null;
            Number max = numbers.size() > 1 ? numbers.get(1) : null;
            
            return BaseSpecification.numberRange(attribute, min, max);
        } catch (Exception e) {
            log.error("Error processing JSON for number range specification: {}", jsonValues, e);
            return null;
        }
    }

    /**
     * 使用 Hutool 解析时间戳范围 JSON
     */
    public static <T> Specification<T> timestampRangeFromJson(String attribute, String jsonValues) {
        if (StrUtil.isBlank(jsonValues)) {
            return null;
        }
        
        try {
            if (!JSONUtil.isJsonArray(jsonValues)) {
                log.warn("Invalid JSON array: {}", jsonValues);
                return null;
            }
            
            JSONArray jsonArray = JSONUtil.parseArray(jsonValues);
            List<Long> timestamps = jsonArray.toList(Long.class);
            
            Long startTimestamp = CollUtil.isNotEmpty(timestamps) ? timestamps.get(0) : null;
            Long endTimestamp = timestamps.size() > 1 ? timestamps.get(1) : null;
            
            Date startDate = ObjectUtil.isNotNull(startTimestamp) ? new Date(startTimestamp) : null;
            Date endDate = ObjectUtil.isNotNull(endTimestamp) ? DateUtil.endOfDay(new Date(endTimestamp)) : null;
            
            return BaseSpecification.dateRange(attribute, startDate, endDate);
        } catch (Exception e) {
            log.error("Error processing JSON for timestamp range specification: {}", jsonValues, e);
            return null;
        }
    }

    /**
     * 使用 Hutool 解析日期范围 JSON (支持多种日期格式)
     */
    public static <T> Specification<T> dateRangeFromJson(String attribute, String jsonValues) {
        if (StrUtil.isBlank(jsonValues)) {
            return null;
        }
        
        try {
            if (!JSONUtil.isJsonArray(jsonValues)) {
                log.warn("Invalid JSON array: {}", jsonValues);
                return null;
            }
            
            JSONArray jsonArray = JSONUtil.parseArray(jsonValues);
            List<String> dateStrings = jsonArray.toList(String.class);
            
            Date startDate = null;
            Date endDate = null;
            
            if (CollUtil.isNotEmpty(dateStrings)) {
                String startDateStr = dateStrings.get(0);
                if (StrUtil.isNotBlank(startDateStr)) {
                    startDate = DateUtil.parse(startDateStr);
                }
            }
            
            if (dateStrings.size() > 1) {
                String endDateStr = dateStrings.get(1);
                if (StrUtil.isNotBlank(endDateStr)) {
                    endDate = DateUtil.parse(endDateStr);
                }
            }
            
            return BaseSpecification.dateRange(attribute, startDate, endDate);
        } catch (Exception e) {
            log.error("Error processing JSON for date range specification: {}", jsonValues, e);
            return null;
        }
    }

    /**
     * 使用 Hutool 增强的条件添加方法
     */
    public static <T> Specification<T> addSpecificationIfNotEmpty(
            Specification<T> spec, String attribute, String value, SpecificationFunction<T> function) {
        if (StrUtil.isNotBlank(value)) {
            Specification<T> newSpec = function.apply(attribute, value);
            if (ObjectUtil.isNotNull(newSpec)) {
                spec = ObjectUtil.isNull(spec) ? newSpec : spec.and(newSpec);
            }
        }
        return spec;
    }

    /**
     * 使用 Hutool 增强的 JSON 条件添加方法
     */
    public static <T> Specification<T> addSpecificationFromJson(
            Specification<T> spec, String attribute, String jsonValues, JsonSpecificationFunction<T> function) {
        if (StrUtil.isNotBlank(jsonValues) && JSONUtil.isJsonArray(jsonValues)) {
            Specification<T> newSpec = function.apply(attribute, jsonValues);
            if (ObjectUtil.isNotNull(newSpec)) {
                spec = ObjectUtil.isNull(spec) ? newSpec : spec.and(newSpec);
            }
        }
        return spec;
    }

    /**
     * 构建复合查询条件
     */
    public static <T> Specification<T> buildComplexSpecification() {
        return Specification.where(null);
    }

    /**
     * 安全地组合多个 Specification
     */
    @SafeVarargs
    public static <T> Specification<T> combineWithAnd(Specification<T>... specifications) {
        Specification<T> result = Specification.where(null);
        for (Specification<T> spec : specifications) {
            if (ObjectUtil.isNotNull(spec)) {
                result = result.and(spec);
            }
        }
        return result;
    }

    /**
     * 安全地组合多个 Specification (OR 条件)
     */
    @SafeVarargs
    public static <T> Specification<T> combineWithOr(Specification<T>... specifications) {
        Specification<T> result = null;
        for (Specification<T> spec : specifications) {
            if (ObjectUtil.isNotNull(spec)) {
                result = ObjectUtil.isNull(result) ? spec : result.or(spec);
            }
        }
        return result;
    }

    /**
     * Specification 函数式接口
     */
    @FunctionalInterface
    public interface SpecificationFunction<T> {
        Specification<T> apply(String attribute, String value);
    }

    /**
     * JSON Specification 函数式接口
     */
    @FunctionalInterface
    public interface JsonSpecificationFunction<T> {
        Specification<T> apply(String attribute, String jsonValues);
    }
}
