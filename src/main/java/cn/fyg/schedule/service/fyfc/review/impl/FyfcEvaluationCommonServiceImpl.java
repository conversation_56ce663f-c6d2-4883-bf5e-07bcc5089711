package cn.fyg.schedule.service.fyfc.review.impl;

import cn.fyg.schedule.dao.fyfc.review.FyfcEvaluationRepository;
import cn.fyg.schedule.dao.fyfc.review.FyfcEvaluationScoreRepository;
import cn.fyg.schedule.dao.fyfc.review.FyfcEvaluationStatusHistoryRepository;
import cn.fyg.schedule.enums.fyfc.review.EvaluationStatus;
import cn.fyg.schedule.pojo.dto.fyfc.review.*;
import cn.fyg.schedule.pojo.fyfc.review.FyfcEvaluation;
import cn.fyg.schedule.pojo.fyfc.review.FyfcEvaluationScore;
import cn.fyg.schedule.pojo.fyfc.review.FyfcEvaluationStatusHistory;
import cn.fyg.schedule.service.fyfc.review.IFyfcEvaluationCommonService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * FYFC 评价通用服务实现类
 * 提供各个模块共用的基础功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FyfcEvaluationCommonServiceImpl implements IFyfcEvaluationCommonService {

    private final FyfcEvaluationRepository evaluationRepository;
    private final FyfcEvaluationScoreRepository scoreRepository;
    private final FyfcEvaluationStatusHistoryRepository statusHistoryRepository;

    @Override
    public FyfcApiResponseDto<FyfcEvaluationDto> getEvaluationById(Integer evaluationId) {
        log.info("根据ID获取评价: {}", evaluationId);
        
        try {
            if (ObjectUtil.isNull(evaluationId)) {
                return FyfcApiResponseDto.error(400, "评价ID不能为空");
            }
            
            Optional<FyfcEvaluation> optionalEvaluation = evaluationRepository.findById(evaluationId);
            if (!optionalEvaluation.isPresent()) {
                return FyfcApiResponseDto.error(404, "评价记录不存在");
            }
            
            FyfcEvaluationDto dto = convertToEvaluationDto(optionalEvaluation.get());
            return FyfcApiResponseDto.success(dto, "查询成功");
        } catch (Exception e) {
            log.error("获取评价失败", e);
            return FyfcApiResponseDto.error("查询失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<List<FyfcEvaluationScoreDto>> getEvaluationScores(Integer evaluationId) {
        log.info("获取评价得分记录: {}", evaluationId);
        
        try {
            if (ObjectUtil.isNull(evaluationId)) {
                return FyfcApiResponseDto.error(400, "评价ID不能为空");
            }
            
            List<FyfcEvaluationScore> scores = scoreRepository.findByEvaluationIdOrderByCreatedAtAsc(evaluationId);
            List<FyfcEvaluationScoreDto> dtoList = scores.stream()
                .map(this::convertToScoreDto)
                .collect(Collectors.toList());
            
            return FyfcApiResponseDto.success(dtoList, "查询成功");
        } catch (Exception e) {
            log.error("获取评价得分失败", e);
            return FyfcApiResponseDto.error("查询失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<List<FyfcEvaluationStatusHistoryDto>> getEvaluationStatusHistory(Integer evaluationId) {
        log.info("获取评价状态历史: {}", evaluationId);
        
        try {
            if (ObjectUtil.isNull(evaluationId)) {
                return FyfcApiResponseDto.error(400, "评价ID不能为空");
            }
            
            List<FyfcEvaluationStatusHistory> histories = statusHistoryRepository.findByEvaluationIdOrderByChangedAtDesc(evaluationId);
            List<FyfcEvaluationStatusHistoryDto> dtoList = histories.stream()
                .map(this::convertToStatusHistoryDto)
                .collect(Collectors.toList());
            
            return FyfcApiResponseDto.success(dtoList, "查询成功");
        } catch (Exception e) {
            log.error("获取状态历史失败", e);
            return FyfcApiResponseDto.error("查询失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<List<String>> getDepartmentList() {
        log.info("获取部门列表");
        
        try {
            List<String> departments = evaluationRepository.getDepartmentHistoryList();
            return FyfcApiResponseDto.success(departments, "查询成功");
        } catch (Exception e) {
            log.error("获取部门列表失败", e);
            return FyfcApiResponseDto.error("查询失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<List<FyfcEvaluationStatusDto>> getStatusOptions() {
        log.info("获取状态选项");
        
        try {
            List<FyfcEvaluationStatusDto> statusOptions = CollUtil.newArrayList(
                FyfcEvaluationStatusDto.SELF,
                FyfcEvaluationStatusDto.COLLEAGUE,
                FyfcEvaluationStatusDto.MANAGER,
                FyfcEvaluationStatusDto.COMPLETED
            );
            return FyfcApiResponseDto.success(statusOptions, "查询成功");
        } catch (Exception e) {
            log.error("获取状态选项失败", e);
            return FyfcApiResponseDto.error("查询失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<List<FyfcUserRoleDto>> getUserRoleOptions() {
        log.info("获取用户角色选项");
        
        try {
            List<FyfcUserRoleDto> roleOptions = CollUtil.newArrayList(
                FyfcUserRoleDto.EMPLOYEE,
                FyfcUserRoleDto.COLLEAGUE,
                FyfcUserRoleDto.MANAGER,
                FyfcUserRoleDto.ADMIN
            );
            return FyfcApiResponseDto.success(roleOptions, "查询成功");
        } catch (Exception e) {
            log.error("获取角色选项失败", e);
            return FyfcApiResponseDto.error("查询失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<Boolean> validateEvaluationData(Integer evaluationId) {
        log.info("验证评价数据完整性: {}", evaluationId);
        
        try {
            if (ObjectUtil.isNull(evaluationId)) {
                return FyfcApiResponseDto.error(400, "评价ID不能为空");
            }
            
            Optional<FyfcEvaluation> optionalEvaluation = evaluationRepository.findById(evaluationId);
            if (!optionalEvaluation.isPresent()) {
                return FyfcApiResponseDto.error(404, "评价记录不存在");
            }
            
            FyfcEvaluation evaluation = optionalEvaluation.get();
            
            // 验证必填字段
            if (StrUtil.isBlank(evaluation.getName())) {
                return FyfcApiResponseDto.success(false, "被评价人姓名不能为空");
            }
            if (StrUtil.isBlank(evaluation.getDepartment())) {
                return FyfcApiResponseDto.success(false, "部门不能为空");
            }
            if (ObjectUtil.isNull(evaluation.getReviewDate())) {
                return FyfcApiResponseDto.success(false, "评价日期不能为空");
            }
            
            return FyfcApiResponseDto.success(true, "数据验证通过");
        } catch (Exception e) {
            log.error("验证评价数据失败", e);
            return FyfcApiResponseDto.error("验证失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<Object> calculateTotalScore(Integer evaluationId) {
        log.info("计算评价总分: {}", evaluationId);
        
        try {
            if (ObjectUtil.isNull(evaluationId)) {
                return FyfcApiResponseDto.error(400, "评价ID不能为空");
            }
            
            List<FyfcEvaluationScore> scores = scoreRepository.findByEvaluationIdOrderByCreatedAtAsc(evaluationId);
            if (CollUtil.isEmpty(scores)) {
                return FyfcApiResponseDto.success(BigDecimal.ZERO, "暂无评分记录");
            }
            
            // 计算平均分
            BigDecimal totalScore = scores.stream()
                .map(FyfcEvaluationScore::getScore)
                .filter(ObjectUtil::isNotNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            BigDecimal averageScore = totalScore.divide(BigDecimal.valueOf(scores.size()), 2, RoundingMode.HALF_UP);
            
            return FyfcApiResponseDto.success(averageScore, "计算成功");
        } catch (Exception e) {
            log.error("计算总分失败", e);
            return FyfcApiResponseDto.error("计算失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<Object> getEvaluationProcess(Integer evaluationId) {
        log.info("获取评价流程状态: {}", evaluationId);
        
        try {
            if (ObjectUtil.isNull(evaluationId)) {
                return FyfcApiResponseDto.error(400, "评价ID不能为空");
            }
            
            Optional<FyfcEvaluation> optionalEvaluation = evaluationRepository.findById(evaluationId);
            if (!optionalEvaluation.isPresent()) {
                return FyfcApiResponseDto.error(404, "评价记录不存在");
            }
            
            FyfcEvaluation evaluation = optionalEvaluation.get();
            List<FyfcEvaluationScore> scores = scoreRepository.findByEvaluationIdOrderByCreatedAtAsc(evaluationId);
            
            // 构建流程状态信息
            Object processInfo = buildProcessInfo(evaluation, scores);
            
            return FyfcApiResponseDto.success(processInfo, "查询成功");
        } catch (Exception e) {
            log.error("获取流程状态失败", e);
            return FyfcApiResponseDto.error("查询失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<Boolean> canEditEvaluation(Integer evaluationId, String userName) {
        log.info("检查评价是否可编辑: evaluationId={}, userName={}", evaluationId, userName);
        
        try {
            if (ObjectUtil.isNull(evaluationId) || StrUtil.isBlank(userName)) {
                return FyfcApiResponseDto.error(400, "参数不能为空");
            }
            
            Optional<FyfcEvaluation> optionalEvaluation = evaluationRepository.findById(evaluationId);
            if (!optionalEvaluation.isPresent()) {
                return FyfcApiResponseDto.error(404, "评价记录不存在");
            }
            
            FyfcEvaluation evaluation = optionalEvaluation.get();
            
            // 检查编辑权限
            boolean canEdit = checkEditPermission(evaluation, userName);
            
            return FyfcApiResponseDto.success(canEdit, canEdit ? "可以编辑" : "无编辑权限");
        } catch (Exception e) {
            log.error("检查编辑权限失败", e);
            return FyfcApiResponseDto.error("检查失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<Object> getEvaluationRelatedUsers(Integer evaluationId) {
        log.info("获取评价相关用户: {}", evaluationId);
        
        try {
            if (ObjectUtil.isNull(evaluationId)) {
                return FyfcApiResponseDto.error(400, "评价ID不能为空");
            }
            
            Optional<FyfcEvaluation> optionalEvaluation = evaluationRepository.findById(evaluationId);
            if (!optionalEvaluation.isPresent()) {
                return FyfcApiResponseDto.error(404, "评价记录不存在");
            }
            
            FyfcEvaluation evaluation = optionalEvaluation.get();
            Object relatedUsers = buildRelatedUsersInfo(evaluation);
            
            return FyfcApiResponseDto.success(relatedUsers, "查询成功");
        } catch (Exception e) {
            log.error("获取相关用户失败", e);
            return FyfcApiResponseDto.error("查询失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<Boolean> sendEvaluationNotification(Integer evaluationId, String notificationType) {
        log.info("发送评价通知: evaluationId={}, type={}", evaluationId, notificationType);
        
        try {
            if (ObjectUtil.isNull(evaluationId) || StrUtil.isBlank(notificationType)) {
                return FyfcApiResponseDto.error(400, "参数不能为空");
            }
            
            // TODO: 实现通知发送逻辑
            // 这里可以集成邮件、短信、站内消息等通知方式
            
            return FyfcApiResponseDto.success(true, "通知发送成功");
        } catch (Exception e) {
            log.error("发送通知失败", e);
            return FyfcApiResponseDto.error("发送失败: " + e.getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 转换为评价DTO
     */
    private FyfcEvaluationDto convertToEvaluationDto(FyfcEvaluation evaluation) {
        FyfcEvaluationDto dto = new FyfcEvaluationDto();
        // 排除 attachments 字段，避免类型转换异常
        BeanUtil.copyProperties(evaluation, dto, "attachments");

        // 处理时间戳转换
        if (ObjectUtil.isNotNull(evaluation.getReviewDate())) {
            dto.setReviewDate(evaluation.getReviewDate().getTime());
        }
        if (ObjectUtil.isNotNull(evaluation.getCreatedAt())) {
            dto.setCreatedAt(evaluation.getCreatedAt().getTime());
        }

        // 设置状态字符串
        if (ObjectUtil.isNotNull(evaluation.getStatus())) {
            dto.setStatus(evaluation.getStatus().getValue());
        }

        // 加载评分数据
        try {
            List<FyfcEvaluationScore> scores = scoreRepository.findByEvaluationIdOrderByCreatedAtAsc(evaluation.getId());
            List<FyfcEvaluationScoreDto> scoreDtos = scores.stream()
                .map(this::convertToScoreDto)
                .collect(Collectors.toList());
            dto.setScores(scoreDtos);
        } catch (Exception e) {
            log.warn("加载评价评分数据失败: evaluationId={}", evaluation.getId(), e);
            // 设置空列表，避免前端出错
            dto.setScores(CollUtil.newArrayList());
        }

        // 加载附件数据
        try {
            List<FyfcAttachmentDto> attachments = getAttachmentsFromJson(evaluation.getAttachments());
            dto.setAttachments(attachments);
        } catch (Exception e) {
            log.warn("加载评价附件数据失败: evaluationId={}", evaluation.getId(), e);
            // 设置空列表，避免前端出错
            dto.setAttachments(CollUtil.newArrayList());
        }

        return dto;
    }

    /**
     * 转换为评分DTO
     */
    private FyfcEvaluationScoreDto convertToScoreDto(FyfcEvaluationScore score) {
        FyfcEvaluationScoreDto dto = new FyfcEvaluationScoreDto();
        BeanUtil.copyProperties(score, dto);
        
        // 设置类型字符串
        if (ObjectUtil.isNotNull(score.getType())) {
            dto.setType(score.getType().getValue());
        }
        
        return dto;
    }

    /**
     * 转换为状态历史DTO
     */
    private FyfcEvaluationStatusHistoryDto convertToStatusHistoryDto(FyfcEvaluationStatusHistory history) {
        FyfcEvaluationStatusHistoryDto dto = new FyfcEvaluationStatusHistoryDto();
        BeanUtil.copyProperties(history, dto);
        
        // 处理时间戳转换
        if (ObjectUtil.isNotNull(history.getChangedAt())) {
            dto.setChangedAt(history.getChangedAt().getTime());
        }
        
        // 设置状态字符串
        if (ObjectUtil.isNotNull(history.getPreviousStatus())) {
            dto.setPreviousStatus(history.getPreviousStatus().getValue());
        }
        if (ObjectUtil.isNotNull(history.getNewStatus())) {
            dto.setNewStatus(history.getNewStatus().getValue());
        }
        
        return dto;
    }

    /**
     * 构建流程状态信息
     */
    private Object buildProcessInfo(FyfcEvaluation evaluation, List<FyfcEvaluationScore> scores) {
        // TODO: 实现流程状态构建逻辑
        return new Object();
    }

    /**
     * 检查编辑权限
     */
    private boolean checkEditPermission(FyfcEvaluation evaluation, String userName) {
        // 创建人可以编辑
        if (userName.equals(evaluation.getCreatedBy())) {
            return true;
        }
        
        // 根据状态和用户角色判断
        EvaluationStatus status = evaluation.getStatus();
        switch (status) {
            case SELF:
                return userName.equals(evaluation.getName());
            case COLLEAGUE:
                return userName.equals(evaluation.getColleagueName());
            case MANAGER:
                return userName.equals(evaluation.getManagerName());
            default:
                return false;
        }
    }

    /**
     * 构建相关用户信息
     */
    private Object buildRelatedUsersInfo(FyfcEvaluation evaluation) {
        // TODO: 实现相关用户信息构建逻辑
        return new Object();
    }

    /**
     * 从JSON字符串解析附件列表
     */
    private List<FyfcAttachmentDto> getAttachmentsFromJson(String attachmentsJson) {
        if (StrUtil.isBlank(attachmentsJson)) {
            return CollUtil.newArrayList();
        }

        try {
            return JSONUtil.toList(attachmentsJson, FyfcAttachmentDto.class);
        } catch (Exception e) {
            log.warn("解析附件JSON失败: {}", attachmentsJson, e);
            return CollUtil.newArrayList();
        }
    }
}
