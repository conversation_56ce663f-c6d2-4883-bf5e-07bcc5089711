package cn.fyg.schedule.service.sheets.yl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.project.yinLong.FYinLongItem;
import cn.fyg.schedule.pojo.project.yinLong.FYinLongSeal;
import cn.fyg.schedule.pojo.project.yinLong.FYinLongSignature;

public interface FYinLongSealService {
    /*
    seal
     */
    BaseResponse saveSeal(FYinLongSeal data);
    BaseResponse deleteSeal(Integer id);
    BaseResponse listAllSeal();
    BaseResponse getDepartmentNameHistoryList();
    BaseResponse getAuditDeptHistoryList();
    BaseResponse getSealTypeHistoryList();
    BaseResponse findById(Integer id);
    /*
    items
     */
    BaseResponse saveItem(FYinLongItem data);
    BaseResponse deleteItem(Integer id);

    /*
    signature
     */
    BaseResponse saveSignature(FYinLongSignature data);
    BaseResponse deleteSignature(Integer id);
}
