package cn.fyg.schedule.service.fyapp.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.fyapp.FFunctionListRepository;
import cn.fyg.schedule.pojo.dto.fyapp.FFunctionList;
import cn.fyg.schedule.service.fyapp.FFunctionListS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class FFunctionListSI implements FFunctionListS {
    private final FFunctionListRepository r;

    public FFunctionListSI(FFunctionListRepository r) {
        this.r = r;
    }


    @Override
    public BaseResponse listAll() {
        BaseResponse response = BaseResponse.initialize();
        try {
            List<FFunctionList> list = r.findByFunStatusOrderByCreateDateDescFunOrderDesc(1);
            response.setResult(list);
        } catch (Exception e) {
            response.setCode(1);
            response.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return response;
    }
}
