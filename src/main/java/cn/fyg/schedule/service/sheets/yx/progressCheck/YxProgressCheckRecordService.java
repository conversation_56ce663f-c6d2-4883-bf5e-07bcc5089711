package cn.fyg.schedule.service.sheets.yx.progressCheck;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.project.yx.progressCheck.YxProgressCheckRecordDto;
import cn.fyg.schedule.pojo.dto.project.yx.security.notification.YxSecurityNotiRecordDto;
import cn.fyg.schedule.pojo.project.yuanXin.progressCheck.YxProgressCheckRecord;
import cn.fyg.schedule.pojo.project.yuanXin.security.notification.YxSecurityNotiRecord;

import java.util.List;

public interface YxProgressCheckRecordService {
    BaseResponse save(YxProgressCheckRecordDto dto);
    BaseResponse delete(Integer id);
    BaseResponse findById(Integer id);
    BaseResponse findByParentId(Integer id);
    List<YxProgressCheckRecord> listRecordByParentId(Integer id, Integer mark);

    List<Integer> listParentIdByCreatorPhone(String creatorPhone);
}
