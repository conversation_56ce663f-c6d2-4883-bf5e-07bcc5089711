package cn.fyg.schedule.pojo.fyjs.staff;

import cn.fyg.schedule.pojo.dto.fyjs.staff.FyjsStaffBaseInfoDto;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

@Entity
@Setter
@Getter
@ToString
@Table(name = "fyjs_staff_base_info")
public class FyjsStaffBaseInfo implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Column(name = "staff_name")
    private String staffName;
    @Column(name = "staff_id_card")
    private String staffIdCard;
    private Integer gender; //0 女 1 男 2 其他
    private Integer age;
    @Column(name = "birth")
    @Temporal(TemporalType.DATE)
    private Date birth;
    @Column(name = "household_registration")
    private String householdRegistration;
    @Column(name = "household_registration_type")
    private String householdRegistrationType;
    @Column(name = "academic_career")
    private String academicCareer;
    @Column(name = "politics_status")
    private String politicsStatus;
    @Column(name = "phone_number")
    private String phoneNumber;
    @Column(name = "entry_date")
    @Temporal(TemporalType.DATE)
    private Date entryDate;
    @Column(name = "date_become_regular")
    @Temporal(TemporalType.DATE)
    private Date dateBecomeRegular;
    @Column(name = "belong_to")
    private String belongTo;
    @Column(name = "age_in_fy")
    private Integer ageInFy;
    @Column(name = "staff_property")
    private String staffProperty;
    @Column(name = "is_on_post")
    private Integer isOnPost; // 0 否 1 是
    @Column(name = "staff_remarks")
    private String staffRemarks;
    @Column(name = "creator")
    private String creator;
    @Column(name = "creator_name")
    private String creatorName;
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;
    private Integer dataStatus;
    private String lastEditor;
    private String avatar;
    private String easId;

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        FyjsStaffBaseInfo other = (FyjsStaffBaseInfo) obj;
        return Objects.equals(id, other.id) && ObjectUtil.equals(belongTo, other.belongTo) && ObjectUtil.equals(staffProperty, other.staffProperty)
                && Objects.equals(isOnPost, other.isOnPost);
    }

    public static FyjsStaffBaseInfo initialized(FyjsStaffBaseInfoDto dto) {
        FyjsStaffBaseInfo data = new FyjsStaffBaseInfo();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        data.setCreator(dto.getCreator());
        data.setCreatorName(dto.getCreatorName());
        if (dto.getCreateDate() != null) {
            data.setCreateDate(DateUtil.date(dto.getCreateDate()));
        }
        data.setDataStatus(dto.getDataStatus());
        data.setLastEditor(dto.getLastEditor());
        data.setStaffName(dto.getStaffName());
        data.setStaffIdCard(dto.getStaffIdCard());
        data.setGender(dto.getGender());
        data.setAge(dto.getAge());
        if (dto.getBirth() != null) {
            data.setBirth(DateUtil.date(dto.getBirth()));
        }
        data.setHouseholdRegistration(dto.getHouseholdRegistration());
        data.setHouseholdRegistrationType(dto.getHouseholdRegistrationType());
        data.setAcademicCareer(dto.getAcademicCareer());
        data.setPoliticsStatus(dto.getPoliticsStatus());
        data.setPhoneNumber(dto.getPhoneNumber());
        if (dto.getEntryDate() != null) {
            data.setEntryDate(DateUtil.date(dto.getEntryDate()));
        }
        if (dto.getDateBecomeRegular() != null) {
            data.setDateBecomeRegular(DateUtil.date(dto.getDateBecomeRegular()));
        }
        data.setBelongTo(dto.getBelongTo());
        data.setAgeInFy(dto.getAgeInFy());
        data.setStaffProperty(dto.getStaffProperty());
        data.setIsOnPost(dto.getIsOnPost());
        data.setStaffRemarks(dto.getStaffRemarks());
        data.setAvatar(dto.getAvatar());
        data.setEasId(dto.getEasId());
        return data;
    }
}
