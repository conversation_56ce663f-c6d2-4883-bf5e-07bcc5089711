package cn.fyg.schedule.service.sheets.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.FPurchaseRepository;
import cn.fyg.schedule.pojo.FPurchase;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.sheets.FPurchaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.Optional;

@Service
@Slf4j
public class FPurchaseServiceImpl implements FPurchaseService {
    @Autowired private FPurchaseRepository r;

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FPurchase data) {
        return BaseService.save(data, r, FPurchase.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Optional<FPurchase> result = r.findById(id);
            if (result.isPresent()) {
                baseResponse.setResult(result.get());
            }
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }
}
