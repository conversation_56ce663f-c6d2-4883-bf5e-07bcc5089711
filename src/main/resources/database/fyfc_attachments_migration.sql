-- FYFC 评价系统附件功能数据库迁移脚本
-- 执行日期: 2024-01-15
-- 描述: 为 fyfc_evaluations 表添加附件字段

-- 检查字段是否已存在
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'fyfc_evaluations' 
  AND COLUMN_NAME = 'attachments';

-- 如果字段不存在，则添加字段
ALTER TABLE fyfc_evaluations 
ADD COLUMN IF NOT EXISTS attachments TEXT COMMENT '附件列表（JSON格式存储）';

-- 验证字段添加成功
DESCRIBE fyfc_evaluations;

-- 创建索引（可选，如果需要按附件搜索）
-- CREATE INDEX idx_fyfc_evaluations_attachments ON fyfc_evaluations(attachments(100));

-- 示例数据（可选，用于测试）
-- UPDATE fyfc_evaluations 
-- SET attachments = '[]' 
-- WHERE attachments IS NULL;

-- 验证更新
SELECT 
    id, 
    name, 
    department,
    CASE 
        WHEN attachments IS NULL THEN 'NULL'
        WHEN attachments = '' THEN 'EMPTY'
        WHEN attachments = '[]' THEN 'EMPTY_ARRAY'
        ELSE 'HAS_DATA'
    END as attachment_status,
    CHAR_LENGTH(COALESCE(attachments, '')) as attachment_length
FROM fyfc_evaluations 
LIMIT 10;

-- 完成提示
SELECT 'FYFC 附件功能数据库迁移完成！' as migration_status;
