package cn.fyg.schedule.controller.yuanxin;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.construction.FyConstructionLicenseItem;
import cn.fyg.schedule.pojo.dto.construction.FyConstructionLicenseDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.service.construction.FyConstructionLicenseItemService;
import cn.fyg.schedule.service.construction.FyConstructionLicenseService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("license/construction")
public class FyConstructionLicenseController {
    private final FyConstructionLicenseItemService itemService;
    private final FyConstructionLicenseService service;

    public FyConstructionLicenseController(FyConstructionLicenseItemService itemService, FyConstructionLicenseService service) {
        this.itemService = itemService;
        this.service = service;
    }
    @PostMapping("filter")
    public BaseResponse filter(FyConstructionLicenseDto dto, Pagination pagination) {
        return service.filter(dto, pagination);
    }

    @PostMapping("find/id")
    public BaseResponse findById(Integer id) {
        return service.findById(id);
    }

    @PostMapping("save")
    public BaseResponse saveConstructionLicense(FyConstructionLicenseDto dto) {
        return service.save(dto);
    }

    @PostMapping("delete")
    public BaseResponse deleteConstructionLicense(Integer id) {
        return service.delete(id);
    }

    @PostMapping("find/item")
    public BaseResponse findItems(Integer id) {
        return itemService.findByParentId(id);
    }

    @PostMapping("delete/item")
    public BaseResponse deleteItem(Integer id) {
        return itemService.delete(id);
    }

    @PostMapping("save/item")
    public BaseResponse saveItem(FyConstructionLicenseItem data) {
        return itemService.save(data);
    }
}
