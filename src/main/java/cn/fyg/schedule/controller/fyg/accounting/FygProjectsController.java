package cn.fyg.schedule.controller.fyg.accounting;

import cn.fyg.schedule.pojo.dto.IntegerOptionObj;
import cn.fyg.schedule.pojo.dto.fyg.accounting.projects.ProjectDTO;
import cn.fyg.schedule.pojo.dto.fyg.accounting.projects.ProjectQuery;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.reponse.BaseResponse;
import cn.fyg.schedule.service.fyg.accounting.FygProjectsService;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("fyg/accounting/project")
public class FygProjectsController {
    private final FygProjectsService service;

    public FygProjectsController(FygProjectsService service) {
        this.service = service;
    }

    @PutMapping("save")
    public BaseResponse<ProjectDTO> save(ProjectDTO dto) {
        if (dto.getId() == null) {
            return service.save(dto);
        } else {
            return service.save(dto, dto.getId());
        }
    }

    @PostMapping("filter")
    public BaseResponse<Page<ProjectDTO>> filter(ProjectQuery query, Pagination pagination) {
        return service.filter(query, pagination);
    }

    @PutMapping("update-status")
    public BaseResponse<ProjectDTO> updateStatus(Integer id, Integer dataStatus, String updatedBy) {
        return service.setDataStatus(id, dataStatus, updatedBy);
    }

    @PostMapping("find/id")
    public BaseResponse<ProjectDTO> findById(Integer id) {
        return service.findById(id);
    }

    @PostMapping("list-options")
    public BaseResponse<List<IntegerOptionObj>> listOptions(Integer dataStatus) {
        return service.listOptions(dataStatus);
    }
}
