package cn.fyg.schedule.reponse;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@AllArgsConstructor
public class BaseResponse<T> {
    private Integer code; // 响应码
    private String message; // 响应消息
    private T data; // 响应数据，使用泛型以便灵活处理且保持类型安全

    // 初始化方法
    public void initialize(Integer code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    // 默认成功方法（需要提供一个默认的数据类型或设置为null）
    public void initialize() {
        this.initialize(200, "Success", null);
    }

    public static <T> BaseResponse<T> initialized() {
        return new BaseResponse<>();
    }
}