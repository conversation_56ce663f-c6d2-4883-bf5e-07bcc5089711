package cn.fyg.schedule.specification.fyjs.social.insurance;

import cn.fyg.schedule.pojo.fyjs.social.insurance.FyjsSocialInsurance;
import cn.fyg.schedule.pojo.fyjs.staff.FyjsStaffBaseInfo;
import cn.fyg.schedule.utils.SpecificationUtil;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class FyjsSocialInsuranceSpecification {
    public static Specification<FyjsSocialInsurance> ifContain(String column, String expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.like(root.get(column), SpecificationUtil.contains(expression)));
    }

    public static Specification<FyjsSocialInsurance> ifDateEq(String column, Date expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }

    public static Specification<FyjsSocialInsurance> ifDateBetween(String column, Date startDate, Date endDate) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.between(root.get(column), startDate, endDate));
    }

    public static Specification<FyjsSocialInsurance> ifDateOverThan(String column, Date date) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.greaterThanOrEqualTo(root.get(column), date));
    }

    public static Specification<FyjsSocialInsurance> ifDateLessThan(String column, Date date) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.lessThanOrEqualTo(root.get(column), date));
    }

    public static Specification<FyjsSocialInsurance> ifEq(String column, String expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }

    public static Specification<FyjsSocialInsurance> ifEq(String column, Integer expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }

    public static Specification<FyjsSocialInsurance> ifNumberBetween(String column, Long min, Long max) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.between(root.get(column), min, max));
    }

    public static Specification<FyjsSocialInsurance> ifNumberOverThan(String column, Long num) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.greaterThanOrEqualTo(root.get(column), num));
    }

    public static Specification<FyjsSocialInsurance> ifNumberLessThan(String column, Long num) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.lessThanOrEqualTo(root.get(column), num));
    }

    public static Specification<FyjsSocialInsurance> joinTableAndContain(String column, String expression) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            Join<FyjsStaffBaseInfo, FyjsSocialInsurance> tableJoin = root.join("staffInfo", JoinType.INNER);
            predicates.add(criteriaBuilder.like(tableJoin.get(column), SpecificationUtil.contains(expression)));
            return criteriaBuilder.or(predicates.toArray(new Predicate[0]));
        };
    }

    public static Specification<FyjsSocialInsurance> joinTableAndDateEq(String column, Date expression) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            Join<FyjsStaffBaseInfo, FyjsSocialInsurance> tableJoin = root.join("staffInfo", JoinType.INNER);
            predicates.add(criteriaBuilder.equal(tableJoin.get(column), expression));
            return criteriaBuilder.or(predicates.toArray(new Predicate[0]));
        };
    }

    public static Specification<FyjsSocialInsurance> joinTableAndDateBetween(String column, Date startDate, Date endDate) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            Join<FyjsStaffBaseInfo, FyjsSocialInsurance> tableJoin = root.join("staffInfo", JoinType.INNER);
            predicates.add(criteriaBuilder.between(tableJoin.get(column), startDate, endDate));
            return criteriaBuilder.or(predicates.toArray(new Predicate[0]));
        };
    }

    public static Specification<FyjsSocialInsurance> joinTableAndDateOverThan(String column, Date date) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            Join<FyjsStaffBaseInfo, FyjsSocialInsurance> tableJoin = root.join("staffInfo", JoinType.INNER);
            predicates.add(criteriaBuilder.greaterThanOrEqualTo(tableJoin.get(column), date));
            return criteriaBuilder.or(predicates.toArray(new Predicate[0]));
        };
    }

    public static Specification<FyjsSocialInsurance> joinTableAndDateLessThan(String column, Date date) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            Join<FyjsStaffBaseInfo, FyjsSocialInsurance> tableJoin = root.join("staffInfo", JoinType.INNER);
            predicates.add(criteriaBuilder.lessThanOrEqualTo(tableJoin.get(column), date));
            return criteriaBuilder.or(predicates.toArray(new Predicate[0]));
        };
    }

    public static Specification<FyjsSocialInsurance> joinTableAndEq(String column, String expression) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            Join<FyjsStaffBaseInfo, FyjsSocialInsurance> tableJoin = root.join("staffInfo", JoinType.INNER);
            predicates.add(criteriaBuilder.equal(tableJoin.get(column), SpecificationUtil.contains(expression)));
            return criteriaBuilder.or(predicates.toArray(new Predicate[0]));
        };
    }

    public static Specification<FyjsSocialInsurance> joinTableAndEq(String column, Integer expression) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            Join<FyjsStaffBaseInfo, FyjsSocialInsurance> tableJoin = root.join("staffInfo", JoinType.INNER);
            predicates.add(criteriaBuilder.equal(tableJoin.get(column), expression));
            return criteriaBuilder.or(predicates.toArray(new Predicate[0]));
        };
    }

    public static Specification<FyjsSocialInsurance> joinTableAndNumberBetween(String column, Integer min, Integer max) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            Join<FyjsStaffBaseInfo, FyjsSocialInsurance> tableJoin = root.join("staffInfo", JoinType.INNER);
            predicates.add(criteriaBuilder.between(tableJoin.get(column), min, max));
            return criteriaBuilder.or(predicates.toArray(new Predicate[0]));
        };
    }

    public static Specification<FyjsSocialInsurance> joinTableAndNumberOverThan(String column, Integer num) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            Join<FyjsStaffBaseInfo, FyjsSocialInsurance> tableJoin = root.join("staffInfo", JoinType.INNER);
            predicates.add(criteriaBuilder.greaterThanOrEqualTo(tableJoin.get(column), num));
            return criteriaBuilder.or(predicates.toArray(new Predicate[0]));
        };
    }

    public static Specification<FyjsSocialInsurance> joinTableAndNumberLessThan(String column, Integer num) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            Join<FyjsStaffBaseInfo, FyjsSocialInsurance> tableJoin = root.join("staffInfo", JoinType.INNER);
            predicates.add(criteriaBuilder.lessThanOrEqualTo(tableJoin.get(column), num));
            return criteriaBuilder.or(predicates.toArray(new Predicate[0]));
        };
    }

    public static Specification<FyjsSocialInsurance> joinTableAndNumberBetween(String column, Long min, Long max) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            Join<FyjsStaffBaseInfo, FyjsSocialInsurance> tableJoin = root.join("staffInfo", JoinType.INNER);
            predicates.add(criteriaBuilder.between(tableJoin.get(column), min, max));
            return criteriaBuilder.or(predicates.toArray(new Predicate[0]));
        };
    }

    public static Specification<FyjsSocialInsurance> joinTableAndNumberOverThan(String column, Long num) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            Join<FyjsStaffBaseInfo, FyjsSocialInsurance> tableJoin = root.join("staffInfo", JoinType.INNER);
            predicates.add(criteriaBuilder.greaterThanOrEqualTo(tableJoin.get(column), num));
            return criteriaBuilder.or(predicates.toArray(new Predicate[0]));
        };
    }

    public static Specification<FyjsSocialInsurance> joinTableAndNumberLessThan(String column, Long num) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            Join<FyjsStaffBaseInfo, FyjsSocialInsurance> tableJoin = root.join("staffInfo", JoinType.INNER);
            predicates.add(criteriaBuilder.lessThanOrEqualTo(tableJoin.get(column), num));
            return criteriaBuilder.or(predicates.toArray(new Predicate[0]));
        };
    }
}
