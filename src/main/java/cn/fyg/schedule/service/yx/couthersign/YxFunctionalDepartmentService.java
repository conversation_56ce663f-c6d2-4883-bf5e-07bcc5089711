package cn.fyg.schedule.service.yx.couthersign;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.yx.countersign.YxFunctionalDepartment;

import java.util.List;

public interface YxFunctionalDepartmentService {
    BaseResponse save(YxFunctionalDepartment data);
    BaseResponse delete(Integer id);
    BaseResponse listAll();
    List<YxFunctionalDepartment> findAll() throws MyException;
}
