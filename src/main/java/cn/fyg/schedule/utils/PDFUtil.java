package cn.fyg.schedule.utils;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;

import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.html2pdf.resolver.font.DefaultFontProvider;
import com.itextpdf.io.font.FontProgram;
import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.kernel.pdf.canvas.draw.ILineDrawer;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.LineSeparator;

public class PDFUtil {
    private static final String FONT = "./pdf/font/NotoSansCJKsc-Regular.otf";

    /**
     * @param html html页面字符串
     * @return
     * @throws FileNotFoundException
     * @throws IOException
     * @Description 将html转换为pdf文件
     */
    public ByteArrayOutputStream html2Pdf(String html) throws IOException {
        PdfFont font = PdfFontFactory.createFont(FONT, PdfEncodings.IDENTITY_H);
        FontProgram fpg = font.getFontProgram();
        ConverterProperties props = new ConverterProperties();
        DefaultFontProvider defaultFontProvider = new DefaultFontProvider(false, false, false);
        defaultFontProvider.addFont(fpg);
        props.setFontProvider(defaultFontProvider);
        ByteArrayOutputStream bao = new ByteArrayOutputStream();
        PdfWriter writer = new PdfWriter(bao);
        PdfDocument pdf = new PdfDocument(writer);
        //a4纸
        pdf.setDefaultPageSize(new PageSize(595, 842));
        Document document = HtmlConverter.convertToDocument(html, pdf, props);
        EndPosition endPosition = new EndPosition();
        LineSeparator separator = new LineSeparator(endPosition);
        document.add(separator);
        document.getRenderer().close();
        PdfPage page = pdf.getPage(1);
        float y = endPosition.getY() - 36;
        System.out.println("y:" + y);
        page.setMediaBox(new Rectangle(0, 0, 595, 842));
        document.close();
        return bao;
    }


    /**
     * 定义操作区域
     */
    class EndPosition implements ILineDrawer {
        // y坐标
        protected float y;

        /**
         * @return
         * @Description: 获取y坐标
         */
        public float getY() {
            return y;
        }

        /**
         * @param pdfCanvas:操作画布
         * @param rect:操作区域
         * @Description: 操作画布特定区域
         */
        @Override
        public void draw(PdfCanvas pdfCanvas, Rectangle rect) {
            this.y = rect.getY();
        }

        /**
         * @return
         * @Description: 获取行颜色
         */
        @Override
        public Color getColor() {
            return null;
        }

        /**
         * @return
         * @Description: 获取行宽
         */
        @Override
        public float getLineWidth() {
            return 0;
        }

        /**
         * @param color
         * @Description: 设置行颜色
         */
        @Override
        public void setColor(Color color) {
        }

        /**
         * @param lineWidth:宽度
         * @Description: 设置行宽
         */
        @Override
        public void setLineWidth(float lineWidth) {
        }
    }
}
