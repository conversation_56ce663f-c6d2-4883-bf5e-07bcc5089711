server:
  port: 7001
  servlet:
    context-path: /fyschedule2/

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************************************
    username: root
    password: fangyuan_27_HPW
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
  jpa:
    hibernate:
      ddl-auto: update
    database-platform: org.hibernate.dialect.MySQL57Dialect
    show-sql: true
    properties:
      hibernate:
        format_sql: true
    # 禁用 Open Session In View，提高性能和避免潜在问题
    open-in-view: false

wx:
  miniapp:
    configs:
      - appid: wxada3727cb7c28d43
        secret: 2e288dbc9760f17ac766e0009c3b5275
        token: xlh
        aesKey: OIFEBHkhYEbD0Oje0gzio7VcUjOmmEb2LqCvhooByKc
        msgDataFormat: JSON

aliyun:
  oss:
    externalEndpoint: https://oss-cn-shanghai.aliyuncs.com
    #endpoint: oss-cn-shanghai-internal.aliyuncs.com
    accessKeyId: LTAI4Fy2z3rKSX8ZGXedbsCD
    accessKeySecret: ******************************
    bucketName: fyschedule
    firstKey: xlh
    endpoint: https://oss-cn-shanghai.aliyuncs.com

fy:
  app:
    server: https://weixin.fyg.cn/fyschedule

eas:
  openapi:
    baseURL: http://172.19.74.29:9999/easportal/openapi
    user: fyguser
    pwd: Taizhou29827f