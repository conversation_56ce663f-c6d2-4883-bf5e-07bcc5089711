package cn.fyg.schedule.pojo.fyjs.bidding;

import cn.fyg.schedule.pojo.dto.fyjs.bidding.FyjsBiddingOpenDto;
import cn.hutool.core.date.DateUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

@Entity
@Setter
@Getter
@ToString
@Table(name = "fyjs_bidding_open")
public class FyjsBiddingOpen {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Column(name = "parent_id")
    private Integer parentId;
    private String creator;
    private String lastEditor;
    private String thirdPartyId;
    private String thirdPartyName;
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;

    public static FyjsBiddingOpen initialized(FyjsBiddingOpenDto dto) {
        FyjsBiddingOpen data = new FyjsBiddingOpen();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        data.setCreator(dto.getCreator());
        data.setLastEditor(dto.getLastEditor());
        if (dto.getCreateDate() != null) {
            data.setCreateDate(DateUtil.date(dto.getCreateDate()));
        }
        data.setParentId(dto.getParentId());
        data.setThirdPartyId(dto.getThirdPartyId());
        data.setThirdPartyName(dto.getThirdPartyName());
        return data;
    }
}
