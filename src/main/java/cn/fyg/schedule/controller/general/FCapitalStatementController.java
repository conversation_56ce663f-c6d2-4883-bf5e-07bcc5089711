package cn.fyg.schedule.controller.general;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.FAssignment;
import cn.fyg.schedule.pojo.dto.query.Base;
import cn.fyg.schedule.pojo.dto.statement.capital.FCapitalStatementDto;
import cn.fyg.schedule.pojo.dto.statement.capital.FCapitalStatementItemDto;
import cn.fyg.schedule.pojo.dto.statement.capital.FCapitalStatementSubject;
import cn.fyg.schedule.pojo.statement.capital.FCapitalStatement;
import cn.fyg.schedule.pojo.statement.capital.FCapitalStatementBalance;
import cn.fyg.schedule.pojo.statement.capital.FCapitalStatementExpenditure;
import cn.fyg.schedule.pojo.statement.capital.FCapitalStatementIncome;
import cn.fyg.schedule.service.FAssignmentService;
import cn.fyg.schedule.service.statement.capital.FCapitalStatementBalanceService;
import cn.fyg.schedule.service.statement.capital.FCapitalStatementExpenditureService;
import cn.fyg.schedule.service.statement.capital.FCapitalStatementIncomeService;
import cn.fyg.schedule.service.statement.capital.FCapitalStatementService;
import cn.fyg.schedule.utils.GsonUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ArrayUtil;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("statement/capital")
public class FCapitalStatementController {
    private final FCapitalStatementService statementService;
    private final FCapitalStatementIncomeService incomeService;
    private final FCapitalStatementExpenditureService expenditureService;
    private final FCapitalStatementBalanceService balanceService;
    private final FAssignmentService assignmentService;

    public FCapitalStatementController(FCapitalStatementService statementService,
                                       FCapitalStatementIncomeService incomeService,
                                       FCapitalStatementExpenditureService expenditureService,
                                       FCapitalStatementBalanceService balanceService,
                                       FAssignmentService assignmentService) {
        this.statementService = statementService;
        this.incomeService = incomeService;
        this.expenditureService = expenditureService;
        this.balanceService = balanceService;
        this.assignmentService = assignmentService;
    }

    @PostMapping("save")
    public BaseResponse save(@RequestBody String request) {
        FCapitalStatementDto dto = GsonUtil.fromJson(request, FCapitalStatementDto.class);
        FCapitalStatement data = FCapitalStatement.initialize(dto);
        BaseResponse response = statementService.save(data);
        Double cumDayIncome = 0.00, cumDayExpenditure = 0.00, cumDayBalance = 0.00;
        Double cumIncome = 0.00, cumExpenditure = 0.00, cumBalance = 0.00;
        if (response.getCode() == 0) {
            FCapitalStatement tmp = (FCapitalStatement) response.getResult();
            Integer id = tmp.getId();
            Integer voucherId = this.getLastStatementId(dto.getScheduleId(), dto.getCreator());
            Double cumulitiveAmount = 0.00;
            for (FCapitalStatementSubject subject : dto.getArray()) {
                String scheme = subject.getScheme();
                int n = 1;
                for (FCapitalStatementItemDto itemDto : subject.getValue()) {
                    switch (scheme) {
                        case "income":
                            FCapitalStatementIncome income = FCapitalStatementIncome.initialize(itemDto);
                            if (voucherId != null) {
                                cumulitiveAmount = this.getCumulitive(income.getCurrentDayAmount(), scheme, voucherId, income.getCurrencySubject(), null);
                            } else {
                                cumulitiveAmount = income.getCurrentDayAmount();
                            }
                            cumDayIncome += income.getCurrentDayAmount();
                            cumIncome += cumulitiveAmount;
                            income.setCumulativeAmount(cumulitiveAmount);
                            income.setNumber(n);
                            income.setCapitalStatementId(id);
                            incomeService.save(income);
                            break;
                        case "expenditure":
                            FCapitalStatementExpenditure expenditure = FCapitalStatementExpenditure.initialize(itemDto);
                            if (voucherId != null) {
                                cumulitiveAmount = this.getCumulitive(expenditure.getCurrentDayAmount(), scheme, voucherId, expenditure.getCurrencySubject(), null);
                            } else {
                                cumulitiveAmount = expenditure.getCurrentDayAmount();
                            }
                            cumDayExpenditure += expenditure.getCurrentDayAmount();
                            cumExpenditure += cumulitiveAmount;
                            expenditure.setCumulativeAmount(cumulitiveAmount);
                            expenditure.setNumber(n);
                            expenditure.setCapitalStatementId(id);
                            expenditureService.save(expenditure);
                            break;
                        default:
                            FCapitalStatementBalance balance = FCapitalStatementBalance.initialize(itemDto);
                            if (voucherId != null) {
                                cumulitiveAmount = this.getCumulitive(balance.getCurrentDayAmount(), scheme, voucherId, balance.getCurrencySubject(), balance.getAccount());
                            } else {
                                cumulitiveAmount = balance.getCurrentDayAmount();
                            }
                            cumDayBalance += balance.getCurrentDayAmount();
                            cumBalance += cumulitiveAmount;
                            balance.setCumulativeAmount(cumulitiveAmount);
                            balance.setNumber(n);
                            balance.setCapitalStatementId(id);
                            balanceService.save(balance);
                            break;
                    }
                    n ++;
                }
            }
            tmp.setCurrentDayAmountBalance(cumDayBalance);
            tmp.setCumulativeAmountBalance(cumBalance);
            tmp.setCurrentDayAmountExpenditure(cumDayExpenditure);
            tmp.setCumulativeAmountExpenditure(cumExpenditure);
            tmp.setCurrentDayAmountIncome(cumDayIncome);
            tmp.setCumulativeAmountIncome(cumIncome);
            response = statementService.save(tmp);
        }
        return response;
    }

    private Double getCumulitive(Double currentDayAmount, String scheme, Integer id, String subject, String account) {
        Double cumulitiveAmount = 0.00;
        Base query = new Base();
        query.setScheme(scheme);
        query.setSubject(subject);
        query.setAccount(account);
        BaseResponse response = this.getCumulativeAmount(id, query);
        if (response.getCode() == 0) {
            cumulitiveAmount = (Double) response.getResult() + currentDayAmount;
        }
        return cumulitiveAmount;
    }

    @PostMapping("delete")
    public BaseResponse delete(Base query) {
        return statementService.delete(query.getId());
    }

    @PostMapping("item/delete")
    public BaseResponse itemDelete(Base query) {
        BaseResponse response = BaseResponse.initialize();
        switch (query.getScheme()) {
            case "income":
                response = incomeService.delete(query.getId());
                break;
            case "expenditure":
                response = expenditureService.delete(query.getId());
                break;
            default:
                response = balanceService.delete(query.getId());
                break;
        }
        return response;
    }

    @PostMapping("find")
    public BaseResponse findById(Base query) {
        return statementService.findById(query.getId());
    }

    @PostMapping("item/find")
    public BaseResponse findItemById(Base query) {
        return incomeService.findById(query.getId());
    }

    @PostMapping("list")
    public BaseResponse listStatement(Base query) {
        Integer[] ids = this.getHistoryStatementIds(query.getScheduleId(), query.getCreator());
        return statementService.listStatement(ids);
    }

    @GetMapping("list")
    public BaseResponse doGet(@RequestParam Integer id, @RequestParam String creator) {
        Integer[] ids = this.getHistoryStatementIds(id, creator);
        return statementService.listStatement(ids);
    }

    @PostMapping("accounts")
    public BaseResponse getAccounts(Base query) {
        Integer[] ids = this.getHistoryStatementIds(query.getScheduleId(), query.getCreator());
        return balanceService.listAccount(ids);
    }

    @PostMapping("cum")
    public BaseResponse doCumulative(Base query) {

        return getCumulativeResponse(query);
    }

    private BaseResponse getCumulativeResponse(Base query) {
        BaseResponse response = BaseResponse.initialize();
        FCapitalStatementBalance data = new FCapitalStatementBalance();
        data.setCumulativeAmount(0.00);
        response.setResult(data);

       Integer voucherId = this.getLastStatementId(query.getScheduleId(), query.getCreator());

       if (voucherId != null) {
           response = getCumulativeAmount(voucherId, query);
       }

        return response;
    }

    private BaseResponse getCumulativeAmount(Integer id, Base query) {
        BaseResponse response = BaseResponse.initialize();
        response.setCode(1);
        response.setMsg("获取历史数据失败");

        switch (query.getScheme()) {
            case "income":
                response = incomeService.getHistoryAmount(id, query.getSubject());
                break;
            case "expenditure":
                response = expenditureService.getHistoryAmount(id, query.getSubject());
                break;
            default:
                response = balanceService.getHistoryAmount(id, query.getAccount(), query.getSubject());
                break;
        }
        return response;
    }

    private Date getFirstStatementDate() {
        Date firstStatementDate = null;
        FCapitalStatement data = statementService.findFirstStatement();
        if (data != null) {
            firstStatementDate = data.getStatementDate();
        }
        return firstStatementDate;
    }

    private Date getFirstStatementDate(Integer[] ids) {
        Date firstStatementDate = null;
        List<FCapitalStatement> list = statementService.findStatementInIds(ids);
        FCapitalStatement statement = list.get(0);
        return statement.getStatementDate();
    }

    private Integer getLastStatementId(Integer id, String creator) {
        List<FAssignment> list = assignmentService.listBySecheduleIdAndCreator(id, creator);
        if (list != null) {
            for (FAssignment data : list) {
                if (data.getVoucherId() != null && data.getVoucherType() == 2) {
                    return data.getVoucherId();
                }
            }
        }
        return null;
    }

    private Integer[] getHistoryStatementIds(Integer id, String creator) {
        List<Integer> ids = new ArrayList<>();
        List<FAssignment> list = assignmentService.listBySecheduleIdAndCreator(id, creator);
        if (list != null) {
            for (FAssignment data : list) {
                if (data.getVoucherId() != null && data.getVoucherType() == 2) {
                    ids.add(data.getVoucherId());
                }
            }
        }
        return ids.toArray(new Integer[ids.size()]);
    }
}
