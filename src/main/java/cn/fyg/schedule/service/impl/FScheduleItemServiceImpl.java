package cn.fyg.schedule.service.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.schedule.FScheduleItemRepository;
import cn.fyg.schedule.pojo.FScheduleItem;
import cn.fyg.schedule.pojo.wx.ImageChat;
import cn.fyg.schedule.pojo.wx.ImageContent;
import cn.fyg.schedule.pojo.wx.TextChat;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.FScheduleItemService;
import cn.fyg.schedule.utils.ImageUtil;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.awt.image.BufferedImage;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class FScheduleItemServiceImpl implements FScheduleItemService {
    @Autowired private FScheduleItemRepository r;

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FScheduleItem data) {
       return BaseService.save(data, r, FScheduleItem.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Optional<FScheduleItem> result = r.findById(id);
            if (result.isPresent()) {
                baseResponse.setResult(result.get());
            }
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse findByScheduleId(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FScheduleItem> result = r.findByScheduleIdOrderByCreateDate(id);
            baseResponse.setResult(this.itemConversion(result));
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    private List<Object> itemConversion(List<FScheduleItem> items) {
        List<Object> new_list = new ArrayList<>();
        for (FScheduleItem item : items) {
            switch (item.getType()) {
                case "text":
                    TextChat textChat = new TextChat();
                    textChat.setNews_type("text");
                    textChat.setNews_centent(item.getContent());
                    textChat.setNews_createdate(DateUtil.format(item.getCreateDate(), "MM-dd HH:mm"));
                    new_list.add(textChat);
                    break;
                case "image":
                    ImageChat imageChat = new ImageChat();
                    imageChat.setNews_type("image");
                    imageChat.setNews_centent(this.initImageInfo("https://weixin.fyg.cn/fyschedule/image/" + item.getContent()));
                    imageChat.setNews_createdate(DateUtil.format(item.getCreateDate(), "MM-dd HH:mm"));
                    new_list.add(imageChat);
                    break;
            }
        }
        return new_list;
    }

    private ImageContent initImageInfo(String src) {
        BufferedImage bufferedImage = ImageUtil.initImage(src);
        ImageContent imageContent = new ImageContent();
        imageContent.setSrc(src);
        imageContent.setHeight(bufferedImage.getHeight());
        imageContent.setWidth(bufferedImage.getWidth());
        return imageContent;
    }
}
