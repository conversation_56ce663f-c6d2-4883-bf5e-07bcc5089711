package cn.fyg.schedule.service.yx.couthersign.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.yx.countersign.YxDepartmentFunctionRepository;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.yx.countersign.YxDepartmentFunction;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.yx.couthersign.YxDepartmentFunctionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;

@Service
@Slf4j
public class YxDepartmentFunctionServiceImpl implements YxDepartmentFunctionService {
    private final YxDepartmentFunctionRepository r;

    public YxDepartmentFunctionServiceImpl(YxDepartmentFunctionRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(YxDepartmentFunction data) {
        BaseResponse response = BaseResponse.initialize();
        try {
            response.setResult(BaseService.saveWithR(data, r, YxDepartmentFunction.class));
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        try {
            BaseService.deleteWithR(id, r);
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse deleteByDepartmentID(Integer departmentId) {
        BaseResponse response = BaseResponse.initialize();
        try {
            response.setResult(deleteWhereDepartmentIdEq(departmentId));
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }

    private Integer deleteWhereDepartmentIdEq(Integer departmentId) throws MyException {
        try {
            return r.deleteWhereDepartmentIdEq(departmentId);
        } catch (JpaSystemException e) {
            throw new MyException(500, "操作失败");
        }
    }

    @Override
    public BaseResponse listByDepartmentId(Integer departmentId) {
        BaseResponse response = BaseResponse.initialize();
        try {
            response.setResult(findByDepartmentId(departmentId));
        } catch (MyException e) {
            BaseService.setResponse(response, e.getCode(), e.getErrMsg());
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse listAll() {
        BaseResponse response = BaseResponse.initialize();
        try {
            List<YxDepartmentFunction> data = findAll();
            // 预加载懒加载的集合，避免LazyInitializationException
            data.forEach(this::preloadLazyCollections);
            response.setResult(data);
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }

    private List<YxDepartmentFunction> findAll() throws MyException {
        try {
            return (List<YxDepartmentFunction>) r.findAll();
        } catch (JpaSystemException e) {
            log.error(e.getMessage());
            throw new MyException(500, "查询失败");
        }
    }
    @Override
    public List<YxDepartmentFunction> findByDepartmentId(Integer departmentId) throws MyException {
        try {
            return r.findByDepartmentIdOrderBySequence(departmentId);
        } catch (JpaSystemException e) {
            throw new MyException(500, "操作失败");
        }
    }

    @Override
    public List<Integer> listDepartmentFunctionId(Integer departmentId) throws MyException {
        try {
            return r.listDepartmentFunctionId(departmentId);
        } catch (JpaSystemException e) {
            throw new MyException(500, "操作失败");
        }
    }

    /**
     * 预加载懒加载的集合，避免LazyInitializationException
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    private void preloadLazyCollections(YxDepartmentFunction function) {
        if (function != null) {
            // 触发懒加载 - 我们只需要触发加载，不需要使用返回值
            // 注意：这些字段有@JsonIgnore注解，通常不会被序列化，但为了安全起见还是预加载
            if (function.getDepartment() != null) {
                function.getDepartment().getId(); // 触发懒加载
            }
            if (function.getMatter() != null) {
                function.getMatter().getId(); // 触发懒加载
            }
        }
    }
}
