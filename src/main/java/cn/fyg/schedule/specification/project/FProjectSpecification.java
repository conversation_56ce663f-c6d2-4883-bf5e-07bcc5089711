package cn.fyg.schedule.specification.project;

import cn.fyg.schedule.pojo.project.management.FProject;
import cn.fyg.schedule.utils.SpecificationUtil;
import org.springframework.data.jpa.domain.Specification;

import java.util.Date;

public class FProjectSpecification {
    public static Specification<FProject> ifContain(String column, String expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.like(root.get(column), SpecificationUtil.contains(expression)));
    }

    public static Specification<FProject> ifDateBetween(String column, Date startDate, Date endDate) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.between(root.get(column), startDate, endDate));
    }

    public static Specification<FProject> ifEq(String column, Integer expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }

    public static Specification<FProject> ifEq(String column, String expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }
}
