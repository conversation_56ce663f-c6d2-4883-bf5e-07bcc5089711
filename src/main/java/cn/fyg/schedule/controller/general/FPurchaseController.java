package cn.fyg.schedule.controller.general;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.FPurchase;
import cn.fyg.schedule.pojo.FPurchaseItem;
import cn.fyg.schedule.pojo.dto.FPurchaseDto;
import cn.fyg.schedule.pojo.dto.FPurchaseItemDto;
import cn.fyg.schedule.pojo.dto.query.Base;
import cn.fyg.schedule.service.sheets.FPurchaseItemService;
import cn.fyg.schedule.service.sheets.FPurchaseService;
import cn.fyg.schedule.utils.GsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("purchase")
public class FPurchaseController {
    @Autowired private FPurchaseService purchaseService;
    @Autowired private FPurchaseItemService purchaseItemService;

    @PostMapping("save")
    public BaseResponse save(@RequestBody String request) {
        FPurchaseDto dto = GsonUtil.fromJson(request, FPurchaseDto.class);
        FPurchase data = FPurchase.initialize(dto);
        BaseResponse response = purchaseService.save(data);
        if (response.getCode() == 0) {
            FPurchase tmp = (FPurchase) response.getResult();
            int i = 1;
            for (FPurchaseItemDto itemDto : dto.getInfos()) {
                itemDto.setAppid(tmp.getId());
                itemDto.setNumber(i);
                FPurchaseItem item = FPurchaseItem.initialize(itemDto);
                BaseResponse response2 = purchaseItemService.save(item);
                i++;
            }
        }
        return response;
    }

    @PostMapping("find")
    public BaseResponse findById(Base query) {
        return purchaseService.findById(query.getId());
    }

    @PostMapping("item/find")
    public BaseResponse findItem(Base query) {
        return purchaseItemService.list(query.getId());
    }

    @PostMapping("item/delete")
    public BaseResponse deleteItem(Base query) {
        return purchaseItemService.delete(query.getId());
    }
}
