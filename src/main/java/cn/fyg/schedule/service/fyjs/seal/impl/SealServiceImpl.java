package cn.fyg.schedule.service.fyjs.seal.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.fyjs.sealManagement.FJsSealManagementDao;
import cn.fyg.schedule.pojo.dto.project.fyjs.sealManagement.FJsSealManagementDto;
import cn.fyg.schedule.pojo.project.fyjs.sealManagement.FJsSealManagement;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.fyjs.seal.SealService;
import cn.fyg.schedule.specification.FJsSealManagementSpecification;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import jdk.nashorn.internal.runtime.options.Option;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.Optional;

@Service
@Slf4j
public class SealServiceImpl implements SealService {
    private final FJsSealManagementDao r;

    public SealServiceImpl(FJsSealManagementDao r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FJsSealManagement data) {
        BaseResponse response = BaseService.save(data, r, FJsSealManagement.class);
        // 如果保存成功，预加载懒加载的集合，避免序列化时的LazyInitializationException
        if (response.getCode() == 0 && response.getResult() instanceof FJsSealManagement) {
            FJsSealManagement savedSeal = (FJsSealManagement) response.getResult();
            preloadLazyCollections(savedSeal);
        }
        return response;
    }

    @Override
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse findById(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Optional<FJsSealManagement> optional = r.findById(id);
            if (optional.isPresent()) {
                FJsSealManagement seal = optional.get();
                // 预加载懒加载的items集合，避免LazyInitializationException
                preloadLazyCollections(seal);
                baseResponse.setResult(seal);
            }
        } catch (Exception e) {
            log.info(e.getMessage());
            baseResponse.setCode(1);
            baseResponse.setMsg("查询出错");
        }
        return baseResponse;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse filter(FJsSealManagementDto dto, Integer currentPage, Integer pageSize, String order, String columnKey) {
        BaseResponse response = BaseResponse.initialize();
        try {
            Sort sort = Sort.unsorted();
            if (columnKey != null) {
                sort = Sort.by(order.equals("ascend") ? Sort.Direction.ASC : Sort.Direction.DESC, columnKey);
            }
            Pageable pageable = PageRequest.of(currentPage, pageSize, sort);
            Specification<FJsSealManagement> specification = getSpecification(dto);
            Page<FJsSealManagement> data = r.findAll(specification, pageable);
            // 预加载懒加载的items集合，避免LazyInitializationException
            data.getContent().forEach(this::preloadLazyCollections);
            response.setResult(data);
        } catch (Exception e) {
            log.info(e.getMessage());
            response.setCode(1);
            response.setMsg("failed");
        }
        return response;
    }

    private Specification<FJsSealManagement> getSpecification(FJsSealManagementDto dto) {
        Specification<FJsSealManagement> specification = Specification.where(StrUtil.isEmptyIfStr(dto.getName()) ? null : FJsSealManagementSpecification.ifContain("name", dto.getName()))
                .and(dto.getSequence() == null ? null : FJsSealManagementSpecification.ifNumberEq("sequence", dto.getSequence()))
                .and(StrUtil.isEmptyIfStr(dto.getCustodianDepartment()) ? null : FJsSealManagementSpecification.ifContain("custodianDepartment", dto.getCustodianDepartment()))
                .and(StrUtil.isEmptyIfStr(dto.getResponsibleCustodian()) ? null : FJsSealManagementSpecification.ifContain("responsibleCustodian", dto.getResponsibleCustodian()))
                .and(dto.getReceivingDate() == null ? null : FJsSealManagementSpecification.ifDateEq("receivingDate", DateUtil.date(dto.getReceivingDate())))
                .and(dto.getRecycleDate() == null ? null : FJsSealManagementSpecification.ifDateEq("recycleDate", DateUtil.date(dto.getRecycleDate())))
                .and(dto.getLiabilityStatement() == null ? null : FJsSealManagementSpecification.ifNumberEq("liabilityStatement", dto.getLiabilityStatement()))
                .and(dto.getPaymentStatus() == null ? null : FJsSealManagementSpecification.ifNumberEq("paymentStatus", dto.getPaymentStatus()))
                .and(StrUtil.isEmptyIfStr(dto.getRemarks()) ? null : FJsSealManagementSpecification.ifContain("remarks", dto.getRemarks()));
        return specification;
    }

    /**
     * 预加载懒加载的集合，避免LazyInitializationException
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    private void preloadLazyCollections(FJsSealManagement seal) {
        if (seal != null) {
            // 触发懒加载
            if (seal.getItems() != null) {
                seal.getItems().size();
            }
        }
    }
}
