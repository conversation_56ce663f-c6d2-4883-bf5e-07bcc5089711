package cn.fyg.schedule.service.yx.couthersign;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.dto.yx.countersign.YxCountersignDetailDto;
import cn.fyg.schedule.pojo.yx.countersign.YxCountersignDetail;

import java.util.List;

public interface YxCountersignDetailService {
    BaseResponse save(YxCountersignDetailDto dto);
    BaseResponse delete(Integer id);
    BaseResponse deleteByParentId(Integer parentId);
    BaseResponse listByParentId(Integer parentId);
    BaseResponse listAuditorByParentId(Integer parentId);
    List<YxCountersignDetail> listByParentIdAndFunctionIds(Integer parentId, List<Integer> ids) throws MyException;
    Integer updateAuditor(String auditorId, Integer parentId, List<Integer> ids) throws MyException;
    Integer updateAuditor(String auditorId, Integer parentId, Integer functionId) throws MyException;
    Integer updateDetail(String auditorSign, Integer judgment, Integer id) throws MyException;
    Integer updateDetail(String auditorSign, String comment, Integer judgment, Integer id) throws MyException;
    BaseResponse updateDetail(String auditData);
}
