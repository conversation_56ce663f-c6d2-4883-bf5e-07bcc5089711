package cn.fyg.schedule.service.construction.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.construction.FBuildingPermitItemRepository;
import cn.fyg.schedule.pojo.construction.FBuildingPermitItem;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.construction.FBuildingPermitItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;

@Service
@Slf4j
public class FBuildingPermitItemServiceImpl implements FBuildingPermitItemService {
    private final FBuildingPermitItemRepository r;
    public FBuildingPermitItemServiceImpl(FBuildingPermitItemRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FBuildingPermitItem data) {
        return BaseService.save(data, r, FBuildingPermitItem.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }
}
