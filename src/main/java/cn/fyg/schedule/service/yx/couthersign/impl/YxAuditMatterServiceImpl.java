package cn.fyg.schedule.service.yx.couthersign.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.yx.countersign.YxAuditMatterRepository;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.yx.countersign.YxAuditMatter;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.yx.couthersign.YxAuditMatterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;

@Service
@Slf4j
public class YxAuditMatterServiceImpl implements YxAuditMatterService {
    private final YxAuditMatterRepository r;

    public YxAuditMatterServiceImpl(YxAuditMatterRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(YxAuditMatter data) {
        BaseResponse response = BaseResponse.initialize();
        try {
            response.setResult(BaseService.saveWithR(data, r, YxAuditMatter.class));
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        try {
            BaseService.deleteWithR(id, r);
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }

    @Override
    public BaseResponse listAll() {
        BaseResponse response = BaseResponse.initialize();
        try {
            response.setResult(findAll());
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }

    private List<YxAuditMatter> findAll() throws MyException {
        try {
            return (List<YxAuditMatter>) r.findAll();
        } catch (JpaSystemException e) {
            log.error(e.getMessage());
            throw new MyException(500, "查询失败");
        }
    }
}
