package cn.fyg.schedule.pojo.statement.capital;

import cn.fyg.schedule.pojo.dto.statement.capital.FCapitalStatementItemDto;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

@Getter
@Setter
@ToString
@Entity
@Table(name = "fcapitalstatementexpenditure")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class FCapitalStatementExpenditure {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Column(name = "capital_statement_id")
    private Integer capitalStatementId;
    @Column(name = "number")
    private Integer number;
    @Column(name = "currency_subject")
    private String currencySubject;
    @Column(name = "current_day_amount")
    private Double currentDayAmount;
    @Column(name = "cumulative_amount")
    private Double cumulativeAmount;
    @Column(name = "remarks")
    private String remarks;
    @Column(name = "create_date")
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @Column(name = "update_date")
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;

//    @ManyToOne(fetch = FetchType.LAZY)
//    @JoinColumn(name = "capital_statement_id", insertable = false, updatable = false)
//    @ToString.Exclude
//    private FCapitalStatement fCapitalStatement;

    public static FCapitalStatementExpenditure initialize(FCapitalStatementItemDto dto) {
        FCapitalStatementExpenditure data = new FCapitalStatementExpenditure();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        if (dto.getCreateDate() != null) {
            data.setCreateDate(DateUtil.parse(dto.getCreateDate()));
        }
        data.setCapitalStatementId(dto.getCaptialStatementId());
        data.setCurrencySubject(dto.getCurrencySubject());
        data.setCurrentDayAmount(dto.getCurrentDayAmount());
        data.setCumulativeAmount(dto.getCumulativeAmount());
        data.setRemarks(dto.getRemarks());
        return data;
    }
}
