package cn.fyg.schedule.service.construction;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.construction.FyConstructionLicenseDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;

public interface FyConstructionLicenseService {
    BaseResponse save(FyConstructionLicenseDto dto);
    BaseResponse delete(Integer id);
    BaseResponse findById(Integer id);
    BaseResponse filter(FyConstructionLicenseDto dto, Pagination pagination);
}
