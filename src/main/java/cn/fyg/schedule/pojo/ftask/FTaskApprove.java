package cn.fyg.schedule.pojo.ftask;

import cn.fyg.schedule.pojo.dto.ftask.FTaskApproveDto;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Setter
@Getter
@ToString
@Table(name = "f_task_approve")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class FTaskApprove {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Column(name = "task_id")
    private Integer taskId;
    private String creator;
    private String creatorName;
    private String comment;
    private String signature;
    private String phoneNumber;
    private String avatar;
    @Column(name = "create_date")
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @Column(name = "update_date")
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "task_approve_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    @ToString.Exclude
    private List<FTaskApproveAttachment> items = new ArrayList<>();

    public static FTaskApprove initialize(FTaskApproveDto dto) {
        FTaskApprove data = new FTaskApprove();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        data.setTaskId(dto.getTaskId());
        data.setCreator(dto.getCreator());
        data.setCreatorName(dto.getCreatorName());
        data.setComment(dto.getComment());
        data.setSignature(dto.getSignature());
        data.setPhoneNumber(dto.getPhoneNumber());
        data.setAvatar(dto.getAvatar());
        if (dto.getCreateDate() != null) {
            data.setCreateDate(DateUtil.parse(dto.getCreateDate()));
        }
        return data;
    }
}
