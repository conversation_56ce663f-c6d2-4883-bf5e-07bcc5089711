package cn.fyg.schedule.controller.xincai;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.project.yx.security.notification.YxSecurityNotiRecordDto;
import cn.fyg.schedule.pojo.dto.project.yx.security.notification.YxSecurityNotificationDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.pojo.project.xincai.noti.XcNotiItem;
import cn.fyg.schedule.pojo.project.xincai.noti.XcNotiRecordItem;
import cn.fyg.schedule.pojo.project.xincai.noti.XcNotiRecordSign;
import cn.fyg.schedule.pojo.project.xincai.noti.XcNotiSign;
import cn.fyg.schedule.service.sheets.xincai.notification.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("xc/notification")
public class XcNotificationController {
    private final XcNotificationService notificationService;
    private final XcNotiRecordService recordService;
    private final XcNotiItemService itemService;
    private final XcNotiSignService signService;
    private final XcNotiRecordSignService recordSignService;
    private final XcNotiRecordItemService recordItemService;

    public XcNotificationController(XcNotificationService notificationService, XcNotiRecordService recordService, XcNotiItemService itemService, XcNotiSignService signService, XcNotiRecordSignService recordSignService, XcNotiRecordItemService recordItemService) {
        this.notificationService = notificationService;
        this.recordService = recordService;
        this.itemService = itemService;
        this.signService = signService;
        this.recordSignService = recordSignService;
        this.recordItemService = recordItemService;
    }

    @PostMapping("events")
    public BaseResponse getEventNameList(Integer type) {
        return notificationService.getEventNameList(type);
    }

    @PostMapping("filter")
    public BaseResponse filter(YxSecurityNotificationDto dto, Pagination pagination) {
        return notificationService.filter(dto, pagination);
    }

    @PostMapping("find/id")
    public BaseResponse findById(Integer id) {
        return notificationService.findById(id);
    }

    @PostMapping ("save")
    public BaseResponse saveNotification(YxSecurityNotificationDto dto) {
        return notificationService.save(dto);
    }

    @PostMapping("delete")
    public BaseResponse deleteNotification(Integer id) {
        return notificationService.delete(id);
    }

    @PostMapping("find/item")
    public BaseResponse findItems(Integer id) {
        return itemService.findByParentId(id);
    }

    @PostMapping("delete/item")
    public BaseResponse deleteItem(Integer id) {
        return itemService.delete(id);
    }

    @PostMapping("save/item")
    public BaseResponse saveItem(XcNotiItem data) {
        return itemService.save(data);
    }

    @PostMapping("find/signature")
    public BaseResponse findSignature(Integer id) {
        return signService.findByParentId(id);
    }

    @PostMapping("delete/signature")
    public BaseResponse deleteSignature(Integer id) {
        return signService.delete(id);
    }

    @PostMapping("save/signature")
    public BaseResponse saveSignature(XcNotiSign data) {
        return signService.save(data);
    }

    @PostMapping("save/record")
    public BaseResponse saveRecord(YxSecurityNotiRecordDto dto) {
        return recordService.save(dto);
    }

    @PostMapping("find/record")
    public BaseResponse findRecord(Integer id) {
        return recordService.findByParentId(id);
    }

    @PostMapping("find/record/status")
    public BaseResponse findRecordIfApproved(Integer id, Integer recordStatus) {
        return recordService.ifApproved(id, recordStatus);
    }

    @PostMapping("delete/record")
    public BaseResponse deleteRecord(Integer id) {
        return recordService.delete(id);
    }

    @PostMapping("find/record/item")
    public BaseResponse findRecordItem(Integer id) {
        return recordItemService.findByParentId(id);
    }

    @PostMapping("save/record/item")
    public BaseResponse saveRecordItem(XcNotiRecordItem data) {
        return recordItemService.save(data);
    }

    @PostMapping("delete/record/item")
    public BaseResponse deleteRecordItem(Integer id) {
        return recordItemService.delete(id);
    }

    @PostMapping("find/record/sign")
    public BaseResponse findRecordSign(Integer id) {
        return recordSignService.findByParentId(id);
    }

    @PostMapping("save/record/sign")
    public BaseResponse saveRecordSign(XcNotiRecordSign data) {
        return recordSignService.save(data);
    }

    @PostMapping("delete/record/sign")
    public BaseResponse deleteRecordSign(Integer id) {
        return recordSignService.deleteByParentId(id);
    }
}
