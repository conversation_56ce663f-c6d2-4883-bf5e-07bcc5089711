package cn.fyg.schedule.service.construction;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.construction.FBuildingPermit;

import java.util.List;

public interface FBuildingPermitService {
    BaseResponse save(FBuildingPermit data);
    BaseResponse batchSave(List<FBuildingPermit> data, String creator);
    BaseResponse findById(Integer id);
    BaseResponse delete(Integer id);
    BaseResponse getCompanyHistoryList();
    BaseResponse listAll();
    BaseResponse listCompanyIn(List<String> companies, String approvalStatus);
}
