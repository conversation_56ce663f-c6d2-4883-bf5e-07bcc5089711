package cn.fyg.schedule.pojo.project.management;

import cn.fyg.schedule.pojo.dto.project.management.FProjectProgressDto;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Setter
@Getter
@ToString
@Table(name = "f_project_progress")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class FProjectProgress {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Column(name = "parent_id")
    private Integer parentId;
    private String name;
    @Column(name = "achieve_date")
    @Temporal(TemporalType.DATE)
    private Date achieveDate;
    @Column(name = "create_date")
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "parent_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    @ToString.Exclude
    List<FProjectProgressItem> items = new ArrayList<>();

    public static FProjectProgress initialized(FProjectProgressDto dto) {
        FProjectProgress data = new FProjectProgress();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        if (dto.getCreateDate() != null) {
            data.setCreateDate(DateUtil.date(dto.getCreateDate()));
        }
        if (dto.getAchieveDate() != null) {
            data.setAchieveDate((DateUtil.date(dto.getAchieveDate())));
        }
        data.setParentId(dto.getParentId());
        data.setName(dto.getName());
        return data;
    }
}
