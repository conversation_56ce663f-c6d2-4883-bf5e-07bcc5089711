package cn.fyg.schedule.specification.yx;

import cn.fyg.schedule.pojo.project.yuanXin.security.notification.YxSecurityNotification;
import cn.fyg.schedule.utils.SpecificationUtil;
import org.springframework.data.jpa.domain.Specification;

import java.util.Date;

public class YxSecurityNotificationSpecification {
    public static Specification<YxSecurityNotification> ifContain(String column, String expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.like(root.get(column), SpecificationUtil.contains(expression)));
    }

    public static Specification<YxSecurityNotification> ifEq(String column, String expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }

    public static Specification<YxSecurityNotification> ifEq(String column, Integer expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }
    public static Specification<YxSecurityNotification> ifDateEq(String column, Date expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }
    public static Specification<YxSecurityNotification> ifDateBetween(String column, Date begin, Date end) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.between(root.get(column), begin, end));
    }
}
