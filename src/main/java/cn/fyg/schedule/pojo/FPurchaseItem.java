package cn.fyg.schedule.pojo;

import cn.fyg.schedule.pojo.dto.FPurchaseItemDto;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import lombok.Data;

import javax.persistence.*;

@Data
@Entity
@Table(name = "fpurchaseitem")
public class FPurchaseItem {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    private Integer number;
    private String name;
    private String info;
    private Integer counts;
    private Double budget;
    private Double price;
    private String supplier;
    private Integer appid;
    @Transient
    private Integer key = RandomUtil.randomInt();

    public static FPurchaseItem initialize(FPurchaseItemDto dto) {
        Double value = 0.00;
        FPurchaseItem data = new FPurchaseItem();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        data.setNumber(dto.getNumber());
        data.setName(dto.getName());
        data.setInfo(dto.getInfo());
        data.setCounts(dto.getCounts());
        data.setBudget(Convert.toDouble(dto.getBudget(), value));
        data.setPrice(Convert.toDouble(dto.getPrice(), value));
        data.setSupplier(dto.getSupplier());
        data.setAppid(dto.getAppid());
        return data;
    }
}
