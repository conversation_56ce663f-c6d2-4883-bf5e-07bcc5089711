package cn.fyg.schedule.service.sheets.yl.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.ylong.FYinLongItemRepository;
import cn.fyg.schedule.dao.documnets.ylong.FYinLongSealRepository;
import cn.fyg.schedule.dao.documnets.ylong.FYinLongSignatureRepository;
import cn.fyg.schedule.pojo.project.yinLong.FYinLongItem;
import cn.fyg.schedule.pojo.project.yinLong.FYinLongSeal;
import cn.fyg.schedule.pojo.project.yinLong.FYinLongSignature;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.sheets.yl.FYinLongSealService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class FYinLongSealServiceImpl implements FYinLongSealService {
    private final FYinLongSealRepository sealR;
    private final FYinLongItemRepository itemR;
    private final FYinLongSignatureRepository signatureR;
    public FYinLongSealServiceImpl(FYinLongSealRepository sealR,
                                   FYinLongItemRepository itemR,
                                   FYinLongSignatureRepository signatureR) {
        this.sealR = sealR;
        this.itemR = itemR;
        this.signatureR = signatureR;
    }
    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse saveSeal(FYinLongSeal data) {
        return BaseService.save(data, sealR, FYinLongSeal.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse deleteSeal(Integer id) {
        return BaseService.delete(id, sealR);
    }

    @Override
    public BaseResponse listAllSeal() {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FYinLongSeal> data = sealR.findAllByOrderByCreateDateDesc();
            baseResponse.setResult(data);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse getDepartmentNameHistoryList() {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<String> data = sealR.getDepartmentNameHistoryList();
            baseResponse.setResult(data);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse getAuditDeptHistoryList() {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<String> data = sealR.getAuditDeptHistoryList();
            baseResponse.setResult(data);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse getSealTypeHistoryList() {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<String> data = sealR.getSealTypeHistoryList();
            baseResponse.setResult(data);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Optional<FYinLongSeal> data = sealR.findById(id);
            data.ifPresent(baseResponse::setResult);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse saveItem(FYinLongItem data) {
        return BaseService.save(data, itemR, FYinLongItem.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse deleteItem(Integer id) {
        return BaseService.delete(id, itemR);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse saveSignature(FYinLongSignature data) {
        return BaseService.save(data, signatureR, FYinLongSignature.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse deleteSignature(Integer id) {
        return BaseService.delete(id, signatureR);
    }
}
