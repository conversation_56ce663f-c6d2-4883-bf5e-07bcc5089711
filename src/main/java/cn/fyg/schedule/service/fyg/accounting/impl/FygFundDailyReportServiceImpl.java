package cn.fyg.schedule.service.fyg.accounting.impl;

import cn.fyg.schedule.dao.fyg.accounting.FygFundDailyReportRepository;
import cn.fyg.schedule.enums.fyg.accounting.BankAccountType;
import cn.fyg.schedule.enums.fyg.accounting.DimensionType;
import cn.fyg.schedule.enums.fyg.accounting.EquityType;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.dto.fyg.accounting.report.*;
import cn.fyg.schedule.pojo.fyg.accounting.FygFundDailyReport;
import cn.fyg.schedule.reponse.BaseResponse;
import cn.fyg.schedule.service.fyg.accounting.FygFundDailyReportService;
import cn.fyg.schedule.utils.BigDecimalUtils;
import cn.fyg.schedule.utils.ToEntityUtil;
import cn.hutool.Hutool;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FygFundDailyReportServiceImpl implements FygFundDailyReportService {
    private static final String TARGET_COMPANY = "方远房地产集团有限公司";
    public static final int BANK_INCOME_DIMENSION = 6;
    public static final int BANK_EXPENSE_DIMENSION = 17;
    public static final int INNER_INCOME_DIMENSION = 4;
    public static final int INNER_EXPENSE_DIMENSION = 15;
    public static final int OUTER_INCOME_DIMENSION = 5;
    public static final int OUTER_EXPENSE_DIMENSION = 16;
    private final FygFundDailyReportRepository r;

    public FygFundDailyReportServiceImpl(FygFundDailyReportRepository r) {
        this.r = r;
    }

    @Override
    public BaseResponse<List<FundSummaryWithNoteDTO>> getFundSummaryWithNote(Integer projectId, Long transactionDate) {
        BaseResponse<List<FundSummaryWithNoteDTO>> resp = new BaseResponse<>();
        try {
            List<FundSummaryWithNoteDTO> result = assembly(projectId, transactionDate);
            resp.initialize(200, "查询成功", result);
        } catch (MyException e) {
            log.error(e.getMessage());
            resp.initialize(e.getCode(), e.getMessage(), null);
        }
        return resp;
    }

    @Override
    public BaseResponse<DailyReportDTO> save(DailyReportDTO dto) {
        BaseResponse<DailyReportDTO> resp = new BaseResponse<>();
        try {
            FygFundDailyReport entity = toEntity(dto);
            if (entity == null) {
                resp.initialize(5001, "数据转换失败", null);
            } else {
                FygFundDailyReport result;
                if (dto.getId() == null) {
                    result = r.save(entity);
                } else {
                    result = update(entity, dto.getId());
                }
                resp.initialize(200, "保存成功", toDto(result));
            }

        } catch (MyException e) {
            resp.initialize(e.getCode(), e.getMessage(), null);
        }
        return resp;
    }

    @Override
    public BaseResponse<List<EquityStructureDTO>> getEquityStructure(Integer projectId) {
        BaseResponse<List<EquityStructureDTO>> resp = new BaseResponse<>();
        try {
            List<EquitySummaryDTO> ming_gu = getEquitySummary(projectId, EquityType.MING_GU);
            List<EquitySummaryDTO> an_gu = getEquitySummary(projectId, EquityType.AN_GU);
            // 更新目标公司的 balance
            if (!an_gu.isEmpty()) {
                updateTargetCompanyBalance(ming_gu, an_gu);
            }

            List<FinancingInstitutionDTO> financingInstitutionBank = getFinancingInstitution(projectId, BANK_INCOME_DIMENSION, BANK_EXPENSE_DIMENSION);
            List<FinancingInstitutionDTO> financingInstitutionInner = getFinancingInstitution(projectId, INNER_INCOME_DIMENSION, INNER_EXPENSE_DIMENSION);
            List<FinancingInstitutionDTO> financingInstitutionOuter = getFinancingInstitution(projectId, OUTER_INCOME_DIMENSION, OUTER_EXPENSE_DIMENSION);
            List<EquityStructureDTO> result = assembly(ming_gu, an_gu, financingInstitutionBank, financingInstitutionInner, financingInstitutionOuter);
            resp.initialize(200, "查询成功", result);
        } catch (MyException e) {
            resp.initialize(e.getCode(), e.getMessage(), null);
        }
        return resp;
    }

    private void updateTargetCompanyBalance(List<EquitySummaryDTO> mingGu, List<EquitySummaryDTO> anGu) {
        BigDecimal anGuTotalBalance = calculateTotalAmount(anGu, new Function<EquitySummaryDTO, BigDecimal>() {
            @Override
            public BigDecimal apply(EquitySummaryDTO equitySummaryDTO) {
                return equitySummaryDTO.getBalance();
            }
        });

        BigDecimal mingGuOtherBalance = BigDecimal.ZERO;
        for (EquitySummaryDTO dto : mingGu) {
            if (!TARGET_COMPANY.equals(dto.getName())) {
                mingGuOtherBalance = mingGuOtherBalance.add(dto.getBalance());
            }
        }

        for (EquitySummaryDTO dto : mingGu) {
            if (TARGET_COMPANY.equals(dto.getName())) {
                dto.setBalance(anGuTotalBalance.subtract(mingGuOtherBalance));
                break;
            }
        }
    }

    private <T> BigDecimal calculateTotalAmount(List<T> items, Function<T, BigDecimal> amountExtractor) {
        BigDecimal total = BigDecimal.ZERO;
        for (T item : items) {
            BigDecimal value = amountExtractor.apply(item);
            if (value != null) {
                total = total.add(value);
            }
        }
        return total;
    }

    private List<EquityStructureDTO> assembly(List<EquitySummaryDTO> ming_gu, List<EquitySummaryDTO> an_gu,
                                              List<FinancingInstitutionDTO> financingInstitutionBank,
                                              List<FinancingInstitutionDTO> financingInstitutionInner,
                                              List<FinancingInstitutionDTO> financingInstitutionOuter)
            throws MyException {
        List<EquityStructureDTO> result = new ArrayList<>();

        try {
            // 校验并处理各部分数据
            if (validateInputs(ming_gu, "ming_gu")) {
                processSection(result, "占用工商股东资金净额", ming_gu, wrapProcessor(this::processEquitySummary));
            }

            if (validateInputs(an_gu, "an_gu")) {
                processSection(result, "占用实际股东资金净额", an_gu, wrapProcessor(this::processEquitySummary));
            }

            if (validateInputs(financingInstitutionBank, "financingInstitutionBank")) {
                processSection(result, "金融机构融资净额", financingInstitutionBank, wrapProcessor(this::processFinancingInstitution));
            }


            // 项目公司借款净额
            List<FinancingInstitutionDTO> combinedList = new ArrayList<>();
            if (validateInputs(financingInstitutionInner, "financingInstitutionInner")) {
                combinedList.addAll(financingInstitutionInner);
            }
            if (validateInputs(financingInstitutionOuter, "financingInstitutionOuter")) {
                combinedList.addAll(financingInstitutionOuter);
            }

            BigDecimal innerOuterTotal = processSection(
                    result,
                    "项目公司借款净额",
                    combinedList,
                    wrapProcessor(this::processFinancingInstitution)
            );

            // 添加合计行
//            if (innerOuterTotal.compareTo(BigDecimal.ZERO) != 0) {
//                result.add(new EquityStructureDTO(
//                        "合计", "", "", "",
//                        innerOuterTotal.toPlainString(), // 已是万元单位，无需再次转换
//                        ""
//                ));
//            }

        } catch (IllegalArgumentException e) {
            throw new MyException(400, "非法参数: " + e.getMessage());
        } catch (Exception e) {
            throw new MyException(500, "处理数据时发生异常: " + e.getMessage());
        }

        return result;
    }

    private boolean validateInputs(Object input, String paramName) {
        if (input == null || (input instanceof Collection && ((Collection<?>) input).isEmpty())) {
            log.error("Warning: {} 为空，将跳过该部分数据的处理。", paramName);
            return false;
        }
        return true;
    }

    private <T> BigDecimal processSection(List<EquityStructureDTO> result, String sectionTitle,
                                          List<T> items, Function<T, EquityStructureDTO> processor) throws MyException {
        if (items == null || items.isEmpty()) return BigDecimal.ZERO;

//        result.add(new EquityStructureDTO("section", sectionTitle));
//        int index = 1;
        BigDecimal total = BigDecimal.ZERO;
//
//        for (T item : items) {
//            if (item == null) continue;
//            EquityStructureDTO dto;
//            try {
//                dto = processor.apply(item);
//            } catch (Exception e) {
//                throw new MyException(500, "处理 " + sectionTitle + " 时发生错误: " + e.getMessage());
//            }
//            if (dto != null) {
//                dto.setIndex(String.valueOf(index++));
//                result.add(dto);
//
//                // 累加金额 (dto.getAmount2 已是万元单位)
//                try {
//                    total = total.add(new BigDecimal(dto.getAmount2()));
//                } catch (NumberFormatException e) {
//                    throw new MyException(500, "金额格式错误: " + dto.getAmount2());
//                }
//            }
//        }
        // 计算总金额
        List<EquityStructureDTO> sectionItems = new ArrayList<>();
        int index = 1;
        for (T item : items) {
            if (item == null) continue;
            EquityStructureDTO dto;
            try {
                dto = processor.apply(item);
            } catch (Exception e) {
                throw new MyException(500, "处理 " + sectionTitle + " 时发生错误: " + e.getMessage());
            }
            if (dto != null) {
                dto.setIndex(String.valueOf(index++));
                sectionItems.add(dto);

                // 累加金额 (dto.getAmount2 已是万元单位)
                try {
                    total = total.add(new BigDecimal(dto.getAmount2()));
                } catch (NumberFormatException e) {
                    throw new MyException(500, "金额格式错误: " + dto.getAmount2());
                }
            }
        }

        // 添加 Section 标题条目并带总金额
        result.add(new EquityStructureDTO("section", sectionTitle, total.toPlainString()));

        // 添加 Section 内的明细条目
        result.addAll(sectionItems);

        return total;
    }


    private EquityStructureDTO processEquitySummary(EquitySummaryDTO item) throws MyException {
        // 如果余额为 0，则不添加该条目
        if (BigDecimalUtils.isEqualToZero(item.getBalance())) {
            return null;
        }
        return new EquityStructureDTO(
                "", // index will be set later
                item.getName(),
                item.getEquityRatio() != null ? item.getEquityRatio().toPlainString() : "",
                "",
                BigDecimalUtils.toWan(item.getBalance()), // 转换为万元单位
                ""
        );
    }

    private EquityStructureDTO processFinancingInstitution(FinancingInstitutionDTO item) throws MyException {
        if (BigDecimalUtils.isGreaterThanZero(item.getBalance())) {
            return null;
        }
        return new EquityStructureDTO(
                "", // index will be set later
                item.getName(),
                item.getAnnualInterestRate() != null ? item.getAnnualInterestRate().toPlainString() : "",
                "",
                BigDecimalUtils.toWan(item.getBalance()), // 转换为万元单位
                ""
        );
    }

    private <T, R> Function<T, R> wrapProcessor(FunctionWithException<T, R> function) {
        return item -> {
            try {
                return function.apply(item);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        };
    }

    @FunctionalInterface
    private interface FunctionWithException<T, R> {
        R apply(T t) throws Exception;
    }


    private List<EquitySummaryDTO> getEquitySummary(Integer projectId, EquityType type) throws MyException {
        try {
            return r.findEquitySummaryByProjectIdAndType(projectId, type);
        } catch (JpaSystemException e) {
            throw new MyException(3001, e.getMessage());
        }
    }

    private List<FinancingInstitutionDTO> getFinancingInstitution(
            Integer projectId, int incomeDimension, int expenseDimension) throws MyException {
        try {
            return r.findFinancingInstitutionDetailsByProjectIdAndDimensions(projectId, incomeDimension, expenseDimension);
        } catch (JpaSystemException e) {
            throw new MyException(3001, e.getMessage());
        }
    }

    private FygFundDailyReport save(FygFundDailyReport entity) throws MyException {
        try {
            return r.save(entity);
        } catch (JpaSystemException e) {
            throw new MyException(3001, e.getMessage());
        }
    }

    private FygFundDailyReport update(FygFundDailyReport source, Integer id) throws MyException {
        try {
            FygFundDailyReport target = getById(id);
            BeanUtils.copyProperties(source, target, "id", "createdAt", "createdBy");
            return save(target);
        } catch (JpaSystemException e) {
            log.error("更新项目失败", e);
            throw new MyException(3001, "更新项目失败");
        }
    }

    private FygFundDailyReport getById(Integer id) throws MyException {
        try {
            return r.findById(id).orElseThrow(() -> new MyException(5001, "数据不存在"));
        } catch (JpaSystemException e) {
            throw new MyException(5001, "查询失败");
        }
    }

    private FygFundDailyReport toEntity(DailyReportDTO dto) {
        FygFundDailyReport entity = new FygFundDailyReport();
        return ToEntityUtil.DTOtoEntity(dto, entity);
    }

    private DailyReportDTO toDto(FygFundDailyReport entity) {
        DailyReportDTO dto = new DailyReportDTO();
        return ToEntityUtil.entityToDTO(entity, dto);
    }

    private List<FundSummaryDTO> getFundSummary(Integer projectId, Long transactionDate) throws MyException {
        try {
            return r.findFundSummaryWithDimensionDetails(projectId, DateUtil.date(transactionDate));
        } catch (JpaSystemException e) {
            throw new MyException(3001, e.getMessage());
        }
    }

    private List<FygFundDailyReport> getNotes(Integer projectId) throws MyException {
        try {
            return r.findByProjectIdAndDataStatus(projectId, 1);
        } catch (JpaSystemException e) {
            throw new MyException(3001, e.getMessage());
        }
    }

    private List<FundSummaryWithNoteDTO> assembly(Integer projectId, Long transactionDate) throws MyException {
        try {
            List<FundSummaryDTO> summaryDTOS = getFundSummary(projectId, transactionDate);
            List<FygFundDailyReport> noteEntities = getNotes(projectId);
            List<BankFundSummaryDTO> bankSummaryDTOS = getBankSummary(projectId, transactionDate);
            return buildSummaryWithNotes(summaryDTOS, noteEntities, bankSummaryDTOS);
        } catch (MyException e) {
            throw new MyException(e.getCode(), e.getMessage());
        }
    }

    private List<BankFundSummaryDTO> getBankSummary(Integer projectId, Long transactionDate) throws MyException {
        try {
            return r.findBankFundSummaryByProjectIdAndDate(projectId, DateUtil.date(transactionDate));
        } catch (JpaSystemException e) {
            throw new MyException(3001, e.getMessage());
        }
    }

    private List<FundSummaryWithNoteDTO> buildSummaryWithNotes(
            List<FundSummaryDTO> summaryDTOS,
            List<FygFundDailyReport> noteEntities,
            List<BankFundSummaryDTO> bankSummaryDTOS) throws MyException {

        List<FundSummaryWithNoteDTO> result = new ArrayList<>();

        try {
            // 计算总收入和支出
            BigDecimal totalIncome = summaryDTOS.stream()
                    .filter(dto -> dto.getType() == DimensionType.REVENUE)
                    .map(FundSummaryDTO::getCumulativeIncome)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal totalExpense = summaryDTOS.stream()
                    .filter(dto -> dto.getType() == DimensionType.EXPENSE)
                    .map(FundSummaryDTO::getCumulativeExpense)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 计算每日收入和支出
            BigDecimal dailyIncome = summaryDTOS.stream()
                    .filter(dto -> dto.getType() == DimensionType.REVENUE)
                    .map(FundSummaryDTO::getCurrentIncome)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal dailyExpense = summaryDTOS.stream()
                    .filter(dto -> dto.getType() == DimensionType.EXPENSE)
                    .map(FundSummaryDTO::getCurrentExpense)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 添加收入合计行
            result.add(new FundSummaryWithNoteDTO(
                    null, null, "一", "收入合计",
                    BigDecimalUtils.toWan(dailyIncome), BigDecimalUtils.toWan(totalIncome), null));

            // 添加收入明细
            int incomeIndex = 1;
            for (FundSummaryDTO summaryDTO : summaryDTOS.stream()
                    .filter(dto -> dto.getType() == DimensionType.REVENUE)
                    .collect(Collectors.toList())) {
                // 查找是否有对应的 noteEntity
                FygFundDailyReport noteEntity = noteEntities.stream()
                        .filter(note -> note.getFundDailyDimensionId().equals(summaryDTO.getFundDailyDimensionId()))
                        .findFirst()
                        .orElse(null);

                result.add(new FundSummaryWithNoteDTO(
                        noteEntity != null ? noteEntity.getId() : null, // 如果有 noteEntity，则取其 ID
                        noteEntity != null ? noteEntity.getNotes() : null, // 如果有 noteEntity，则取其备注
                        String.valueOf(incomeIndex++),
                        summaryDTO.getCashFlowAccountName(),
                        BigDecimalUtils.toWan(summaryDTO.getCurrentIncome()), // 转换每日金额
                        BigDecimalUtils.toWan(summaryDTO.getCumulativeIncome()), // 转换累计金额
                        summaryDTO.getFundDailyDimensionId()));
            }

            // 添加支出合计行
            result.add(new FundSummaryWithNoteDTO(
                    null, null, "二", "支出合计",
                    BigDecimalUtils.toWan(dailyExpense), BigDecimalUtils.toWan(totalExpense), null));

            // 添加支出明细
            int expenseIndex = 1;
            for (FundSummaryDTO summaryDTO : summaryDTOS.stream()
                    .filter(dto -> dto.getType() == DimensionType.EXPENSE)
                    .collect(Collectors.toList())) {
                // 查找是否有对应的 noteEntity
                FygFundDailyReport noteEntity = noteEntities.stream()
                        .filter(note -> note.getFundDailyDimensionId().equals(summaryDTO.getFundDailyDimensionId()))
                        .findFirst()
                        .orElse(null);

                result.add(new FundSummaryWithNoteDTO(
                        noteEntity != null ? noteEntity.getId() : null, // 如果有 noteEntity，则取其 ID
                        noteEntity != null ? noteEntity.getNotes() : null, // 如果有 noteEntity，则取其备注
                        String.valueOf(expenseIndex++),
                        summaryDTO.getCashFlowAccountName(),
                        BigDecimalUtils.toWan(summaryDTO.getCurrentExpense()), // 转换每日金额
                        BigDecimalUtils.toWan(summaryDTO.getCumulativeExpense()), // 转换累计金额
                        summaryDTO.getFundDailyDimensionId()));
            }

            // 计算资金余额（总收入 - 总支出）
            BigDecimal balance = totalIncome.subtract(totalExpense);
            BigDecimal dailyBalance = dailyIncome.subtract(dailyExpense);

            // 添加资金余额部分
            result.add(new FundSummaryWithNoteDTO(
                    null, null, "三", "资金余额",
                    BigDecimalUtils.toWan(dailyBalance), BigDecimalUtils.toWan(balance), null));

            // 添加银行资金余额部分
            int balanceIndex = 1;
            for (BankFundSummaryDTO bankSummaryDTO : bankSummaryDTOS) {
                result.add(new FundSummaryWithNoteDTO(
                        null,
                        getBankAccountNotes(bankSummaryDTO.getAccountType(), bankSummaryDTO.getNotes()),
                        String.valueOf(balanceIndex),
                        bankSummaryDTO.getAbbreviation(),
                        BigDecimalUtils.toWan(BigDecimalUtils.safe(bankSummaryDTO.getCurrentIncome())
                                .subtract(BigDecimalUtils.safe(bankSummaryDTO.getCurrentExpense()))), // 转换每日余额
                        BigDecimalUtils.toWan(BigDecimalUtils.safe(bankSummaryDTO.getCumulativeIncome())
                                .subtract(BigDecimalUtils.safe(bankSummaryDTO.getCumulativeExpense()))), // 转换累计余额
                        null));
                balanceIndex++;
            }

        } catch (Exception e) {
            log.error("Error building summary with notes: {}", e.getMessage());
            throw new MyException(5001, "构建Summary时出错: " + e.getMessage());
        }

        return result;
    }

    /**
     * 根据银行账户类型和备注信息生成账户备注字符串。
     *
     * @param bankAccountType 银行账户类型
     * @param notes           备注信息
     * @return 生成的账户备注字符串
     */
    private String getBankAccountNotes(BankAccountType bankAccountType, String notes) {
        String resolvedNotes = ObjectUtil.defaultIfBlank(notes, ""); // 将null转为空字符串
        if (bankAccountType != null) {
            String accountTypeValue = bankAccountType.getValue();
            if (resolvedNotes.isEmpty()) {
                return accountTypeValue; // notes为空时直接返回账户类型
            } else {
                // 使用String.format()来格式化字符串，其中%s表示字符串占位符
                return String.format("%s %s", bankAccountType.getValue(), resolvedNotes);
            }
        } else {
            return resolvedNotes;
        }
    }


}
