package cn.fyg.schedule.specification.fg.approval.document;

import cn.fyg.schedule.pojo.fg.approval.document.FgDocumentApproval;
import cn.fyg.schedule.utils.SpecificationUtil;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.Expression;

public class FgDocumentApprovalSpecification {
    public static Specification<FgDocumentApproval> ifFieldContains(String key, String column, String expression) {
        return (root, query, criteriaBuilder) -> {
            // 构建 JSON_EXTRACT 函数调用
            Expression<String> jsonExtractExp = criteriaBuilder.function(
                    "JSON_EXTRACT", String.class, root.get(column), criteriaBuilder.literal("$." + key)
            );
            return criteriaBuilder.like(jsonExtractExp, "%" + expression + "%");
        };
    }

    public static Specification<FgDocumentApproval> ifContains(String column, String expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.like(root.get(column), SpecificationUtil.contains(expression)));
    }
}
