package cn.fyg.schedule.specification.yx;

import cn.fyg.schedule.pojo.yx.countersign.YxCountersignInfo;
import cn.fyg.schedule.utils.SpecificationUtil;
import org.springframework.data.jpa.domain.Specification;

public class YxCountersignSpecification {
    public static Specification<YxCountersignInfo> ifContain(String column, String expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.like(root.get(column), SpecificationUtil.contains(expression)));
    }

    public static Specification<YxCountersignInfo> ifEq(String column, Integer expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }
}
