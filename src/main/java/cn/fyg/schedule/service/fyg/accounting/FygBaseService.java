package cn.fyg.schedule.service.fyg.accounting;


import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.reponse.BaseResponse;
/**
 * 基础服务接口，用于处理一些通用操作
 *
 * @param <T> 表示服务返回的对象类型
 * @param <V> 表示服务接收的输入类型
 * @param <P> 表示过滤操作返回的对象类型
 * @param <Q> 表示过滤操作的输入类型
 */
public interface FygBaseService<T, V, P, Q> {
    BaseResponse<T> findById(Integer id);
    BaseResponse<T> save(V value);
    BaseResponse<P> filter(Q query,  Pagination pagination);
    BaseResponse<T> setDataStatus(Integer id, Integer dataStatus, String updatedBy);
    BaseResponse<T> save(V value, Integer id);
}
