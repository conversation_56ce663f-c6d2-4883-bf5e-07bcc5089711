package cn.fyg.schedule.service.fyjs.social.insurance;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.fyjs.social.insurance.FyjsSocialInsuranceDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;
public interface FyjsSocialInsuranceService {
    BaseResponse save(FyjsSocialInsuranceDto dto);
    BaseResponse delete(FyjsSocialInsuranceDto dto);
    BaseResponse findById(Integer id);
    BaseResponse filter(Pagination pagination, FyjsSocialInsuranceDto dto);

    BaseResponse findFirstTop();
}
