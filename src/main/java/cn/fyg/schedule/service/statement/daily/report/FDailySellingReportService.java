package cn.fyg.schedule.service.statement.daily.report;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.statement.daily.report.FDailySellingReport;

public interface FDailySellingReportService {
    BaseResponse listProjects();
    BaseResponse listDaiySellingReportGroupByProject();
    BaseResponse delete(Integer id);
    BaseResponse findById(Integer id);
    BaseResponse save(FDailySellingReport data);
    BaseResponse findTopByCreatorAndFName(String creator, String name);
    BaseResponse annualSellingReport(String creator, String name);

    BaseResponse listNewestData();
}
