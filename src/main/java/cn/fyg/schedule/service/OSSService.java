package cn.fyg.schedule.service;

import cn.fyg.schedule.base.BaseResponse;
import org.springframework.web.multipart.MultipartFile;

public interface OSSService {
    BaseResponse upload(MultipartFile file, String bucket, Integer id, String type, String schema);
    BaseResponse upload(MultipartFile file, String bucket);
    BaseResponse deleteObject(String key, String bucket);
    BaseResponse getObjectUrl(String bucket, String objectName, Integer period);
}
