package cn.fyg.schedule.pojo.fyjs.bidding;

import cn.fyg.schedule.pojo.dto.fyjs.bidding.FyjsBiddingInfoDto;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Setter
@Getter
@ToString
@Table(name = "fyjs_bidding_info")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class FyjsBiddingInfo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    private String projectName;
    private BigDecimal projectCost;
    private Integer projectTimeLimit;
    @Temporal(TemporalType.TIMESTAMP)
    private Date bidEndDate;
    private String creator;
    private String lastEditor;
    private Integer dataStatus;
    private Integer bidStatus;
    private Integer isOpened;
    private Integer confirmed;
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "parent_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    @ToString.Exclude
    List<FyjsBiddingItem> items = new ArrayList<>();

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "parent_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    @ToString.Exclude
    List<FyjsBiddingTenderer> tenderers = new ArrayList<>();

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "parent_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    @ToString.Exclude
    List<FyjsBiddingOpen> openers = new ArrayList<>();

    public static FyjsBiddingInfo initialized(FyjsBiddingInfoDto dto) {
        FyjsBiddingInfo data = new FyjsBiddingInfo();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        data.setCreator(dto.getCreator());
        data.setLastEditor(dto.getLastEditor());
        if (dto.getCreateDate() != null) {
            data.setCreateDate(DateUtil.date(dto.getCreateDate()));
        }
        data.setDataStatus(dto.getDataStatus());
        data.setProjectName(dto.getProjectName());
        data.setProjectCost(dto.getProjectCost());
        data.setProjectTimeLimit(dto.getProjectTimeLimit());
        if (dto.getBidEndDate() != null) {
            data.setBidEndDate(DateUtil.date(dto.getBidEndDate()));
        }
        data.setBidStatus(dto.getBidStatus());
        data.setIsOpened(dto.getIsOpened());
        data.setConfirmed(dto.getConfirmed());
        return data;
    }
}
