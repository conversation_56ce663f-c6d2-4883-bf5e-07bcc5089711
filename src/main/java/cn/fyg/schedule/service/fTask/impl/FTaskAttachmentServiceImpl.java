package cn.fyg.schedule.service.fTask.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.ftask.FTaskAttachmentRepository;
import cn.fyg.schedule.pojo.ftask.FTaskAttachment;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.fTask.FTaskAttachmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;

@Slf4j
@Service
public class FTaskAttachmentServiceImpl implements FTaskAttachmentService {
    private final FTaskAttachmentRepository r;
    public FTaskAttachmentServiceImpl(FTaskAttachmentRepository r) {
        this.r = r;
    }


    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FTaskAttachment data) {
        return BaseService.save(data, r, FTaskAttachment.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }
}
