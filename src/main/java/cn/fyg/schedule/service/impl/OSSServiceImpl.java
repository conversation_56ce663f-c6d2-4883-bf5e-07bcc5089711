package cn.fyg.schedule.service.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.config.AliyunOssProperties;
import cn.fyg.schedule.pojo.FAssignmentItem;
import cn.fyg.schedule.pojo.FScheduleItem;
import cn.fyg.schedule.service.FAssignmentItemService;
import cn.fyg.schedule.service.FScheduleItemService;
import cn.fyg.schedule.service.OSSService;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.*;
import com.aliyun.oss.common.comm.Protocol;
import com.aliyun.oss.model.BucketInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.net.URL;
import java.util.Date;

@Service
@Slf4j
public class OSSServiceImpl implements OSSService {

    private final AliyunOssProperties aliyunOssProperties;

    private final FScheduleItemService fScheduleItemService;

    private final FAssignmentItemService fAssignmentItemService;

    public OSSServiceImpl(AliyunOssProperties aliyunOssProperties, FScheduleItemService fScheduleItemService,
                          FAssignmentItemService fAssignmentItemService) {
        this.fAssignmentItemService = fAssignmentItemService;
        this.aliyunOssProperties = aliyunOssProperties;
        this.fScheduleItemService = fScheduleItemService;
    }

//    @Autowired
//    public void setFAssignmentItemService(FAssignmentItemService fAssignmentItemService) {
//        this.fAssignmentItemService = fAssignmentItemService;
//    }
    private static ClientBuilderConfiguration buildClientBuilderConfiguration() {
        ClientBuilderConfiguration clientBuilderConfiguration = new ClientBuilderConfiguration();
        clientBuilderConfiguration.setProtocol(Protocol.HTTPS);
        return clientBuilderConfiguration;
    }
    @Override
    public BaseResponse upload(MultipartFile file, String bucket, Integer id, String type, String schema) {


        OSS ossClient = new OSSClientBuilder().build(aliyunOssProperties.getEndpoint(), aliyunOssProperties.getAccessKeyId(), aliyunOssProperties.getAccessKeySecret());
        BaseResponse response = BaseResponse.initialize();
        try {
            if (ossClient.doesBucketExist(bucket)) {
                log.info("您已经创建Bucket：" + bucket + "。");
                BucketInfo info = ossClient.getBucketInfo(bucket);
                log.info("Bucket " + bucket + "的信息如下：");
                log.info("\t数据中心：" + info.getBucket().getLocation());
                log.info("\t创建时间：" + info.getBucket().getCreationDate());
                log.info("\t用户标志：" + info.getBucket().getOwner());
                String fileName = file.getOriginalFilename();
                byte[] fileBuff = file.getBytes();
                InputStream is = new ByteArrayInputStream(fileBuff);
                String fileKey = getFileKey(fileName);
                ossClient.putObject(bucket, fileKey, is);


                if (schema.equals("schedule")) {
                    FScheduleItem item = new FScheduleItem();
                    item.setContent(fileKey);
                    item.setScheduleId(id);
                    item.setType(type);
                    response = fScheduleItemService.save(item);
                } else if (schema.equals("assignment")) {
                    FAssignmentItem item = new FAssignmentItem();
                    item.setContent(fileKey);
                    item.setAssignmentId(id);
                    item.setType(type);
                    response = fAssignmentItemService.save(item);
                }

            } else {
                response.setCode(1);
                response.setMsg("Bucket不存在:" + bucket + ".");
                log.info("Bucket不存在:" + bucket + ".");
            }
        } catch (OSSException oe) {
            oe.printStackTrace();
            response.setCode(1);
            response.setMsg(oe.getMessage());
        } catch (ClientException ce) {
            ce.printStackTrace();
            response.setCode(1);
            response.setMsg(ce.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            response.setCode(1);
            response.setMsg(e.getMessage());
        } finally {
            ossClient.shutdown();
        }
        return response;
    }

    @Override
    public BaseResponse upload(MultipartFile file, String bucket) {
        OSS ossClient = new OSSClientBuilder().build(aliyunOssProperties.getEndpoint(), aliyunOssProperties.getAccessKeyId(), aliyunOssProperties.getAccessKeySecret());
        BaseResponse response = BaseResponse.initialize();
        try {
            if (ossClient.doesBucketExist(bucket)) {
                log.info("您已经创建Bucket：" + bucket + "。");
                BucketInfo info = ossClient.getBucketInfo(bucket);
                log.info("Bucket " + bucket + "的信息如下：");
                log.info("\t数据中心：" + info.getBucket().getLocation());
                log.info("\t创建时间：" + info.getBucket().getCreationDate());
                log.info("\t用户标志：" + info.getBucket().getOwner());
                String fileName = file.getOriginalFilename();
                byte[] fileBuff = file.getBytes();
                InputStream is = new ByteArrayInputStream(fileBuff);
                String fileKey = getFileKey(fileName);
                ossClient.putObject(bucket, fileKey, is);
                log.info("key:" + fileKey);
                response.setResult(fileKey);

            } else {
                response.setCode(1);
                response.setMsg("Bucket不存在:" + bucket + ".");
                log.info("Bucket不存在:" + bucket + ".");
            }
        } catch (OSSException oe) {
            oe.printStackTrace();
            response.setCode(1);
            response.setMsg(oe.getMessage());
        } catch (ClientException ce) {
            ce.printStackTrace();
            response.setCode(1);
            response.setMsg(ce.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            response.setCode(1);
            response.setMsg(e.getMessage());
        } finally {
            ossClient.shutdown();
        }
        return response;
    }

    @Override
    public BaseResponse deleteObject(String key, String bucket) {
        return delete(key, bucket);
    }

    @Override
    public BaseResponse getObjectUrl(String bucket, String objectName, Integer period) {
        BaseResponse response = BaseResponse.initialize();
        OSS ossClient = new OSSClientBuilder().build(aliyunOssProperties.getExternalEndpoint(), aliyunOssProperties.getAccessKeyId(), aliyunOssProperties.getAccessKeySecret(), buildClientBuilderConfiguration());
        try {
        // 设置签名URL过期时间，单位为毫秒。
            Date expiration = new Date(new Date().getTime() + period * 1000);
            // 生成以GET方法访问的签名URL，访客可以直接通过浏览器访问相关内容。
            URL url = ossClient.generatePresignedUrl(bucket, objectName, expiration);
            response.setResult(url);
            log.info(url.toString());
        } catch (OSSException oe) {
            response.setCode(1);
            response.setMsg(oe.getErrorMessage());
            response.setResult(oe.getRequestId());
            log.info("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            log.info("Error Message:" + oe.getErrorMessage());
            log.info("Error Code:" + oe.getErrorCode());
            log.info("Request ID:" + oe.getRequestId());
            log.info("Host ID:" + oe.getHostId());
        } catch (ClientException ce) {
            response.setCode(1);
            response.setMsg(ce.getErrorMessage());
            log.info("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            log.info("Error Message:" + ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return response;
    }

    private BaseResponse delete(String key, String bucket) {
        BaseResponse response = BaseResponse.initialize();
        OSS ossClient = new OSSClientBuilder().build(aliyunOssProperties.getEndpoint(), aliyunOssProperties.getAccessKeyId(), aliyunOssProperties.getAccessKeySecret());
        try {
            if (ossClient.doesBucketExist(bucket)) {
                log.info("您已经创建Bucket：" + bucket + "。");
                BucketInfo info = ossClient.getBucketInfo(bucket);
                log.info("Bucket " + bucket + "的信息如下：");
                log.info("\t数据中心：" + info.getBucket().getLocation());
                log.info("\t创建时间：" + info.getBucket().getCreationDate());
                log.info("\t用户标志：" + info.getBucket().getOwner());

                ossClient.deleteObject(bucket, key);
                response.setMsg("文件删除成功");
                if (ossClient.doesObjectExist(bucket, key)) {
                    response.setCode(1);
                    response.setMsg("文件删除失败");
                }
            } else {
                log.info("Bucket不存在:" + bucket + ".");
                response.setCode(1);
                response.setMsg("Bucket不存在:" + bucket + ".");
            }
        } catch (OSSException oe) {
            oe.printStackTrace();
            response.setCode(1);
            response.setMsg(oe.getMessage());
        } catch (ClientException ce) {
            ce.printStackTrace();
            response.setCode(1);
            response.setMsg(ce.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            response.setCode(1);
            response.setMsg(e.getMessage());
        } finally {
            ossClient.shutdown();
        }
        return response;
    }

    private String getFileKey(String fileName) {
        String prefix = StrUtil.subBefore(fileName, '.', true);
        String suffix = StrUtil.removePrefix(fileName, prefix);
        String fileKey = IdUtil.randomUUID() + suffix;
        return fileKey;
    }

    private String getFileKey(String fileName, String separator) {
        String prefix_remove = StrUtil.subBefore(fileName, '.', true);
        String prefix_keep = StrUtil.subBefore(fileName, '.', false);
        String suffix = StrUtil.removePrefix(fileName, prefix_remove);
        String fileKey = prefix_keep + separator + IdUtil.randomUUID() + suffix;
        return fileKey;
    }
}
