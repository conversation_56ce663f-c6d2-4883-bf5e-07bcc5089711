package cn.fyg.schedule.service.core;

import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.font.FontProvider;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;

@Service
public class HtmlToPdfService {
    private static final String FONT = "./pdf/font/NotoSansCJKsc-Regular.otf";

    public byte[] convertHtmlToPdf(String html) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        PdfWriter writer = new PdfWriter(outputStream);
        PdfDocument pdf = new PdfDocument(writer);

        FontProvider fontProvider = new FontProvider();

        fontProvider.addFont(FONT);

        ConverterProperties properties = new ConverterProperties();
        properties.setFontProvider(fontProvider);

        HtmlConverter.convertToPdf(html, pdf, properties);
        pdf.close();

        return outputStream.toByteArray();
    }

}

