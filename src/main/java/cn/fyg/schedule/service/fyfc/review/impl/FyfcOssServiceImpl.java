package cn.fyg.schedule.service.fyfc.review.impl;

import cn.fyg.schedule.dao.fyfc.review.FyfcEvaluationRepository;
import cn.fyg.schedule.pojo.dto.fyfc.review.FyfcApiResponseDto;
import cn.fyg.schedule.pojo.dto.fyfc.review.FyfcAttachmentDto;
import cn.fyg.schedule.pojo.fyfc.review.FyfcEvaluation;
import cn.fyg.schedule.service.fyfc.review.IFyfcOssService;
import cn.fyg.schedule.service.common.ICommonOssService;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;

/**
 * FYFC OSS 服务实现
 * 专注于 FYFC 评价系统的业务逻辑，底层文件操作委托给通用 OSS 服务
 */
@Slf4j
@Service
public class FyfcOssServiceImpl implements IFyfcOssService {

    private final ICommonOssService commonOssService;
    private final FyfcEvaluationRepository evaluationRepository;
    
    public FyfcOssServiceImpl(ICommonOssService commonOssService, 
                             FyfcEvaluationRepository evaluationRepository) {
        this.commonOssService = commonOssService;
        this.evaluationRepository = evaluationRepository;
    }
    
    /**
     * FYFC 评价系统的文件夹路径
     */
    private static final String FYFC_FOLDER = "fyfc/evaluation";

    @Override
    @Transactional
    public FyfcApiResponseDto<FyfcAttachmentDto> uploadFile(MultipartFile file, Integer evaluationId, String uploadBy) {
        return uploadFileInternal(file, evaluationId, uploadBy, null);
    }

    @Override
    @Transactional
    public FyfcApiResponseDto<FyfcAttachmentDto> uploadFile(MultipartFile file, Integer evaluationId, String uploadBy, String bucketName) {
        return uploadFileInternal(file, evaluationId, uploadBy, bucketName);
    }

    /**
     * 内部上传文件实现
     */
    private FyfcApiResponseDto<FyfcAttachmentDto> uploadFileInternal(MultipartFile file, Integer evaluationId, String uploadBy, String bucketName) {
        log.info("FYFC上传文件: fileName={}, evaluationId={}, uploadBy={}, bucket={}", 
            file.getOriginalFilename(), evaluationId, uploadBy, bucketName);
        
        try {
            // 业务参数验证
            if (ObjectUtil.isNull(evaluationId)) {
                return FyfcApiResponseDto.error(400, "评价ID不能为空");
            }
            
            // 验证评价是否存在
            Optional<FyfcEvaluation> optionalEvaluation = evaluationRepository.findById(evaluationId);
            if (!optionalEvaluation.isPresent()) {
                return FyfcApiResponseDto.error(404, "评价记录不存在");
            }
            
            // 构建文件夹路径（包含评价ID）
            String folder = FYFC_FOLDER + "/" + evaluationId;
            
            // 委托给通用OSS服务进行文件上传
            FyfcApiResponseDto<FyfcAttachmentDto> uploadResult = commonOssService.uploadFile(file, folder, uploadBy, bucketName);
            
            if (uploadResult.getSuccess()) {
                // 将附件信息保存到评价记录中
                saveAttachmentToEvaluation(evaluationId, uploadResult.getData(), uploadBy);
                log.info("FYFC文件上传成功: fileKey={}", uploadResult.getData().getFileKey());
            }
            
            return uploadResult;
            
        } catch (Exception e) {
            log.error("FYFC文件上传失败", e);
            return FyfcApiResponseDto.error("文件上传失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public FyfcApiResponseDto<List<FyfcAttachmentDto>> uploadFiles(MultipartFile[] files, Integer evaluationId, String uploadBy) {
        return uploadFilesInternal(files, evaluationId, uploadBy, null);
    }

    @Override
    @Transactional
    public FyfcApiResponseDto<List<FyfcAttachmentDto>> uploadFiles(MultipartFile[] files, Integer evaluationId, String uploadBy, String bucketName) {
        return uploadFilesInternal(files, evaluationId, uploadBy, bucketName);
    }

    /**
     * 内部批量上传文件实现
     */
    private FyfcApiResponseDto<List<FyfcAttachmentDto>> uploadFilesInternal(MultipartFile[] files, Integer evaluationId, String uploadBy, String bucketName) {
        log.info("FYFC批量上传文件: fileCount={}, evaluationId={}, uploadBy={}, bucket={}", 
            files.length, evaluationId, uploadBy, bucketName);
        
        try {
            // 业务参数验证
            if (ObjectUtil.isNull(evaluationId)) {
                return FyfcApiResponseDto.error(400, "评价ID不能为空");
            }
            
            // 验证评价是否存在
            Optional<FyfcEvaluation> optionalEvaluation = evaluationRepository.findById(evaluationId);
            if (!optionalEvaluation.isPresent()) {
                return FyfcApiResponseDto.error(404, "评价记录不存在");
            }
            
            // 构建文件夹路径（包含评价ID）
            String folder = FYFC_FOLDER + "/" + evaluationId;
            
            // 委托给通用OSS服务进行批量上传
            FyfcApiResponseDto<List<FyfcAttachmentDto>> uploadResult = commonOssService.uploadFiles(files, folder, uploadBy, bucketName);
            
            if (uploadResult.getSuccess() && uploadResult.getData() != null) {
                // 批量保存附件信息到评价记录中
                saveBatchAttachmentsToEvaluation(evaluationId, uploadResult.getData(), uploadBy);
                log.info("FYFC批量文件上传成功: fileCount={}", uploadResult.getData().size());
            }
            
            return uploadResult;
            
        } catch (Exception e) {
            log.error("FYFC批量上传文件失败", e);
            return FyfcApiResponseDto.error("批量上传失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public FyfcApiResponseDto<Boolean> deleteFile(String fileKey, Integer evaluationId, String operatorName) {
        return deleteFileInternal(fileKey, evaluationId, operatorName, null);
    }

    @Override
    @Transactional
    public FyfcApiResponseDto<Boolean> deleteFile(String fileKey, Integer evaluationId, String operatorName, String bucketName) {
        return deleteFileInternal(fileKey, evaluationId, operatorName, bucketName);
    }

    /**
     * 内部删除文件实现
     */
    private FyfcApiResponseDto<Boolean> deleteFileInternal(String fileKey, Integer evaluationId, String operatorName, String bucketName) {
        log.info("FYFC删除文件: fileKey={}, evaluationId={}, operator={}, bucket={}", 
            fileKey, evaluationId, operatorName, bucketName);
        
        try {
            if (StrUtil.isBlank(fileKey)) {
                return FyfcApiResponseDto.error(400, "文件键不能为空");
            }
            if (ObjectUtil.isNull(evaluationId)) {
                return FyfcApiResponseDto.error(400, "评价ID不能为空");
            }
            
            // 验证评价是否存在
            Optional<FyfcEvaluation> optionalEvaluation = evaluationRepository.findById(evaluationId);
            if (!optionalEvaluation.isPresent()) {
                return FyfcApiResponseDto.error(404, "评价记录不存在");
            }
            
            // 从评价记录中移除附件信息
            removeAttachmentFromEvaluation(evaluationId, fileKey, operatorName);
            
            // 委托给通用OSS服务删除文件
            FyfcApiResponseDto<Boolean> deleteResult = commonOssService.deleteFile(fileKey, bucketName);
            
            if (deleteResult.getSuccess()) {
                log.info("FYFC文件删除成功: fileKey={}", fileKey);
            }
            
            return deleteResult;
            
        } catch (Exception e) {
            log.error("FYFC删除文件失败", e);
            return FyfcApiResponseDto.error("删除文件失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<String> getFileUrl(String fileKey, Integer expireSeconds) {
        return commonOssService.getFileUrl(fileKey, expireSeconds, null);
    }

    @Override
    public FyfcApiResponseDto<String> getFileUrl(String fileKey, Integer expireSeconds, String bucketName) {
        // 直接委托给通用OSS服务
        return commonOssService.getFileUrl(fileKey, expireSeconds, bucketName);
    }

    @Override
    public FyfcApiResponseDto<List<FyfcAttachmentDto>> getEvaluationAttachments(Integer evaluationId) {
        log.info("FYFC获取评价附件: evaluationId={}", evaluationId);
        
        try {
            if (ObjectUtil.isNull(evaluationId)) {
                return FyfcApiResponseDto.error(400, "评价ID不能为空");
            }
            
            Optional<FyfcEvaluation> optionalEvaluation = evaluationRepository.findById(evaluationId);
            if (!optionalEvaluation.isPresent()) {
                return FyfcApiResponseDto.error(404, "评价记录不存在");
            }
            
            FyfcEvaluation evaluation = optionalEvaluation.get();
            List<FyfcAttachmentDto> attachments = getAttachmentsFromJson(evaluation.getAttachments());
            
            // 为每个附件生成下载URL
            for (FyfcAttachmentDto attachment : attachments) {
                FyfcApiResponseDto<String> urlResult = commonOssService.getFileUrl(
                    attachment.getFileKey(), 3600, attachment.getBucketName());
                if (urlResult.getSuccess()) {
                    attachment.setFileUrl(urlResult.getData());
                }
            }
            
            return FyfcApiResponseDto.success(attachments, "获取附件列表成功");
            
        } catch (Exception e) {
            log.error("FYFC获取评价附件失败", e);
            return FyfcApiResponseDto.error("获取附件列表失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public FyfcApiResponseDto<Boolean> updateEvaluationAttachments(Integer evaluationId, List<FyfcAttachmentDto> attachments, String operatorName) {
        log.info("FYFC更新评价附件: evaluationId={}, attachmentCount={}, operator={}", 
            evaluationId, attachments.size(), operatorName);
        
        try {
            if (ObjectUtil.isNull(evaluationId)) {
                return FyfcApiResponseDto.error(400, "评价ID不能为空");
            }
            
            Optional<FyfcEvaluation> optionalEvaluation = evaluationRepository.findById(evaluationId);
            if (!optionalEvaluation.isPresent()) {
                return FyfcApiResponseDto.error(404, "评价记录不存在");
            }
            
            FyfcEvaluation evaluation = optionalEvaluation.get();
            evaluation.setAttachments(JSONUtil.toJsonStr(attachments));
            evaluation.setUpdatedBy(operatorName);
            evaluationRepository.save(evaluation);
            
            return FyfcApiResponseDto.success(true, "附件列表更新成功");
            
        } catch (Exception e) {
            log.error("FYFC更新评价附件失败", e);
            return FyfcApiResponseDto.error("更新附件列表失败: " + e.getMessage());
        }
    }

    // ==================== 私有业务方法 ====================

    /**
     * 将附件信息保存到评价记录中
     */
    private void saveAttachmentToEvaluation(Integer evaluationId, FyfcAttachmentDto attachment, String operatorName) {
        try {
            Optional<FyfcEvaluation> optionalEvaluation = evaluationRepository.findById(evaluationId);
            if (!optionalEvaluation.isPresent()) {
                log.error("保存附件失败：评价记录不存在, evaluationId={}", evaluationId);
                return;
            }
            
            FyfcEvaluation evaluation = optionalEvaluation.get();
            
            // 获取现有附件列表
            List<FyfcAttachmentDto> attachments = getAttachmentsFromJson(evaluation.getAttachments());
            
            // 添加新附件
            attachments.add(attachment);
            
            // 更新评价记录
            evaluation.setAttachments(JSONUtil.toJsonStr(attachments));
            evaluation.setUpdatedBy(operatorName);
            evaluationRepository.save(evaluation);
            
            log.info("附件信息已保存到评价记录: evaluationId={}, fileKey={}", evaluationId, attachment.getFileKey());
            
        } catch (Exception e) {
            log.error("保存附件信息到评价记录失败: evaluationId={}, fileKey={}", evaluationId, attachment.getFileKey(), e);
            // 这里不抛异常，避免影响文件上传的成功返回
        }
    }

    /**
     * 批量保存附件信息到评价记录中
     */
    private void saveBatchAttachmentsToEvaluation(Integer evaluationId, List<FyfcAttachmentDto> newAttachments, String operatorName) {
        try {
            Optional<FyfcEvaluation> optionalEvaluation = evaluationRepository.findById(evaluationId);
            if (!optionalEvaluation.isPresent()) {
                log.error("批量保存附件失败：评价记录不存在, evaluationId={}", evaluationId);
                return;
            }
            
            FyfcEvaluation evaluation = optionalEvaluation.get();
            
            // 获取现有附件列表
            List<FyfcAttachmentDto> existingAttachments = getAttachmentsFromJson(evaluation.getAttachments());
            
            // 添加新附件
            existingAttachments.addAll(newAttachments);
            
            // 更新评价记录
            evaluation.setAttachments(JSONUtil.toJsonStr(existingAttachments));
            evaluation.setUpdatedBy(operatorName);
            evaluationRepository.save(evaluation);
            
            log.info("批量附件信息已保存到评价记录: evaluationId={}, newAttachmentCount={}", 
                evaluationId, newAttachments.size());
            
        } catch (Exception e) {
            log.error("批量保存附件信息到评价记录失败: evaluationId={}, attachmentCount={}", 
                evaluationId, newAttachments.size(), e);
            // 这里不抛异常，避免影响文件上传的成功返回
        }
    }

    /**
     * 从评价记录中移除附件信息
     */
    private void removeAttachmentFromEvaluation(Integer evaluationId, String fileKey, String operatorName) {
        try {
            Optional<FyfcEvaluation> optionalEvaluation = evaluationRepository.findById(evaluationId);
            if (!optionalEvaluation.isPresent()) {
                log.error("移除附件失败：评价记录不存在, evaluationId={}", evaluationId);
                return;
            }
            
            FyfcEvaluation evaluation = optionalEvaluation.get();
            
            // 获取现有附件列表并移除指定附件
            List<FyfcAttachmentDto> attachments = getAttachmentsFromJson(evaluation.getAttachments());
            attachments.removeIf(att -> fileKey.equals(att.getFileKey()));
            
            // 更新评价记录
            evaluation.setAttachments(JSONUtil.toJsonStr(attachments));
            evaluation.setUpdatedBy(operatorName);
            evaluationRepository.save(evaluation);
            
            log.info("附件信息已从评价记录中移除: evaluationId={}, fileKey={}", evaluationId, fileKey);
            
        } catch (Exception e) {
            log.error("从评价记录中移除附件信息失败: evaluationId={}, fileKey={}", evaluationId, fileKey, e);
            // 这里不抛异常，避免影响文件删除的成功返回
        }
    }

    /**
     * 从JSON字符串解析附件列表
     */
    private List<FyfcAttachmentDto> getAttachmentsFromJson(String attachmentsJson) {
        if (StrUtil.isBlank(attachmentsJson)) {
            return new ArrayList<>();
        }
        
        try {
            return JSONUtil.toList(attachmentsJson, FyfcAttachmentDto.class);
        } catch (Exception e) {
            log.warn("解析附件JSON失败: {}", attachmentsJson, e);
            return new ArrayList<>();
        }
    }
}
