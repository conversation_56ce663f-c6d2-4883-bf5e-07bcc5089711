package cn.fyg.schedule.utils;

import cn.hutool.core.date.CalendarUtil;
import cn.hutool.core.date.DateException;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
@Slf4j
public class DatePeriodUtil {
    private static final String[] possibleDateFormats = {
            "yyyy年",
            "yyyy-MM",
            "yyyy/MM",
            "yyyy.MM",
            "yyyy年MM月"
            // 添加更多可能的格式
    };
    public static Date getDate(String period, Integer beginOrEnd) {
        String today = DateUtil.today();
        Date date = DateUtil.date();
        switch (period) {
            case "day":
                date = DateUtil.parseDate(today);
                break;
            case "week":
                date = beginOrEnd == 0 ? CalendarUtil.beginOfWeek(CalendarUtil.calendar()).getTime()
                        : CalendarUtil.endOfWeek(CalendarUtil.calendar()).getTime();
                break;
            case "month":
                date = beginOrEnd == 0 ? CalendarUtil.beginOfMonth(CalendarUtil.calendar()).getTime()
                        : CalendarUtil.endOfMonth(CalendarUtil.calendar()).getTime();
                break;
            case "quarter":
                date = beginOrEnd == 0 ? CalendarUtil.beginOfQuarter(CalendarUtil.calendar()).getTime()
                        : CalendarUtil.endOfQuarter(CalendarUtil.calendar()).getTime();
                break;
            case "year":
                date = beginOrEnd == 0 ? CalendarUtil.beginOfYear(CalendarUtil.calendar()).getTime()
                        : CalendarUtil.endOfYear(CalendarUtil.calendar()).getTime();
                break;
            case "lastYear":
                int day = DateUtil.isLeapYear(DateUtil.thisYear()) ? -366 : -365;
                date = beginOrEnd == 0 ? CalendarUtil.beginOfYear(CalendarUtil.calendar(DateUtil.offsetDay(date, day))).getTime()
                        : CalendarUtil.endOfYear(CalendarUtil.calendar(DateUtil.offsetDay(date, day))).getTime();
                break;

        }
        return date;
    }

    public static Date getDate(String dateStr) {
        Date parsedDate = null;
        try {
            parsedDate = DateUtil.parse(dateStr);
        } catch (DateException e) {
            log.error(e.getMessage());
            parsedDate = getDateWithDefaultFormat(dateStr);
        }
        return parsedDate;
    }

    private static Date getDateWithDefaultFormat(String dateStr) {
        Date parsedDate = null;
        // 尝试解析日期字符串
        for (String dateFormat : possibleDateFormats) {
            try {
                parsedDate = DateUtil.parse(dateStr, dateFormat);
                log.info("成功解析日期：" + parsedDate);
                break;
            } catch (DateException e) {
                // 解析失败，尝试下一个格式
            }
        }
        return parsedDate;
    }
}
