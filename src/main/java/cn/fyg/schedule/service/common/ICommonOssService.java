package cn.fyg.schedule.service.common;

import cn.fyg.schedule.pojo.dto.fyfc.review.FyfcApiResponseDto;
import cn.fyg.schedule.pojo.dto.fyfc.review.FyfcAttachmentDto;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 通用 OSS 服务接口
 * 提供基础的文件上传、下载、删除功能，不依赖具体业务
 */
public interface ICommonOssService {
    
    /**
     * 上传单个文件
     * 
     * @param file 文件
     * @param folder 文件夹路径（如：fyfc/evaluation、hr/resume等）
     * @param uploadBy 上传人
     * @param bucketName bucket名称（可选，为空则使用默认bucket）
     * @return 附件信息
     */
    FyfcApiResponseDto<FyfcAttachmentDto> uploadFile(MultipartFile file, String folder, String uploadBy, String bucketName);
    
    /**
     * 上传单个文件（使用默认bucket）
     */
    FyfcApiResponseDto<FyfcAttachmentDto> uploadFile(MultipartFile file, String folder, String uploadBy);
    
    /**
     * 批量上传文件
     * 
     * @param files 文件列表
     * @param folder 文件夹路径
     * @param uploadBy 上传人
     * @param bucketName bucket名称（可选）
     * @return 附件信息列表
     */
    FyfcApiResponseDto<List<FyfcAttachmentDto>> uploadFiles(MultipartFile[] files, String folder, String uploadBy, String bucketName);
    
    /**
     * 批量上传文件（使用默认bucket）
     */
    FyfcApiResponseDto<List<FyfcAttachmentDto>> uploadFiles(MultipartFile[] files, String folder, String uploadBy);
    
    /**
     * 删除文件
     * 
     * @param fileKey 文件键
     * @param bucketName bucket名称（可选）
     * @return 删除结果
     */
    FyfcApiResponseDto<Boolean> deleteFile(String fileKey, String bucketName);
    
    /**
     * 删除文件（使用默认bucket）
     */
    FyfcApiResponseDto<Boolean> deleteFile(String fileKey);
    
    /**
     * 获取文件下载URL
     * 
     * @param fileKey 文件键
     * @param expireSeconds 过期时间（秒）
     * @param bucketName bucket名称（可选）
     * @return 下载URL
     */
    FyfcApiResponseDto<String> getFileUrl(String fileKey, Integer expireSeconds, String bucketName);
    
    /**
     * 获取文件下载URL（使用默认bucket）
     */
    FyfcApiResponseDto<String> getFileUrl(String fileKey, Integer expireSeconds);
    
    /**
     * 检查文件是否存在
     * 
     * @param fileKey 文件键
     * @param bucketName bucket名称（可选）
     * @return 是否存在
     */
    FyfcApiResponseDto<Boolean> fileExists(String fileKey, String bucketName);
    
    /**
     * 检查文件是否存在（使用默认bucket）
     */
    FyfcApiResponseDto<Boolean> fileExists(String fileKey);

    /**
     * 获取文件信息
     *
     * @param fileKey 文件键
     * @param bucketName bucket名称（可选）
     * @return 文件信息
     */
    FyfcApiResponseDto<FyfcAttachmentDto> getFileInfo(String fileKey, String bucketName);

    /**
     * 获取文件信息（使用默认bucket）
     */
    FyfcApiResponseDto<FyfcAttachmentDto> getFileInfo(String fileKey);

    /**
     * 生成文件键
     *
     * @param originalFileName 原始文件名
     * @param folder 文件夹路径
     * @param businessId 业务ID（可选，用于分组）
     * @return 生成的文件键
     */
    String generateFileKey(String originalFileName, String folder, String businessId);
    
    /**
     * 生成文件键（不使用业务ID）
     */
    String generateFileKey(String originalFileName, String folder);
}
