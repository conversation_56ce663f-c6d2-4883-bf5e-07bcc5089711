package cn.fyg.schedule.service.fg.approval.document.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.fg.approval.document.FgDocumentApprovalItemRepository;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.fg.approval.document.FgDocumentApprovalItem;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.fg.approval.document.FgDocumentApprovalItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
public class FgDocumentApprovalItemServiceImpl implements FgDocumentApprovalItemService {
    private final FgDocumentApprovalItemRepository r;

    public FgDocumentApprovalItemServiceImpl(FgDocumentApprovalItemRepository r) {
        this.r = r;
    }

    @Override
    public BaseResponse save(FgDocumentApprovalItem data) {
        BaseResponse response = BaseResponse.initialize();
        try {
            response.setResult(saveItem(data));
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }

    @Override
    public FgDocumentApprovalItem saveItem(FgDocumentApprovalItem data) throws MyException {
        return BaseService.saveWithR(data, r, FgDocumentApprovalItem.class);
    }

    @Override
    public BaseResponse findTopByParentId(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        try {
            FgDocumentApprovalItem result = getTopOne(id);
            if (result == null) {
                response.setCode(1);
                response.setMsg("未查到数据");
            } else {
                response.setResult(result);
            }
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }

    private FgDocumentApprovalItem getTopOne(Integer id) throws MyException {
        try {
            Optional<FgDocumentApprovalItem> optional = r.findFirstByParentIdOrderByCreateDateDesc(id);
            return optional.orElse(null);
        } catch (JpaSystemException e) {
            throw new MyException(500, "查询出错");
        }
    }
}
