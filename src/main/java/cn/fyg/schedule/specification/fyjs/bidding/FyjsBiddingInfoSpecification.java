package cn.fyg.schedule.specification.fyjs.bidding;

import cn.fyg.schedule.pojo.fyjs.bidding.FyjsBiddingInfo;
import cn.fyg.schedule.utils.SpecificationUtil;
import org.springframework.data.jpa.domain.Specification;

import java.math.BigDecimal;
import java.util.Date;

public class FyjsBiddingInfoSpecification {
    public static Specification<FyjsBiddingInfo> ifContain(String column, String expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.like(root.get(column), SpecificationUtil.contains(expression)));
    }

    public static Specification<FyjsBiddingInfo> ifDateEq(String column, Date expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }

    public static Specification<FyjsBiddingInfo> ifDateBetween(String column, Date startDate, Date endDate) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.between(root.get(column), startDate, endDate));
    }

    public static Specification<FyjsBiddingInfo> ifDateOverThan(String column, Date date) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.greaterThan(root.get(column), date));
    }

    public static Specification<FyjsBiddingInfo> ifDateLessThan(String column, Date date) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.lessThan(root.get(column), date));
    }

    public static Specification<FyjsBiddingInfo> ifDateOverThanOrEqualTo(String column, Date date) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.greaterThanOrEqualTo(root.get(column), date));
    }

    public static Specification<FyjsBiddingInfo> ifDateLessThanOrEqualTo(String column, Date date) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.lessThanOrEqualTo(root.get(column), date));
    }

    public static Specification<FyjsBiddingInfo> ifEq(String column, String expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }

    public static Specification<FyjsBiddingInfo> ifEq(String column, Integer expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }

    public static Specification<FyjsBiddingInfo> ifEq(String column, BigDecimal expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }

    public static Specification<FyjsBiddingInfo> ifNumberBetween(String column, BigDecimal min, BigDecimal max) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.between(root.get(column), min, max));
    }

    public static Specification<FyjsBiddingInfo> ifNumberOverThan(String column, BigDecimal num) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.greaterThan(root.get(column), num));
    }

    public static Specification<FyjsBiddingInfo> ifNumberOverThanOrEqualTo(String column, BigDecimal num) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.greaterThanOrEqualTo(root.get(column), num));
    }

    public static Specification<FyjsBiddingInfo> ifNumberLessThan(String column, BigDecimal num) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.lessThan(root.get(column), num));
    }
    public static Specification<FyjsBiddingInfo> ifNumberLessThanOrEqualTo(String column, BigDecimal num) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.lessThanOrEqualTo(root.get(column), num));
    }

    public static Specification<FyjsBiddingInfo> ifNumberBetween(String column, Integer min, Integer max) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.between(root.get(column), min, max));
    }

    public static Specification<FyjsBiddingInfo> ifNumberOverThan(String column, Integer num) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.greaterThan(root.get(column), num));
    }

    public static Specification<FyjsBiddingInfo> ifNumberLessThan(String column, Integer num) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.lessThan(root.get(column), num));
    }
    public static Specification<FyjsBiddingInfo> ifNumberOverThanOrEqualTo(String column, Integer num) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.greaterThanOrEqualTo(root.get(column), num));
    }

    public static Specification<FyjsBiddingInfo> ifNumberLessThanOrEqualTo(String column, Integer num) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.lessThanOrEqualTo(root.get(column), num));
    }
}
