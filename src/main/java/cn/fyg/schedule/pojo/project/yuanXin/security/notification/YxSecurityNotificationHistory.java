package cn.fyg.schedule.pojo.project.yuanXin.security.notification;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Setter
@Getter
@ToString
@Table(name = "yx_security_noti_history")
public class YxSecurityNotificationHistory {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    private Integer parentId;
    private String creator;
    private String creatorName;
    @Column(name = "yx_project_name")
    private String yxProjectName;
    @Column(name = "yx_time_limit")
    private Integer yxTimeLimit;
    @Column(name = "yx_problem_description")
    private String yxProblemDescription;
    @Column(name = "create_date")
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @Column(name = "history_create_date")
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date historyCreateDate;
    @Column(name = "inspection_date")
    @Temporal(TemporalType.DATE)
    private Date inspectionDate;
    private Integer type;
    private String yxEventName;
    private Integer needSign;
    private String lastEditor;

//    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
//    @JoinColumn(name = "parent_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
//    @JsonIgnore
//    @ToString.Exclude
//    List<YxSecurityNotiItem> items = new ArrayList<>();
//
//    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
//    @JoinColumn(name = "parent_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
//    @JsonIgnore
//    @ToString.Exclude
//    List<YxSecurityNotiSign> signatures = new ArrayList<>();

    public static YxSecurityNotificationHistory initialize(YxSecurityNotification dto) {
        YxSecurityNotificationHistory data = new YxSecurityNotificationHistory();

        data.setCreator(dto.getCreator());
        data.setCreatorName(dto.getCreatorName());
        data.setYxProjectName(dto.getYxProjectName());
        data.setYxTimeLimit(dto.getYxTimeLimit());
        data.setYxProblemDescription(dto.getYxProblemDescription());
        data.setHistoryCreateDate(dto.getCreateDate());
        if (dto.getInspectionDate() != null) {
            data.setInspectionDate(dto.getInspectionDate());
        }
        data.setType(dto.getType());
        data.setYxEventName(dto.getYxEventName());
        data.setNeedSign(dto.getNeedSign());
        data.setLastEditor(dto.getLastEditor());
        return data;
    }
}
