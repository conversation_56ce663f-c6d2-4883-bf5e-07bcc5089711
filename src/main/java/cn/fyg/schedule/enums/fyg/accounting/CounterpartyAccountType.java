package cn.fyg.schedule.enums.fyg.accounting;

import cn.fyg.schedule.enums.BaseEnum;
import lombok.Getter;

@Getter
public enum CounterpartyAccountType implements BaseEnum<String> {
    GU_DONG_FANG("股东方"),
    NEI_BU_DAN_WEI("内部单位"),
    WAI_BU_DAN_WEI("外部单位"),
    DAI_KUAN_YIN_HANG("贷款银行");

    private final String value;

    CounterpartyAccountType(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return value;
    }
}
