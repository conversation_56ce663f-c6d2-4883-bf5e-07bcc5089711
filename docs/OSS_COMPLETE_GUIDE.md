# FYFC OSS 完整指南

## 📋 **目录**

1. [OSS 服务架构](#oss-服务架构)
2. [OSS 配置指南](#oss-配置指南)
3. [API 文档](#api-文档)
4. [中文字符支持](#中文字符支持)
5. [问题修复记录](#问题修复记录)
6. [最佳实践](#最佳实践)

---

## 🏗️ **OSS 服务架构**

### **服务层次结构**

```
FyfcOssController (业务层)
    ↓
FyfcOssServiceImpl (FYFC专用服务)
    ↓
CommonOssServiceImpl (通用OSS服务)
    ↓
AliyunOSS (阿里云OSS SDK)
```

### **核心组件**

1. **通用 OSS 服务** (`CommonOssServiceImpl`)
   - 基础文件操作（上传、下载、删除）
   - 文件验证和限制
   - Bucket 管理
   - 元数据处理

2. **FYFC 专用服务** (`FyfcOssServiceImpl`)
   - 评价附件管理
   - 数据库同步
   - 业务逻辑处理

3. **配置管理**
   - `AliyunOssProperties`: OSS 连接配置
   - `FyfcOssUploadProperties`: 上传限制配置

---

## ⚙️ **OSS 配置指南**

### **1. 基础配置**

```yaml
# application.yml
aliyun:
  oss:
    endpoint: https://oss-cn-hangzhou.aliyuncs.com
    access-key-id: ${ALIYUN_ACCESS_KEY_ID}
    access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET}
    bucket-name: fyfc

fyfc:
  oss:
    upload:
      enable-size-check: true
      max-file-size: 52428800        # 50MB
      enable-type-check: true
      allowed-types: 
        - "application/pdf"
        - "application/msword"
        - "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        - "application/vnd.ms-excel"
        - "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        - "image/jpeg"
        - "image/png"
        - "text/plain"
      max-file-count: 10
      max-batch-total-size: 524288000  # 500MB
```

### **2. 环境变量配置**

```bash
# 生产环境
export ALIYUN_ACCESS_KEY_ID="your-access-key-id"
export ALIYUN_ACCESS_KEY_SECRET="your-access-key-secret"

# 开发环境
export ALIYUN_ACCESS_KEY_ID="dev-access-key-id"
export ALIYUN_ACCESS_KEY_SECRET="dev-access-key-secret"
```

### **3. Bucket 策略配置**

```json
{
  "Version": "1",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": "*",
      "Action": ["oss:GetObject"],
      "Resource": ["acs:oss:*:*:fyfc/*"]
    }
  ]
}
```

---

## 📚 **API 文档**

### **核心接口**

#### **1. 上传文件**

```http
POST /api/fyfc/oss/upload
Content-Type: multipart/form-data

Parameters:
- file: MultipartFile (必需)
- evaluationId: Integer (必需)
- uploadBy: String (必需)
- bucketName: String (可选)
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "id": "8eb3b178-23bd-4533-ab81-9d51db239978",
    "fileName": "document.docx",
    "fileKey": "fyfc/evaluation/6/2025/06/06/file.docx",
    "bucketName": "fyfc",
    "fileSize": 16316,
    "fileType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "uploadTime": 1749188994768,
    "uploadBy": "张三"
  }
}
```

#### **2. 批量上传**

```http
POST /api/fyfc/oss/upload/batch
Content-Type: multipart/form-data

Parameters:
- files: MultipartFile[] (必需)
- evaluationId: Integer (必需)
- uploadBy: String (必需)
- bucketName: String (可选)
```

#### **3. 获取附件列表**

```http
GET /api/fyfc/oss/attachments/{evaluationId}?bucketName={bucketName}
```

#### **4. 删除文件**

```http
DELETE /api/fyfc/oss/delete
Content-Type: application/json

{
  "fileKey": "fyfc/evaluation/6/2025/06/06/file.docx",
  "evaluationId": 6,
  "operatorName": "张三",
  "bucketName": "fyfc"
}
```

#### **5. 获取下载URL**

```http
GET /api/fyfc/oss/url?fileKey={fileKey}&expireSeconds={seconds}&bucketName={bucketName}
```

#### **6. 文件预览**

```http
GET /api/fyfc/oss/preview?fileKey={fileKey}&bucketName={bucketName}
```

### **TypeScript 类型定义**

```typescript
interface FyfcAttachment {
  id: string;
  fileName: string;
  fileKey: string;
  bucketName?: string;
  fileSize: number;
  fileType: string;
  uploadTime: number;
  uploadBy: string;
  fileUrl?: string;
}

interface FyfcEvaluation {
  id: number;
  name: string;
  department: string;
  attachments?: string;  // JSON字符串
  // ... 其他字段
}
```

---

## 🌏 **中文字符支持**

### **问题背景**

OSS 用户元数据只支持 ASCII 字符，中文字符会导致错误：
```
InvalidArgument: The user metadata contains invalid characters
```

### **解决方案：Base64 编码**

#### **智能编码策略**

```java
/**
 * 编码用户元数据，处理中文字符
 */
private String encodeUserMetadata(String value) {
    if (StrUtil.isBlank(value)) {
        return value;
    }
    
    // 检查是否包含非ASCII字符
    if (!value.matches("^[\\x00-\\x7F]*$")) {
        // 包含非ASCII字符，进行Base64编码
        return Base64.getEncoder().encodeToString(value.getBytes(StandardCharsets.UTF_8));
    }
    return value;  // 纯ASCII字符，无需编码
}
```

#### **安全解码策略**

```java
/**
 * 解码用户元数据
 */
private String decodeUserMetadata(String encodedValue) {
    if (StrUtil.isBlank(encodedValue)) {
        return encodedValue;
    }
    
    try {
        byte[] decoded = Base64.getDecoder().decode(encodedValue);
        return new String(decoded, StandardCharsets.UTF_8);
    } catch (IllegalArgumentException e) {
        // 解码失败，可能是原始ASCII字符
        return encodedValue;
    }
}
```

#### **使用效果**

- **中文用户名**：`张三` → 自动编码存储 → 自动解码显示
- **英文用户名**：`John` → 保持原样 → 直接显示
- **向后兼容**：现有数据继续正常工作

---

## 🔧 **问题修复记录**

### **1. Bucket 参数问题**

**问题**：接口中 bucket 参数不一致，部分接口缺少 bucket 支持

**解决**：
- 统一所有接口支持可选的 `bucketName` 参数
- 提供默认 bucket 回退机制
- 更新测试页面支持 bucket 选择

### **2. 数据库同步问题**

**问题**：OSS 操作成功但数据库未更新

**解决**：
- 使用 `@Transactional` 确保事务一致性
- 添加异常回滚机制
- 完善错误处理和日志记录

### **3. 上传限制配置**

**问题**：硬编码的上传限制，不够灵活

**解决**：
- 创建 `FyfcOssUploadProperties` 配置类
- 支持文件大小、类型、数量限制
- 提供友好的错误提示

### **4. 测试页面问题**

**问题**：测试页面功能不完整，bucket 参数缺失

**解决**：
- 完善测试页面 UI 和功能
- 添加 bucket 选择器
- 改进错误显示和用户体验

---

## 🎯 **最佳实践**

### **1. 文件上传**

```typescript
// 推荐的上传方式
async function uploadFile(file: File, evaluationId: number, uploadBy: string) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('evaluationId', evaluationId.toString());
  formData.append('uploadBy', uploadBy);
  
  try {
    const response = await fetch('/fyschedule2/api/fyfc/oss/upload', {
      method: 'POST',
      body: formData
    });
    
    const result = await response.json();
    if (result.success) {
      console.log('上传成功:', result.data);
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('上传失败:', error);
    throw error;
  }
}
```

### **2. 错误处理**

```typescript
// 统一的错误处理
function handleOssError(error: any) {
  if (error.code === 400) {
    // 参数错误
    showMessage('请检查上传参数', 'error');
  } else if (error.code === 413) {
    // 文件过大
    showMessage('文件大小超过限制', 'error');
  } else if (error.code === 415) {
    // 文件类型不支持
    showMessage('不支持的文件类型', 'error');
  } else {
    // 其他错误
    showMessage('操作失败，请重试', 'error');
  }
}
```

### **3. 文件预览**

```typescript
// 安全的文件预览
async function previewFile(attachment: FyfcAttachment) {
  if (isImageFile(attachment.fileType)) {
    // 图片直接预览
    const url = await getFileUrl(attachment.fileKey, 3600);
    showImagePreview(url);
  } else if (isPdfFile(attachment.fileType)) {
    // PDF 在新窗口打开
    const url = await getFileUrl(attachment.fileKey, 3600);
    window.open(url, '_blank');
  } else {
    // 其他文件类型下载
    downloadFile(attachment);
  }
}
```

### **4. 批量操作**

```typescript
// 批量上传优化
async function uploadFiles(files: File[], evaluationId: number, uploadBy: string) {
  const maxBatchSize = 10;
  const results = [];
  
  for (let i = 0; i < files.length; i += maxBatchSize) {
    const batch = files.slice(i, i + maxBatchSize);
    const batchResult = await uploadBatch(batch, evaluationId, uploadBy);
    results.push(...batchResult);
  }
  
  return results;
}
```

### **5. 性能优化**

```typescript
// 文件压缩（可选）
async function compressImage(file: File): Promise<File> {
  if (!file.type.startsWith('image/')) {
    return file;
  }
  
  // 使用 canvas 压缩图片
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  const img = new Image();
  
  return new Promise((resolve) => {
    img.onload = () => {
      const maxWidth = 1920;
      const maxHeight = 1080;
      
      let { width, height } = img;
      
      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height);
        width *= ratio;
        height *= ratio;
      }
      
      canvas.width = width;
      canvas.height = height;
      
      ctx.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob((blob) => {
        resolve(new File([blob], file.name, { type: file.type }));
      }, file.type, 0.8);
    };
    
    img.src = URL.createObjectURL(file);
  });
}
```

---

## 📊 **监控和维护**

### **1. 关键指标**

- **上传成功率**：> 99%
- **平均上传时间**：< 5秒
- **存储使用量**：定期监控
- **错误率**：< 1%

### **2. 日志监控**

```java
// 关键操作日志
log.info("OSS文件上传: fileKey={}, size={}, uploadBy={}", fileKey, fileSize, uploadBy);
log.warn("OSS操作失败: operation={}, error={}", operation, error.getMessage());
log.error("OSS严重错误: {}", error.getMessage(), error);
```

### **3. 定期维护**

- **清理过期文件**：定期清理测试文件
- **备份重要数据**：定期备份生产数据
- **更新配置**：根据使用情况调整限制
- **性能优化**：监控并优化慢查询

---

## 🎉 **总结**

FYFC OSS 服务提供了完整的文件管理解决方案：

1. ✅ **功能完整**：上传、下载、删除、预览
2. ✅ **中文支持**：Base64 编码解决中文字符问题
3. ✅ **配置灵活**：支持多种配置和限制
4. ✅ **错误处理**：完善的异常处理和用户提示
5. ✅ **性能优化**：批量操作和缓存机制
6. ✅ **易于维护**：清晰的架构和完整的文档

通过这套 OSS 服务，FYFC 评价系统可以安全、高效地管理各种文件附件！🚀
