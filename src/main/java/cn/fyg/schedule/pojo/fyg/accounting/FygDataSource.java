package cn.fyg.schedule.pojo.fyg.accounting;

import cn.fyg.schedule.enums.fyg.accounting.CounterpartyAccountType;
import cn.fyg.schedule.enums.fyg.accounting.CounterpartyAccountTypeConverter;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

@Entity
@Setter
@Getter
@ToString
@Table(name = "fyg_data_source")
public class FygDataSource {

    // 主键ID，自动生成
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    // 交易日期
    @Column(name = "transaction_date", nullable = false)
    @Temporal(TemporalType.DATE)
    private Date transactionDate;

    // 银行账户ID，关联银行账户表
    @Column(name = "bank_account_id")
    private Integer bankAccountId;

    // 银行账户
    @Column(name = "bank_account_name", length = 255)
    private String bankAccountName;

    // 银行账号
    @Column(name = "bank_account_number", length = 100)
    private String bankAccountNumber;

    // 银行账户简称
    @Column(name = "bank_abbreviation", length = 50)
    private String bankAbbreviation;

    // 收入金额，默认值为0.00
    @Column(name = "income_amount", precision = 18, scale = 2)
    private BigDecimal incomeAmount = BigDecimal.valueOf(0.00);

    // 支出金额，默认值为0.00
    @Column(name = "expense_amount", precision = 18, scale = 2)
    private BigDecimal expenseAmount = BigDecimal.valueOf(0.00);

    // 对方户名ID，关联对方账户表
    @Deprecated
    @Column(name = "counterparty_account_id")
    private Integer counterpartyAccountId;

    // 对方户名
    @Column(name = "counterparty_name", length = 255)
    private String counterpartyName;

    //对方类型
    @Convert(converter = CounterpartyAccountTypeConverter.class)
    @Column(name = "counterparty_type")
    private CounterpartyAccountType counterpartyType;

    // 资金日报维度ID，关联资金日报维度表
    @Column(name = "fund_daily_dimension_id")
    private Integer fundDailyDimensionId;

    // 税金明细
    @Column(name = "tax_detail", length = 255)
    private String taxDetail;

    // 开发成本明细
    @Column(name = "development_cost_detail", length = 255)
    private String developmentCostDetail;

    // 项目ID，关联项目表
    @Column(name = "project_id", nullable = false)
    private Integer projectId;

    // 详细用途
    @Column(name = "purpose_detail", length = 255)
    private String purposeDetail;

    // 对方账号
    @Column(name = "counterparty_account_number", length = 100)
    private String counterpartyAccountNumber;

    // 对方开户行
    @Column(name = "counterparty_bank_name", length = 100)
    private String counterpartyBankName;

    // 年利率，百分比
    @Column(name = "annual_interest_rate", precision = 5, scale = 2)
    private BigDecimal annualInterestRate;

    // 数据状态，1表示正常，0表示删除
    @Column(name = "data_status", columnDefinition = "tinyint(4) default 1", nullable = false)
    private Integer dataStatus = 1;

    // 创建时间，自动生成
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;

    // 更新时间，自动更新
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at", nullable = false)
    private Date updatedAt;

    // 创建者
    @Column(name = "created_by", length = 50)
    private String createdBy;

    // 最后更新者
    @Column(name = "updated_by", length = 50)
    private String updatedBy;

    // 收支二级明细
    @Column(name = "secondary_detail", length = 255)
    private String secondaryDetail;

    // 收支三级明细
    @Column(name = "tertiary_detail", length = 255)
    private String tertiaryDetail;

    // 备注
    @Column(name = "remark", length = 255)
    private String remark;

    // 外键关联
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "bank_account_id", referencedColumnName = "id", insertable = false, updatable = false)
    private FygBankAccounts bankAccount;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "fund_daily_dimension_id", referencedColumnName = "id", insertable = false, updatable = false)
    private FygFundDailyDimension fundDailyDimension;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", referencedColumnName = "id", insertable = false, updatable = false)
    private FygProjects project;
}
