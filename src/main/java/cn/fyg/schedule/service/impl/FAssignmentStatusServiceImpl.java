package cn.fyg.schedule.service.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.assignment.FAssignmentStatusRepository;
import cn.fyg.schedule.pojo.FAssignmentStatus;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.FAssignmentStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;

@Service
@Slf4j
public class FAssignmentStatusServiceImpl implements FAssignmentStatusService {
    @Autowired private FAssignmentStatusRepository r;
    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FAssignmentStatus status) {
        return BaseService.save(status, r, FAssignmentStatus.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findByAssignmentId(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FAssignmentStatus> result = r.findByAssignmentIdOrderByCreateDateDesc(id);
            baseResponse.setResult(result);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }
}
