package cn.fyg.schedule.controller.mini.app;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.FSchedule;
import cn.fyg.schedule.pojo.dto.FScheduleDto;
import cn.fyg.schedule.pojo.dto.query.FScheduleQuery;
import cn.fyg.schedule.service.FScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("schedule")
@Slf4j
public class FScheduleController {

    private final FScheduleService fScheduleService;

    public FScheduleController(FScheduleService fScheduleService) {
        this.fScheduleService = fScheduleService;
    }

    @PostMapping("findcolumns")
    public BaseResponse findColumns(String table) {
       return fScheduleService.findAllColumns(table);
    }

    @PostMapping("list")
    public BaseResponse getList(FScheduleQuery query) {
        return fScheduleService.findByCreator(query.getCreator(), query.getPage(), query.getSize());
    }

    @PostMapping("listbydate")
    public BaseResponse getListByDate(FScheduleQuery query) {
        return fScheduleService.findByCreatorAndStartDate(query.getCreator(), query.getStartDate(), query.getPage(), query.getSize());
    }

    @PostMapping("listbyduration")
    public BaseResponse getListByDuration(FScheduleQuery query) {
        return fScheduleService.findByCreatorAndDuration(query.getCreator(), query.getStartDate(), query.getDuration(), query.getPage(), query.getSize());
    }

    @PostMapping("listeventbyduration")
    public BaseResponse getListEventByDuration(FScheduleQuery query) {
        return fScheduleService.findByCreatorAndDurationAndCurrentStatus(query.getCreator(), query.getStartDate(), query.getDuration(), query.getCurrentStatus(), query.getPage(), query.getSize());
    }

    @PostMapping("search")
    public BaseResponse search(FScheduleQuery query) {
        return fScheduleService.search(query.getCreator(), query.getStartDate(), query.getDuration(), query.getSearchText(), query.getPage(), query.getSize());
    }

    @PostMapping("detail")
    public BaseResponse getDetail(FScheduleQuery query) {
        return fScheduleService.findById(query.getId());
    }

    @PostMapping("filter")
    public BaseResponse listByFilter(FScheduleDto dto) {
        return fScheduleService.listByFilter(dto);
    }

    @PostMapping("save")
    public BaseResponse save(FScheduleDto dto) {
        FSchedule fSchedule = FSchedule.initialize(dto);
        return fScheduleService.save(fSchedule);
    }

    @PostMapping("delete")
    public BaseResponse delete(Integer id) {
        return fScheduleService.delete(id);
    }

    @PostMapping("marked")
    public BaseResponse findMarkedDate(FScheduleQuery query) {
        return fScheduleService.findMarkedDate(query.getCreator());
    }

    @PostMapping("markedcount")
    public BaseResponse getMarkedSchedule(FScheduleQuery query) {
        return fScheduleService.getMarkedSchedule(query.getCreator());
    }
}
