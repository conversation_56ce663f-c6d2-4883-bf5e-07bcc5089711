package cn.fyg.schedule.pojo.fyg.accounting;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Setter
@Getter
@ToString
@Table(name = "fyg_daily_report_summary")
public class FygDailyReportSummary {

    // 主键ID，自动生成
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    // 项目ID，关联项目表
    @Column(name = "project_id", nullable = false)
    private Integer projectId;

    // 序号
    @Column(name = "sequence_number")
    private Integer sequenceNumber;

    // 项目名称（冗余字段）
    @Column(name = "project_name", length = 255)
    private String projectName;

    // 纯股比，百分比
    @Column(name = "pure_equity_ratio", precision = 5, scale = 2)
    private BigDecimal pureEquityRatio;

    // 房产投入金额
    @Column(name = "real_estate_input", precision = 18, scale = 2)
    private BigDecimal realEstateInput;

    // 房产回款金额
    @Column(name = "real_estate_recovered", precision = 18, scale = 2)
    private BigDecimal realEstateRecovered;

    // 实际占用房产资金
    @Column(name = "actual_used_real_estate_funds", precision = 18, scale = 2)
    private BigDecimal actualUsedRealEstateFunds;

    // 合作股东占用资金
    @Column(name = "partner_funds_used", precision = 18, scale = 2)
    private BigDecimal partnerFundsUsed;

    // 资金余额
    @Column(name = "fund_balance", precision = 18, scale = 2)
    private BigDecimal fundBalance;

    // 监管户余额
    @Column(name = "supervised_account_balance", precision = 18, scale = 2)
    private BigDecimal supervisedAccountBalance;

    // 贷款余额
    @Column(name = "loan_balance", precision = 18, scale = 2)
    private BigDecimal loanBalance;

    // 到期日
    @Temporal(TemporalType.DATE)
    @Column(name = "due_date")
    private Date dueDate;

    // 销售货值（不含回购）
    @Column(name = "sale_value_without_buyback", precision = 18, scale = 2)
    private BigDecimal saleValueWithoutBuyback;

    // 销售金额（不含回购）
    @Column(name = "sale_amount_without_buyback", precision = 18, scale = 2)
    private BigDecimal saleAmountWithoutBuyback;

    // 已收销售款（不含回购）
    @Column(name = "received_sales_funds_without_buyback", precision = 18, scale = 2)
    private BigDecimal receivedSalesFundsWithoutBuyback;

    // 已售可收（不含回购）
    @Column(name = "recoverable_funds_without_buyback", precision = 18, scale = 2)
    private BigDecimal recoverableFundsWithoutBuyback;

    // 回购货值
    @Column(name = "buyback_value", precision = 18, scale = 2)
    private BigDecimal buybackValue;

    // 节点已到回购（类销售金额）
    @Column(name = "reached_buyback_node", precision = 18, scale = 2)
    private BigDecimal reachedBuybackNode;

    // 已收回购款
    @Column(name = "received_buyback_funds", precision = 18, scale = 2)
    private BigDecimal receivedBuybackFunds;

    // 可收回购款
    @Column(name = "recoverable_buyback_funds", precision = 18, scale = 2)
    private BigDecimal recoverableBuybackFunds;

    // 经营支出
    @Column(name = "operating_expenditure", precision = 18, scale = 2)
    private BigDecimal operatingExpenditure;

    // 还贷计划（时间或条件）
    @Column(name = "repayment_plan", length = 255)
    private String repaymentPlan;

    // 数据状态，1表示正常，0表示删除
    @Column(name = "data_status", columnDefinition = "tinyint(4) default 1")
    private Integer dataStatus;

    // 备注
    @Column(name = "remarks", length = 255)
    private String remarks;

    // 创建者
    @Column(name = "created_by", length = 50)
    private String createdBy;

    // 最后更新者
    @Column(name = "updated_by", length = 50)
    private String updatedBy;

    // 创建时间，自动生成
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;

    // 更新时间，自动更新
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at", nullable = false)
    private Date updatedAt;

    // 外键关联注解，关联fyg_projects表
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", referencedColumnName = "id", insertable = false, updatable = false)
    private FygProjects project;
}

