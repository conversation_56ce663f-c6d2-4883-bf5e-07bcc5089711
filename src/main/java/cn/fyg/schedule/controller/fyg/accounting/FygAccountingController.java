package cn.fyg.schedule.controller.fyg.accounting;

import cn.fyg.schedule.pojo.dto.fyg.accounting.report.DailyReportDTO;
import cn.fyg.schedule.pojo.dto.fyg.accounting.report.EquityStructureDTO;
import cn.fyg.schedule.pojo.dto.fyg.accounting.report.FundSummaryWithNoteDTO;
import cn.fyg.schedule.pojo.dto.fyg.accounting.report.summary.DailyReportSummaryDTO;
import cn.fyg.schedule.pojo.dto.fyg.accounting.report.summary.DailyReportSummaryQuery;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.reponse.BaseResponse;
import cn.fyg.schedule.service.fyg.accounting.FygDailyReportSummaryService;
import cn.fyg.schedule.service.fyg.accounting.FygFundDailyReportService;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/fyg/accounting/report")
public class FygAccountingController {
    private final FygFundDailyReportService dailyReportService;
    private final FygDailyReportSummaryService dailyReportSummaryService;

    public FygAccountingController(FygFundDailyReportService dailyReportService, FygDailyReportSummaryService dailyReportSummaryService) {
        this.dailyReportService = dailyReportService;
        this.dailyReportSummaryService = dailyReportSummaryService;
    }

    @PutMapping("save")
    public BaseResponse<DailyReportDTO> save(DailyReportDTO dto) {
        return dailyReportService.save(dto);
    }

    @PostMapping("/daily/show")
    public BaseResponse<List<FundSummaryWithNoteDTO>> dailyReport(Integer projectId, Long transactionDate) {
       return dailyReportService.getFundSummaryWithNote(projectId, transactionDate);
    }

    @PostMapping("/daily/show-equity")
    public BaseResponse<List<EquityStructureDTO>> dailyReportEquity(Integer projectId) {
        return dailyReportService.getEquityStructure(projectId);
    }

    @PutMapping("/daily/summary/sync")
    public BaseResponse<DailyReportSummaryDTO> sync(DailyReportSummaryDTO dto) {
        return dailyReportSummaryService.updateDailyReportSummary(dto);
    }

    @PostMapping("/daily/summary/filter")
    public BaseResponse<Page<DailyReportSummaryDTO>> filter(DailyReportSummaryQuery query, Pagination pagination) {
        return dailyReportSummaryService.filter(query, pagination);
    }

    @PutMapping("/daily/summary/save")
    public BaseResponse<DailyReportSummaryDTO> save(DailyReportSummaryDTO dto) {
        return dailyReportSummaryService.save(dto);
    }

    @PutMapping("update-status")
    public BaseResponse<DailyReportSummaryDTO> updateStatus(Integer id, Integer dataStatus, String updatedBy) {
        return dailyReportSummaryService.setDataStatus(id, dataStatus, updatedBy);
    }

}
