package cn.fyg.schedule.pojo.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class FSealControlDto extends BaseDto {
    private String number;
    private String departmentName;
    private String sealType;
    private String contacts;
    private Integer counts;
    private String auditDept;
    private String sealContent;
    private String transactionDate;
    private String transactionTime;
}
