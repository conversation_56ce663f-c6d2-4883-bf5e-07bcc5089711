package cn.fyg.schedule.pojo.core;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

@Entity
@Setter
@Getter
@ToString
@Table(name = "f_history")
public class FHistory {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    private String historyId;
    private String tableName;
    private String jsonData;
    private String action;
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
}
