package cn.fyg.schedule.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * FYFC OSS 配置类
 */
@Slf4j
@Configuration
public class FyfcOssConfig {

    @Autowired
    private AliyunOssProperties ossProperties;

    @PostConstruct
    public void init() {
        log.info("FYFC OSS 配置初始化完成");
        log.info("OSS Endpoint: {}", ossProperties.getEndpoint());
        log.info("OSS Bucket: {}", ossProperties.getBucketName());
        log.info("OSS AccessKeyId: {}***", 
            ossProperties.getAccessKeyId() != null ? 
            ossProperties.getAccessKeyId().substring(0, Math.min(8, ossProperties.getAccessKeyId().length())) : "null");
    }
}
