package cn.fyg.schedule.pojo.project.fyjs.sealManagement;

import cn.fyg.schedule.pojo.dto.project.fyjs.sealManagement.FJsSealManagementDto;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Setter
@Getter
@ToString
@Table(name = "f_js_seal_management")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class FJsSealManagement {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    private Integer sequence;
    private String name;
    private String custodianDepartment;
    private String responsibleCustodian;
    @Temporal(TemporalType.DATE)
    private Date receivingDate;
    private Integer liabilityStatement;
    private Integer paymentStatus;
    @Temporal(TemporalType.DATE)
    private Date recycleDate;
    private String remarks;
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "sealId", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
//    @JsonIgnore
    @ToString.Exclude
    List<FJsSealAttachment> items = new ArrayList<>();

    public static FJsSealManagement initialize(FJsSealManagementDto dto) {
        FJsSealManagement data = new FJsSealManagement();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        data.setSequence(dto.getSequence());
        data.setName(dto.getName());
        data.setCustodianDepartment(dto.getCustodianDepartment());
        data.setResponsibleCustodian(dto.getResponsibleCustodian());
        if (dto.getReceivingDate() != null) {
            data.setReceivingDate(DateUtil.date(dto.getReceivingDate()));
        }
        if (dto.getRecycleDate() != null) {
            data.setRecycleDate(DateUtil.date(dto.getRecycleDate()));
        }
        if (dto.getCreateDate() != null) {
            data.setCreateDate(DateUtil.date(dto.getCreateDate()));
        }
        data.setLiabilityStatement(dto.getLiabilityStatement());
        data.setPaymentStatus(dto.getPaymentStatus());
        data.setRemarks(dto.getRemarks());
        return data;
    }
}
