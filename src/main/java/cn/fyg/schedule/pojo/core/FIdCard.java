package cn.fyg.schedule.pojo.core;

import cn.hutool.core.util.IdcardUtil;
import lombok.Data;

@Data
public class FIdCard {
    private Integer gender;
    private Integer age;
    private Long birth;
    private String province;

    public static FIdCard initialized(String idCard) {
        FIdCard data = new FIdCard();
        data.setAge(IdcardUtil.getAgeByIdCard(idCard));
        data.setGender(IdcardUtil.getGenderByIdCard(idCard));
        data.setProvince(IdcardUtil.getProvinceByIdCard(idCard));
        data.setBirth(IdcardUtil.getBirthDate(idCard).getTime());
        return data;
    }
}
