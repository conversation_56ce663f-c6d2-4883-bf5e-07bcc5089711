package cn.fyg.schedule.service.sheets.yx.progressCheck;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.project.yuanXin.progressCheck.YxProgressCheckRecordItem;

public interface YxProgressCheckRecordItemService {
    BaseResponse save(YxProgressCheckRecordItem data);
    BaseResponse delete(Integer id);
    BaseResponse findById(Integer id);
    BaseResponse findByParentId(Integer id);
}
