package cn.fyg.schedule.service.fyfc.review;

import cn.fyg.schedule.pojo.dto.fyfc.review.FyfcApiResponseDto;
import cn.fyg.schedule.pojo.dto.fyfc.review.FyfcAttachmentDto;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * FYFC OSS 服务接口
 */
public interface IFyfcOssService {
    
    /**
     * 上传单个文件
     *
     * @param file 文件
     * @param evaluationId 评价ID
     * @param uploadBy 上传人
     * @return 附件信息
     */
    FyfcApiResponseDto<FyfcAttachmentDto> uploadFile(MultipartFile file, Integer evaluationId, String uploadBy);

    /**
     * 上传单个文件到指定bucket
     *
     * @param file 文件
     * @param evaluationId 评价ID
     * @param uploadBy 上传人
     * @param bucketName bucket名称（可选，为空则使用默认bucket）
     * @return 附件信息
     */
    FyfcApiResponseDto<FyfcAttachmentDto> uploadFile(MultipartFile file, Integer evaluationId, String uploadBy, String bucketName);

    /**
     * 批量上传文件
     *
     * @param files 文件列表
     * @param evaluationId 评价ID
     * @param uploadBy 上传人
     * @return 附件信息列表
     */
    FyfcApiResponseDto<List<FyfcAttachmentDto>> uploadFiles(MultipartFile[] files, Integer evaluationId, String uploadBy);

    /**
     * 批量上传文件到指定bucket
     *
     * @param files 文件列表
     * @param evaluationId 评价ID
     * @param uploadBy 上传人
     * @param bucketName bucket名称（可选，为空则使用默认bucket）
     * @return 附件信息列表
     */
    FyfcApiResponseDto<List<FyfcAttachmentDto>> uploadFiles(MultipartFile[] files, Integer evaluationId, String uploadBy, String bucketName);
    
    /**
     * 删除文件
     *
     * @param fileKey 文件键
     * @param evaluationId 评价ID
     * @param operatorName 操作人
     * @return 删除结果
     */
    FyfcApiResponseDto<Boolean> deleteFile(String fileKey, Integer evaluationId, String operatorName);

    /**
     * 删除指定bucket中的文件
     *
     * @param fileKey 文件键
     * @param evaluationId 评价ID
     * @param operatorName 操作人
     * @param bucketName bucket名称（可选，为空则使用默认bucket）
     * @return 删除结果
     */
    FyfcApiResponseDto<Boolean> deleteFile(String fileKey, Integer evaluationId, String operatorName, String bucketName);

    /**
     * 获取文件下载URL
     *
     * @param fileKey 文件键
     * @param expireSeconds 过期时间（秒）
     * @return 下载URL
     */
    FyfcApiResponseDto<String> getFileUrl(String fileKey, Integer expireSeconds);

    /**
     * 获取指定bucket中文件的下载URL
     *
     * @param fileKey 文件键
     * @param expireSeconds 过期时间（秒）
     * @param bucketName bucket名称（可选，为空则使用默认bucket）
     * @return 下载URL
     */
    FyfcApiResponseDto<String> getFileUrl(String fileKey, Integer expireSeconds, String bucketName);
    
    /**
     * 获取评价的所有附件
     * 
     * @param evaluationId 评价ID
     * @return 附件列表
     */
    FyfcApiResponseDto<List<FyfcAttachmentDto>> getEvaluationAttachments(Integer evaluationId);
    
    /**
     * 更新评价的附件列表
     * 
     * @param evaluationId 评价ID
     * @param attachments 附件列表
     * @param operatorName 操作人
     * @return 更新结果
     */
    FyfcApiResponseDto<Boolean> updateEvaluationAttachments(Integer evaluationId, List<FyfcAttachmentDto> attachments, String operatorName);
}
