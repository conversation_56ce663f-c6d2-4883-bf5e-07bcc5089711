package cn.fyg.schedule.pojo.fyg.accounting;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

@Entity
@Setter
@Getter
@ToString
@Table(name = "fyg_fund_daily_report")
public class FygFundDailyReport {

    // 主键ID，自动生成
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    // 资金日报维度ID，关联资金日报维度表
    @Column(name = "fund_daily_dimension_id", nullable = false)
    private Integer fundDailyDimensionId;

    // 项目ID，关联项目表
    @Column(name = "project_id")
    private Integer projectId;

    // 备注
    @Column(name = "notes", length = 255)
    private String notes;

    // 数据状态，1表示正常，0表示删除
    @Column(name = "data_status", columnDefinition = "tinyint(4) default 1")
    private Integer dataStatus;

    // 创建时间，自动生成
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;

    // 更新时间，自动更新
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at", nullable = false)
    private Date updatedAt;

    // 创建者
    @Column(name = "created_by", length = 50)
    private String createdBy;

    // 最后更新者
    @Column(name = "updated_by", length = 50)
    private String updatedBy;

    // 外键关联注解
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", referencedColumnName = "id", insertable = false, updatable = false)
    private FygProjects project;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "fund_daily_dimension_id", referencedColumnName = "id", insertable = false, updatable = false)
    private FygFundDailyDimension fundDailyDimension;
}

