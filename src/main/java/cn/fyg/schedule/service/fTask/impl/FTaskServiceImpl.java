package cn.fyg.schedule.service.fTask.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.ftask.FTaskManagementRepository;
import cn.fyg.schedule.pojo.ftask.FTaskManagement;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.fTask.FTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class FTaskServiceImpl implements FTaskService {
    private final FTaskManagementRepository r;
    public FTaskServiceImpl(FTaskManagementRepository r) {
        this.r = r;
    }


    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FTaskManagement data) {
        BaseResponse response = BaseService.save(data, r, FTaskManagement.class);
        // 如果保存成功，预加载懒加载的集合，避免序列化时的LazyInitializationException
        if (response.getCode() == 0 && response.getResult() instanceof FTaskManagement) {
            FTaskManagement savedTask = (FTaskManagement) response.getResult();
            preloadLazyCollections(savedTask);
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse listTaskByType(String typeKey) {
        BaseResponse response = BaseResponse.initialize();
        try {
            List<FTaskManagement> list = r.findByTypeKeyOrderByCreateDateDesc(typeKey);
            response.setResult(list);
        } catch (Exception e) {
            response.setCode(1);
            response.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse findById(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        try {
            Optional<FTaskManagement> data = r.findById(id);
            if (data.isPresent()) {
                FTaskManagement task = data.get();
                // 预加载懒加载的集合，避免LazyInitializationException
                preloadLazyCollections(task);
                response.setResult(task);
            }
        } catch (Exception e) {
            response.setCode(1);
            response.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return response;
    }

    /**
     * 预加载懒加载的集合，避免LazyInitializationException
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    private void preloadLazyCollections(FTaskManagement task) {
        if (task != null) {
            // 触发懒加载
            if (task.getItems() != null) {
                task.getItems().size();
            }
            if (task.getComments() != null) {
                task.getComments().size();
            }
        }
    }
}
