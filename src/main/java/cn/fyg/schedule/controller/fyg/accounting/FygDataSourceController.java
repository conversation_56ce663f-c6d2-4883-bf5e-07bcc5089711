package cn.fyg.schedule.controller.fyg.accounting;

import cn.fyg.schedule.pojo.dto.fyg.accounting.DataSourceDTO;
import cn.fyg.schedule.pojo.dto.fyg.accounting.DataSourceQuery;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.reponse.BaseResponse;
import cn.fyg.schedule.service.fyg.accounting.FygDataSourceService;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/fyg/accounting/dataSource")
public class FygDataSourceController {
    private final FygDataSourceService service;
    public FygDataSourceController(FygDataSourceService service) {
        this.service = service;
    }

    @PutMapping("save")
    public BaseResponse<DataSourceDTO> save(DataSourceDTO dto) {
        if (dto.getId() == null) {
            return service.save(dto);
        } else {
            return service.save(dto, dto.getId());
        }
    }

    @PostMapping("filter")
    public BaseResponse<Page<DataSourceDTO>> filter(DataSourceQuery query, Pagination pagination) {
        return service.filter(query, pagination);
    }

    @PutMapping("update-status")
    public BaseResponse<DataSourceDTO> updateStatus(Integer id, Integer dataStatus, String updatedBy) {
        return service.setDataStatus(id, dataStatus, updatedBy);
    }
}
