package cn.fyg.schedule.service.fyfc.review;

import cn.fyg.schedule.pojo.dto.fyfc.review.*;

import java.util.List;

/**
 * FYFC 评价评分服务接口
 * 专门处理评分相关的业务逻辑
 */
public interface IFyfcEvaluationScoreService {

    /**
     * 提交评分
     * @param scoreForm 评分表单
     * @param evaluatorName 评价人
     * @return 统一API响应格式
     */
    FyfcApiResponseDto<Boolean> submitScore(FyfcScoreFormDto scoreForm, String evaluatorName);

    /**
     * 更新评分
     * @param scoreId 评分ID
     * @param scoreForm 评分表单
     * @param evaluatorName 评价人
     * @return 统一API响应格式
     */
    FyfcApiResponseDto<Boolean> updateScore(Integer scoreId, FyfcScoreFormDto scoreForm, String evaluatorName);

    /**
     * 删除评分
     * @param scoreId 评分ID
     * @param operatorName 操作人
     * @return 统一API响应格式
     */
    FyfcApiResponseDto<Boolean> deleteScore(Integer scoreId, String operatorName);

    /**
     * 获取用户对指定评价的评分记录
     * @param evaluationId 评价ID
     * @param evaluatorName 评价人
     * @return 统一API响应格式的评分记录
     */
    FyfcApiResponseDto<FyfcEvaluationScoreDto> getUserScore(Integer evaluationId, String evaluatorName);

    /**
     * 检查用户是否已经评分
     * @param evaluationId 评价ID
     * @param evaluatorName 评价人
     * @return 统一API响应格式的检查结果
     */
    FyfcApiResponseDto<Boolean> hasUserScored(Integer evaluationId, String evaluatorName);

    /**
     * 获取评价的所有评分记录
     * @param evaluationId 评价ID
     * @return 统一API响应格式的评分列表
     */
    FyfcApiResponseDto<List<FyfcEvaluationScoreDto>> getEvaluationScores(Integer evaluationId);

    /**
     * 计算评价的平均分
     * @param evaluationId 评价ID
     * @return 统一API响应格式的平均分
     */
    FyfcApiResponseDto<Object> calculateAverageScore(Integer evaluationId);

    /**
     * 获取评分统计信息
     * @param evaluationId 评价ID
     * @return 统一API响应格式的统计信息
     */
    FyfcApiResponseDto<Object> getScoreStatistics(Integer evaluationId);

    /**
     * 验证评分数据
     * @param scoreForm 评分表单
     * @return 统一API响应格式的验证结果
     */
    FyfcApiResponseDto<Object> validateScoreData(FyfcScoreFormDto scoreForm);

    /**
     * 获取评分模板
     * @param evaluatorType 评价人类型
     * @return 统一API响应格式的评分模板
     */
    FyfcApiResponseDto<Object> getScoreTemplate(String evaluatorType);

    /**
     * 批量导入评分
     * @param evaluationId 评价ID
     * @param scoreList 评分列表
     * @param operatorName 操作人
     * @return 统一API响应格式的导入结果
     */
    FyfcApiResponseDto<Object> batchImportScores(Integer evaluationId, List<FyfcScoreFormDto> scoreList, String operatorName);

    /**
     * 检查评分权限
     * @param evaluationId 评价ID
     * @param evaluatorName 评价人
     * @return 统一API响应格式的权限检查结果
     */
    FyfcApiResponseDto<Object> checkScorePermission(Integer evaluationId, String evaluatorName);
}
