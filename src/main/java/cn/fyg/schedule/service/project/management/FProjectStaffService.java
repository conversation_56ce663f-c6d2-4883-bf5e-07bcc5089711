package cn.fyg.schedule.service.project.management;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.project.management.FProjectStaffDto;

public interface FProjectStaffService {
    BaseResponse save(FProjectStaffDto dto);
    BaseResponse delete(Integer id);
    BaseResponse findByParentIdAndIsSiteAdminAndIsActivated(Integer parentId, Integer isActivated, Integer isSiteAdmin);
}
