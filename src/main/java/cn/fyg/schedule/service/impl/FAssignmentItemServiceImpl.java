package cn.fyg.schedule.service.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.assignment.FAssignmentItemRepository;
import cn.fyg.schedule.pojo.FAssignmentItem;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.FAssignmentItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class FAssignmentItemServiceImpl implements FAssignmentItemService {
    private final FAssignmentItemRepository r;

    @Autowired
    public FAssignmentItemServiceImpl(FAssignmentItemRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FAssignmentItem data) {
        return BaseService.save(data, r, FAssignmentItem.class);
    }

    @Override
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Optional<FAssignmentItem> result = r.findById(id);
            result.ifPresent(baseResponse::setResult);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse findByAssignmentId(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FAssignmentItem> result = r.findByAssignmentIdOrderByCreateDate(id);
            baseResponse.setResult(this.itemConversion(result));
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    private List<Object> itemConversion(List<FAssignmentItem> items) {
        List<Object> new_list = new ArrayList<>();
        for (FAssignmentItem item : items) {
            BaseService.setList(item.getType(), item.getContent(), item.getCreateDate(), new_list);
        }
        return new_list;
    }
}
