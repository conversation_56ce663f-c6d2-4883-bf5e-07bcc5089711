package cn.fyg.schedule.service.project.management.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.projectManagement.FProjectObjectiveRepository;
import cn.fyg.schedule.pojo.dto.project.management.FProjectObjectiveDto;
import cn.fyg.schedule.pojo.project.management.FProjectObjective;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.project.management.FProjectObjectiveService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;

@Service
@Slf4j
public class FProjectObjectiveServiceImpl implements FProjectObjectiveService {
    private final FProjectObjectiveRepository r;

    public FProjectObjectiveServiceImpl(FProjectObjectiveRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FProjectObjectiveDto dto) {
        FProjectObjective data = FProjectObjective.initialized(dto);
        return BaseService.save(data, r, FProjectObjective.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findByParentId(Integer parentId) {
        BaseResponse response = BaseResponse.initialize();
        List<FProjectObjective> list = getItemsByParentId(parentId);
        if (list != null) {
            response.setResult(list);
        } else {
            response.setCode(1);
            response.setMsg("查询出错");
        }
        return response;
    }

    private List<FProjectObjective> getItemsByParentId(Integer parentId) {
        try {
            List<FProjectObjective> items = r.findByParentId(parentId);
            return items;
        } catch (Exception e) {
            log.error(e.getLocalizedMessage());
        }
        return null;
    }
}
