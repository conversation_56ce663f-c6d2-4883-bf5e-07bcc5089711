package cn.fyg.schedule.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "aliyun.face")
public class AliyunEcsProperties {
    private String accessKeyId;
    private String accessKeySecret;
    private String regionId;
    private String endpoint;
}
