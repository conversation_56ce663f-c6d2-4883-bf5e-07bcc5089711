package cn.fyg.schedule.service.sheets.xincai.notification;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.project.yx.security.notification.YxSecurityNotiRecordDto;
import cn.fyg.schedule.pojo.project.xincai.noti.XcNotiRecord;

import java.util.List;

public interface XcNotiRecordService {
    BaseResponse save(YxSecurityNotiRecordDto dto);
    BaseResponse delete(Integer id);
    BaseResponse findById(Integer id);
    BaseResponse findByParentId(Integer id);
    List<XcNotiRecord> listRecordByParentId(Integer id, Integer mark);
    BaseResponse ifApproved(Integer id, Integer recordStatus);
}
