package cn.fyg.schedule.service.yx.couthersign;

import cn.fyg.schedule.pojo.dto.yx.countersign.CountersignDetailDto;
import cn.fyg.schedule.pojo.dto.yx.countersign.CountersignInfoDto;
import cn.fyg.schedule.pojo.yx.countersign.*;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;
@Service
public class YxCountersignConvertService {
    public CountersignInfoDto covertToDTO(YxCountersignInfo data) {
        List<CountersignDetailDto> detailDTOs = data.getItems().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        return CountersignInfoDto.convertToDTO(data, detailDTOs);
    }

    public CountersignDetailDto convertToDTO(YxCountersignDetail data) {
        DepartmentFunctionDTO functionDTO = convertToDTO(data.getDepartmentFunction());
        return CountersignDetailDto.convertToDTO(data, functionDTO);
    }

    public DepartmentFunctionDTO convertToDTO(YxDepartmentFunction data) {

        return DepartmentFunctionDTO.convertToDTO(data);
    }
}
