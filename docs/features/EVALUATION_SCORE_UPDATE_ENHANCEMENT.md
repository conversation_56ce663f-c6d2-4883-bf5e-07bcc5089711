# 评分接口增强 - 支持更新评价总分

## 🎯 **需求描述**

评分接口需要增加一个传参用来更新 evaluation 的 score，因为每次有人评分后最终评分会有所调整。

## ✅ **实现方案**

### **1. 修改 FyfcScoreFormDto**

在评分表单 DTO 中添加新字段：

```java
/**
 * 评价总分（用于更新evaluation表的score字段）
 */
private BigDecimal evaluationScore;
```

**字段说明**：
- **字段名**：`evaluationScore`
- **类型**：`BigDecimal`
- **用途**：传递计算后的评价总分，用于更新 evaluation 表的 score 字段
- **可选性**：可选字段，如果不提供则不更新评价总分

### **2. 修改评分服务实现**

#### **2.1 提交评分时更新总分**

```java
// 在 submitScore 方法中添加
// 保存评分
scoreRepository.save(score);

// 更新评价总分（如果提供了）
updateEvaluationScore(evaluation, scoreForm.getEvaluationScore());

// 检查是否需要更新评价状态
updateEvaluationStatusIfNeeded(evaluation, evaluatorType);
```

#### **2.2 更新评分时更新总分**

```java
// 在 updateScore 方法中添加
// 保存更新
scoreRepository.save(score);

// 更新评价总分（如果提供了）
Optional<FyfcEvaluation> optionalEvaluation = evaluationRepository.findById(scoreForm.getEvaluationId());
if (optionalEvaluation.isPresent()) {
    updateEvaluationScore(optionalEvaluation.get(), scoreForm.getEvaluationScore());
}
```

#### **2.3 新增 updateEvaluationScore 方法**

```java
/**
 * 更新评价总分
 */
private void updateEvaluationScore(FyfcEvaluation evaluation, BigDecimal evaluationScore) {
    try {
        if (ObjectUtil.isNotNull(evaluationScore)) {
            BigDecimal oldScore = evaluation.getScore();
            evaluation.setScore(evaluationScore);
            evaluationRepository.save(evaluation);
            
            log.info("评价总分已更新: evaluationId={}, {} -> {}", 
                evaluation.getId(), oldScore, evaluationScore);
        }
    } catch (Exception e) {
        log.error("更新评价总分失败: evaluationId={}", evaluation.getId(), e);
    }
}
```

## 🔄 **业务流程**

### **评分流程增强**

```mermaid
graph TD
    A[前端提交评分] --> B[包含 evaluationScore 参数]
    B --> C[后端接收评分数据]
    C --> D[验证评分数据]
    D --> E[保存个人评分记录]
    E --> F{是否提供了 evaluationScore?}
    F -->|是| G[更新 evaluation 表的 score 字段]
    F -->|否| H[跳过总分更新]
    G --> I[更新评价状态]
    H --> I
    I --> J[返回成功响应]
```

### **总分计算逻辑**

1. **前端计算**：
   - 前端根据业务规则计算最终评分
   - 可能包含权重、附加分等复杂逻辑
   - 将计算结果通过 `evaluationScore` 传递给后端

2. **后端更新**：
   - 后端接收前端计算的总分
   - 直接更新 evaluation 表的 score 字段
   - 记录更新日志便于追踪

## 📊 **修改内容总结**

### **修改的文件**

1. ✅ **FyfcScoreFormDto.java**
   - 添加 `evaluationScore` 字段
   - 用于传递评价总分

2. ✅ **FyfcEvaluationScoreServiceImpl.java**
   - 修改 `submitScore` 方法
   - 修改 `updateScore` 方法
   - 新增 `updateEvaluationScore` 方法

### **新增功能**

- ✅ **总分更新**：支持在评分时同步更新评价总分
- ✅ **日志记录**：记录总分更新的详细日志
- ✅ **错误处理**：总分更新失败不影响评分提交
- ✅ **可选参数**：evaluationScore 为可选，不影响现有功能

## 🎯 **使用示例**

### **前端调用示例**

```typescript
// 提交评分时包含评价总分
const submitScore = async (scoreForm: ScoreFormDto) => {
  // 计算评价总分（包含权重、附加分等）
  const evaluationScore = calculateFinalScore(scoreForm, additionalScore, weights);
  
  const response = await fyfcApi.staff.submitScore({
    ...scoreForm,
    evaluationScore: evaluationScore  // 传递计算后的总分
  }, userName);
  
  return response;
};

// 计算最终评分的示例函数
const calculateFinalScore = (scoreForm: ScoreFormDto, additionalScore: number, weights: any) => {
  const baseScore = scoreForm.performanceScore + scoreForm.attitudeScore + 
                   scoreForm.abilityScore + scoreForm.growthScore;
  
  // 应用权重和附加分
  const weightedScore = baseScore * weights.baseWeight;
  const finalScore = weightedScore + additionalScore;
  
  return Math.round(finalScore * 100) / 100; // 保留两位小数
};
```

### **API 请求示例**

```http
POST /api/fyfc/evaluation/staff/score/self?userName=employee1
Content-Type: application/json

{
  "evaluationId": 1,
  "performanceScore": 50,
  "attitudeScore": 8,
  "abilityScore": 9,
  "growthScore": 7,
  "signature": "张三",
  "evaluationScore": 78.5
}
```

### **后端日志示例**

```
2024-01-15 10:30:15 INFO  - 评价总分已更新: evaluationId=1, 75.0 -> 78.5
2024-01-15 10:30:15 INFO  - 评价状态已更新: evaluationId=1, SELF -> COLLEAGUE, reason=自评完成，转入同事评价阶段
```

## 🔍 **验证要点**

### **1. 功能验证**

- ✅ **提交评分**：evaluationScore 正确更新到 evaluation 表
- ✅ **更新评分**：修改评分时 evaluationScore 同步更新
- ✅ **可选参数**：不提供 evaluationScore 时不影响现有功能
- ✅ **错误处理**：总分更新失败不影响评分提交成功

### **2. 数据验证**

```sql
-- 验证评价总分是否正确更新
SELECT 
    e.id,
    e.name,
    e.score as evaluation_score,
    GROUP_CONCAT(s.score) as individual_scores
FROM fyfc_evaluations e
LEFT JOIN fyfc_evaluation_scores s ON e.id = s.evaluation_id
WHERE e.id = 1
GROUP BY e.id;
```

### **3. 日志验证**

- 检查评价总分更新日志
- 确认更新前后的分数变化
- 验证错误情况的日志记录

## 🚀 **扩展建议**

### **1. 总分计算规则**

可以考虑在后端也实现总分计算逻辑作为备选：

```java
/**
 * 计算评价总分（备选方案）
 */
private BigDecimal calculateEvaluationScore(Integer evaluationId) {
    // 获取所有评分
    List<FyfcEvaluationScore> scores = scoreRepository.findByEvaluationIdOrderByCreatedAtAsc(evaluationId);
    
    // 根据业务规则计算总分
    // 可以包含权重、附加分等逻辑
    
    return finalScore;
}
```

### **2. 总分历史记录**

可以考虑记录总分变更历史：

```java
/**
 * 记录总分变更历史
 */
private void recordScoreChange(Integer evaluationId, BigDecimal oldScore, BigDecimal newScore, String reason) {
    // 记录总分变更历史
}
```

### **3. 前端计算验证**

可以在后端验证前端计算的总分是否合理：

```java
/**
 * 验证评价总分
 */
private boolean validateEvaluationScore(Integer evaluationId, BigDecimal evaluationScore) {
    // 验证总分是否在合理范围内
    // 可以与后端计算结果进行对比
    return true;
}
```

## 🎉 **实现完成**

评分接口已成功增强，现在支持：

1. ✅ **传递评价总分**：通过 `evaluationScore` 参数
2. ✅ **自动更新**：评分时自动更新 evaluation 表的 score 字段
3. ✅ **向后兼容**：不影响现有的评分功能
4. ✅ **错误容忍**：总分更新失败不影响评分提交
5. ✅ **日志完整**：详细记录总分更新过程

现在前端可以在每次评分时传递计算后的总分，后端会自动更新评价记录的总分字段！🚀
