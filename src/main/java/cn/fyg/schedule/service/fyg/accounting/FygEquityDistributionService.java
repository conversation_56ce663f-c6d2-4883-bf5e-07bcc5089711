package cn.fyg.schedule.service.fyg.accounting;

import cn.fyg.schedule.pojo.dto.fyg.accounting.equity.EquityDistributionDTO;
import cn.fyg.schedule.reponse.BaseResponse;

import java.util.List;

public interface FygEquityDistributionService extends FygBaseService<EquityDistributionDTO, EquityDistributionDTO, Void, EquityDistributionDTO> {
    BaseResponse<List<EquityDistributionDTO>> list(Integer dataStatus, Integer projectId);

    BaseResponse<EquityDistributionDTO> findByProjectAndCounterparty(Integer projectId, String counterpartyAccountName, String equityType);
}
