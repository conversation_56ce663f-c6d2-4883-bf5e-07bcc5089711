package cn.fyg.schedule.service.eas.openApi;

import cn.hutool.json.JSON;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class EASService {
    private static final JSONConfig DEFAULT_JSONCONFIG = new JSONConfig();
    static {
        DEFAULT_JSONCONFIG.setIgnoreNullValue(false);
        DEFAULT_JSONCONFIG.setNatureKeyComparator();
        DEFAULT_JSONCONFIG.setDateFormat("yyyy-MM-dd HH:mm:ss");
    }

    private final EASOpenApiConfig easOpenApiConfig;

    public EASService(EASOpenApiConfig easOpenApiConfig) {
        this.easOpenApiConfig = easOpenApiConfig;
    }

    private String toArrayJsonString(Object param) {
        List<Object> data = Collections.singletonList(param);
        JSON parse = JSONUtil.parse(data, DEFAULT_JSONCONFIG);
        return JSONUtil.toJsonStr(parse);
    }

    public <T> List<T> callRetList(String method, Object param, Class<T> elementType) {

        String arrryJsonString = this.toArrayJsonString(param);

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("api", method);
        map.put("data", arrryJsonString);
        String ret = easOpenApiConfig.callAPI(map);
        if(ret==null) return null;
        JSONObject retJson = JSONUtil.parseObj(ret);
        String result = retJson.getStr("result");
        return JSONUtil.toList(result, elementType);
    }

    public <T> T callRetObject(String method, Object param, Class<T> elementType) {

        String arrryJsonString = this.toArrayJsonString(param);

        Map<String, Object> map = new LinkedHashMap<String, Object>();
        map.put("api", method);
        map.put("data", arrryJsonString);
        String ret = easOpenApiConfig.callAPI(map);
        if(ret==null) return null;
        JSONObject retJson = JSONUtil.parseObj(ret);
        String result = retJson.getStr("result");
        return JSONUtil.toBean(result, elementType);
    }

    public <T> T callRetObjectParamArray(String method, Object paramArray, Class<T> elementType) {

        JSON parse = JSONUtil.parse(paramArray, DEFAULT_JSONCONFIG);
        String arrryJsonString= JSONUtil.toJsonStr(parse);

        Map<String, Object> map = new LinkedHashMap<String, Object>();
        map.put("api", method);
        map.put("data", arrryJsonString);

        String ret = easOpenApiConfig.callAPI(map);
        if(ret==null) return null;

        JSONObject retJson = JSONUtil.parseObj(ret);
        String result = retJson.getStr("result");
        return JSONUtil.toBean(result, elementType);
    }

    public <T> List<T> callRetListParamArray(String method, Object paramArray, Class<T> elementType) {

        JSON parse = JSONUtil.parse(paramArray, DEFAULT_JSONCONFIG);
        String arrryJsonString= JSONUtil.toJsonStr(parse);

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("api", method);
        map.put("data", arrryJsonString);
        String ret = easOpenApiConfig.callAPI(map);
        if(ret==null) return null;

        JSONObject retJson = JSONUtil.parseObj(ret);
        String result = retJson.getStr("result");
        return JSONUtil.toList(result, elementType);
    }
}
