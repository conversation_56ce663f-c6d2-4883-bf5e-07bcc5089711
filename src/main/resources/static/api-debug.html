<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API 调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-item {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>🔍 API 调试页面</h1>

    <div class="test-item">
        <h3>1. 测试基础连接</h3>
        <button onclick="testBasicConnection()">测试根路径</button>
        <button onclick="testApiPath()">测试 API 路径</button>
        <div id="connectionResult" class="result"></div>
    </div>

    <div class="test-item">
        <h3>2. 测试 OSS 健康检查</h3>
        <button onclick="testOssHealth()">OSS 健康检查</button>
        <div id="ossHealthResult" class="result"></div>
    </div>

    <div class="test-item">
        <h3>3. 测试 FYFC API</h3>
        <button onclick="testFyfcApi()">测试 FYFC 评价 API</button>
        <div id="fyfcApiResult" class="result"></div>
    </div>

    <div class="test-item">
        <h3>4. 检查控制器映射</h3>
        <button onclick="checkControllerMappings()">检查控制器映射</button>
        <div id="mappingResult" class="result"></div>
    </div>

    <script>
        function showResult(elementId, data, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
        }

        // API 配置
        const CONTEXT_PATH = '/fyschedule2';
        const API_BASE = `${CONTEXT_PATH}/api/fyfc/oss`;
        const TEST_API_BASE = `${CONTEXT_PATH}/api/fyfc/oss/test`;
        const DIAGNOSTIC_API_BASE = `${CONTEXT_PATH}/api/fyfc/oss/diagnostic`;

        async function testBasicConnection() {
            try {
                console.log('测试根路径...');
                const response = await fetch(CONTEXT_PATH + '/');
                const text = await response.text();
                
                showResult('connectionResult', {
                    status: response.status,
                    statusText: response.statusText,
                    contentType: response.headers.get('content-type'),
                    content: text.substring(0, 100) + '...'
                }, response.ok);
            } catch (error) {
                showResult('connectionResult', { error: error.message }, false);
            }
        }

        async function testApiPath() {
            try {
                console.log('测试 API 路径...');
                const response = await fetch(CONTEXT_PATH + '/api');
                const text = await response.text();
                
                showResult('connectionResult', {
                    status: response.status,
                    statusText: response.statusText,
                    contentType: response.headers.get('content-type'),
                    content: text.substring(0, 100) + '...'
                }, response.ok);
            } catch (error) {
                showResult('connectionResult', { error: error.message }, false);
            }
        }

        async function testOssHealth() {
            const urls = [
                `${TEST_API_BASE}/health`,
                `${DIAGNOSTIC_API_BASE}/health`,
                `${API_BASE}/test/health`
            ];

            for (const url of urls) {
                try {
                    console.log(`测试 OSS 健康检查: ${url}`);
                    const response = await fetch(url);
                    
                    if (response.ok) {
                        const contentType = response.headers.get('content-type');
                        if (contentType && contentType.includes('application/json')) {
                            const data = await response.json();
                            showResult('ossHealthResult', {
                                url: url,
                                success: true,
                                data: data
                            }, true);
                            return;
                        } else {
                            const text = await response.text();
                            showResult('ossHealthResult', {
                                url: url,
                                status: response.status,
                                contentType: contentType,
                                content: text.substring(0, 200)
                            }, false);
                        }
                    } else {
                        const text = await response.text();
                        console.log(`${url} 失败:`, response.status, text);
                    }
                } catch (error) {
                    console.log(`${url} 错误:`, error.message);
                }
            }
            
            showResult('ossHealthResult', {
                error: '所有 OSS 健康检查 URL 都失败了',
                testedUrls: urls
            }, false);
        }

        async function testFyfcApi() {
            const urls = [
                `${CONTEXT_PATH}/api/fyfc/evaluation/common/1`,
                `${CONTEXT_PATH}/api/fyfc/evaluation/staff/pending/testUser`,
                `${CONTEXT_PATH}/api/fyfc/evaluation/admin/search`
            ];

            const results = [];
            
            for (const url of urls) {
                try {
                    console.log(`测试 FYFC API: ${url}`);
                    const response = await fetch(url);
                    
                    results.push({
                        url: url,
                        status: response.status,
                        statusText: response.statusText,
                        contentType: response.headers.get('content-type'),
                        ok: response.ok
                    });
                } catch (error) {
                    results.push({
                        url: url,
                        error: error.message
                    });
                }
            }
            
            showResult('fyfcApiResult', results, true);
        }

        async function checkControllerMappings() {
            // 检查 Spring Boot Actuator 端点（如果启用）
            const endpoints = [
                `${CONTEXT_PATH}/actuator`,
                `${CONTEXT_PATH}/actuator/mappings`,
                `${CONTEXT_PATH}/actuator/health`,
                `${CONTEXT_PATH}/actuator/info`
            ];

            const results = [];
            
            for (const endpoint of endpoints) {
                try {
                    console.log(`检查端点: ${endpoint}`);
                    const response = await fetch(endpoint);
                    
                    results.push({
                        endpoint: endpoint,
                        status: response.status,
                        available: response.ok
                    });
                    
                    if (response.ok && endpoint === '/actuator/mappings') {
                        const data = await response.json();
                        // 查找 OSS 相关的映射
                        const ossMappings = JSON.stringify(data).includes('fyfc/oss');
                        results.push({
                            endpoint: endpoint,
                            hasOssMappings: ossMappings,
                            data: data
                        });
                    }
                } catch (error) {
                    results.push({
                        endpoint: endpoint,
                        error: error.message
                    });
                }
            }
            
            showResult('mappingResult', results, true);
        }

        // 页面加载时自动运行基础测试
        window.onload = function() {
            console.log('页面加载完成，开始自动测试...');
            testBasicConnection();
            setTimeout(testOssHealth, 1000);
        };
    </script>
</body>
</html>
