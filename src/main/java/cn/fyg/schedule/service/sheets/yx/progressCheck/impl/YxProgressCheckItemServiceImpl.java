package cn.fyg.schedule.service.sheets.yx.progressCheck.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.yuanxin.progressCheck.YxProgressCheckItemRepository;
import cn.fyg.schedule.pojo.project.yuanXin.progressCheck.YxProgressCheckItem;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.sheets.yx.progressCheck.YxProgressCheckItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;
@Slf4j
@Service
public class YxProgressCheckItemServiceImpl implements YxProgressCheckItemService {
    private final YxProgressCheckItemRepository r;

    public YxProgressCheckItemServiceImpl(YxProgressCheckItemRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(YxProgressCheckItem data) {
        return BaseService.save(data, r, YxProgressCheckItem.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        YxProgressCheckItem data = getById(id);
        if (data != null) {
            response.setResult(data);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    private YxProgressCheckItem getById(Integer id) {
        try {
            Optional<YxProgressCheckItem> optional = r.findById(id);
            if (optional.isPresent()) {
                return optional.get();
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    @Override
    public BaseResponse findByParentId(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        List<YxProgressCheckItem> data = getItems(id);
        if (data != null) {
            response.setResult(data);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    private List<YxProgressCheckItem> getItems(Integer id) {
        try {
            List<YxProgressCheckItem> items = r.findByParentIdOrderByCreateDateDesc(id);
            if (!items.isEmpty()) {
                return items;
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }
}
