package cn.fyg.schedule.pojo.dto.statement.capital;

import cn.fyg.schedule.pojo.dto.BaseDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class FCapitalStatementDto extends BaseDto {
    private String statementDate;
    private String title;
    private List<FCapitalStatementSubject> array;
    private Integer scheduleId;
}
