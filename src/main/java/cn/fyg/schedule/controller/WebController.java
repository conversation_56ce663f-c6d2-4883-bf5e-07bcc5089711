package cn.fyg.schedule.controller;
import cn.fyg.schedule.base.FormNumberGeneratorService;
import cn.fyg.schedule.pojo.dto.project.yx.security.notification.YxSecurityNotificationDto;
import cn.fyg.schedule.pojo.project.xincai.noti.XcNotification;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Slf4j
public class WebController {

    private final FormNumberGeneratorService formNumberGeneratorService;

    public WebController(FormNumberGeneratorService formNumberGeneratorService) {
        this.formNumberGeneratorService = formNumberGeneratorService;
    }

    @GetMapping("test")
    public String test() {
//        YxSecurityNotificationDto dto = new YxSecurityNotificationDto();
//        dto.setCreator("xlh");
//        XcNotification data = XcNotification.initialize(dto);
        String test = formNumberGeneratorService.generateFormNumber();
        return test;
    }

    @PostMapping("/list")
    public ResponseEntity<String> receiveList(@RequestBody List<String> list) {
        // Do something with the list
        return ResponseEntity.ok("Received list with " + list.size() + " elements.");
    }

}
