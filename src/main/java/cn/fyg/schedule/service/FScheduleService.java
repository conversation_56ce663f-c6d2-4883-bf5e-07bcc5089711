package cn.fyg.schedule.service;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.FSchedule;
import cn.fyg.schedule.pojo.dto.FScheduleDto;

import java.util.List;

public interface FScheduleService {
    BaseResponse save(FSchedule fSchedule);
    BaseResponse delete(Integer id);
    BaseResponse findByCreator(String creator, Integer page, Integer size);
    BaseResponse findById(Integer id);
    BaseResponse findByCreatorAndStartDate(String creator, String startDate, Integer page, Integer size);
    BaseResponse findByCreatorAndDuration(String creator, String start, String duration, Integer page, Integer size);
    BaseResponse findByCreatorAndDurationAndCurrentStatus(String creator, String start, String duration, String currentStatus, Integer page, Integer size);
    BaseResponse search(String creator, String start, String duration, String searchText, Integer page, Integer size);
    BaseResponse findMarkedDate(String creator);
    BaseResponse getMarkedSchedule(String creator);

    BaseResponse findAllColumns(String table);

    BaseResponse listByFilter(FScheduleDto dto);

    List<FSchedule> findByVoucherTypeAndVoucherId(Integer voucherId, Integer voucherType);
}