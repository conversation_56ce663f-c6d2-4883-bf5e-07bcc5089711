package cn.fyg.schedule.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * FYFC OSS 上传配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "fyfc.oss.upload")
public class FyfcOssUploadProperties {

    /**
     * 单个文件大小限制（字节）
     * 默认 50MB
     */
    private Long maxFileSize = 50 * 1024 * 1024L;

    /**
     * 批量上传总大小限制（字节）
     * 默认 500MB
     */
    private Long maxBatchTotalSize = 500 * 1024 * 1024L;

    /**
     * 单次上传文件数量限制
     * 默认 10 个
     */
    private Integer maxFileCount = 10;

    /**
     * 允许的文件类型（MIME类型）
     * 空列表表示允许所有类型
     */
    private List<String> allowedMimeTypes = Arrays.asList();

    /**
     * 允许的文件扩展名
     * 空列表表示允许所有扩展名
     */
    private List<String> allowedExtensions = Arrays.asList();

    /**
     * 禁止的文件类型（MIME类型）
     * 用于安全考虑，禁止某些危险文件类型
     */
    private List<String> forbiddenMimeTypes = Arrays.asList(
        "application/x-executable",
        "application/x-msdownload",
        "application/x-msdos-program",
        "application/x-msi",
        "application/x-bat",
        "application/x-sh",
        "text/x-script"
    );

    /**
     * 禁止的文件扩展名
     */
    private List<String> forbiddenExtensions = Arrays.asList(
        ".exe", ".bat", ".cmd", ".com", ".scr", ".pif", ".msi", ".dll",
        ".sh", ".bash", ".zsh", ".fish", ".ps1", ".vbs", ".js", ".jar"
    );

    /**
     * 是否启用文件类型检查
     * 默认启用
     */
    private Boolean enableTypeCheck = true;

    /**
     * 是否启用文件大小检查
     * 默认启用
     */
    private Boolean enableSizeCheck = true;

    /**
     * 获取格式化的单个文件大小限制
     */
    public String getFormattedMaxFileSize() {
        return formatFileSize(maxFileSize);
    }

    /**
     * 获取格式化的批量总大小限制
     */
    public String getFormattedMaxBatchTotalSize() {
        return formatFileSize(maxBatchTotalSize);
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(Long sizeInBytes) {
        if (sizeInBytes == null) {
            return "未限制";
        }

        if (sizeInBytes < 1024) {
            return sizeInBytes + " B";
        } else if (sizeInBytes < 1024 * 1024) {
            return String.format("%.1f KB", sizeInBytes / 1024.0);
        } else if (sizeInBytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", sizeInBytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", sizeInBytes / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 检查文件类型是否被允许
     */
    public boolean isFileTypeAllowed(String mimeType, String fileName) {
        if (!enableTypeCheck) {
            return true;
        }

        // 检查是否在禁止列表中
        if (mimeType != null && forbiddenMimeTypes.contains(mimeType.toLowerCase())) {
            return false;
        }

        // 检查文件扩展名是否在禁止列表中
        if (fileName != null) {
            String extension = getFileExtension(fileName).toLowerCase();
            if (forbiddenExtensions.contains(extension)) {
                return false;
            }
        }

        // 如果设置了允许列表，检查是否在允许列表中
        if (!allowedMimeTypes.isEmpty()) {
            return mimeType != null && allowedMimeTypes.contains(mimeType.toLowerCase());
        }

        if (!allowedExtensions.isEmpty() && fileName != null) {
            String extension = getFileExtension(fileName).toLowerCase();
            return allowedExtensions.contains(extension);
        }

        // 默认允许（除了禁止列表中的）
        return true;
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf("."));
    }
}
