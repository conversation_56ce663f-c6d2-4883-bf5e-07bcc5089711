package cn.fyg.schedule.controller.yuanxin;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.FSignature;
import cn.fyg.schedule.pojo.dto.FSignatureDto;
import cn.fyg.schedule.service.FSignatureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("signature")
public class FSignatureController {
    @Autowired private FSignatureService service;

    @PostMapping("save")
    public BaseResponse save(FSignatureDto dto) {
        FSignature signature = FSignature.initialize(dto);
        return service.save(signature);
    }

    @PostMapping("delete")
    public BaseResponse delete(Integer id) {
        return service.delete(id);
    }

    @PostMapping("find")
    public BaseResponse find(Integer id) {
        return service.findById(id);
    }

    @PostMapping("list")
    public BaseResponse getList(Integer id) {
        return service.findByAssignmentId(id);
    }

}
