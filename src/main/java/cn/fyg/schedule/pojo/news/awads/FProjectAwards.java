package cn.fyg.schedule.pojo.news.awads;

import cn.fyg.schedule.pojo.dto.news.awards.FProjectAwardsDto;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Setter
@Getter
@ToString
@Table(name = "fprojectawards")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class FProjectAwards {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    private String company;
    private String awardAnnual;
    @Column(name = "award_date")
    @Temporal(TemporalType.DATE)
    private Date awardDate;
    private String awardLevel;
    private String awardName;
    private String awardingInstitution;
    private String note;
    @Column(name = "create_date")
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @Column(name = "update_date")
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;
    private String awardProject;
    private String contractor;
    private String awardType;
    private String creator;

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "award_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    @ToString.Exclude
    private List<FProjectAwardsItem> items = new ArrayList<>();

    public static FProjectAwards initialize(FProjectAwardsDto dto) {
        FProjectAwards data = new FProjectAwards();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        data.setCompany(dto.getCompany());
        data.setAwardAnnual(dto.getAwardAnnual());
        data.setAwardDate(DateUtil.date(dto.getAwardDate()));
        data.setAwardLevel(dto.getAwardLevel());
        data.setAwardName(dto.getAwardName());
        data.setAwardingInstitution(dto.getAwardingInstitution());
        data.setNote(dto.getNote());
        data.setCreator(dto.getCreator());
        if (dto.getCreateDate() != null) {
            data.setCreateDate(DateUtil.parse(dto.getCreateDate()));
        }
        data.setAwardProject(dto.getAwardProject());
        data.setContractor(dto.getContractor());
        data.setAwardType(dto.getAwardType());
        return data;
    }
}
