package cn.fyg.schedule.service.project.management.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.projectManagement.FProjectItemRepository;
import cn.fyg.schedule.pojo.project.management.FProjectItem;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.project.management.FProjectItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;

@Service
@Slf4j
public class FProjectItemServiceImpl implements FProjectItemService {
    private final FProjectItemRepository r;

    public FProjectItemServiceImpl(FProjectItemRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FProjectItem data) {
        return BaseService.save(data, r, FProjectItem.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findByParentId(Integer parentId) {
        BaseResponse response = BaseResponse.initialize();
        List<FProjectItem> list = getItemsByParentId(parentId);
        if (list != null) {
            response.setResult(list);
        } else {
            response.setCode(1);
            response.setMsg("查询出错");
        }
        return response;
    }

    private List<FProjectItem> getItemsByParentId(Integer parentId) {
        try {
            List<FProjectItem> items = r.findByParentId(parentId);
            return items;
        } catch (Exception e) {
            log.error(e.getLocalizedMessage());
        }
        return null;
    }
}
