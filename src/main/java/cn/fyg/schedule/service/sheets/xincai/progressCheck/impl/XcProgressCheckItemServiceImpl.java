package cn.fyg.schedule.service.sheets.xincai.progressCheck.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.xincai.progressCheck.XcProgressCheckItemRepository;
import cn.fyg.schedule.pojo.project.xincai.progressCheck.XcProgressCheckItem;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.sheets.xincai.progressCheck.XcProgressCheckItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;
@Slf4j
@Service
public class XcProgressCheckItemServiceImpl implements XcProgressCheckItemService {
    private final XcProgressCheckItemRepository r;

    public XcProgressCheckItemServiceImpl(XcProgressCheckItemRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(XcProgressCheckItem data) {
        return BaseService.save(data, r, XcProgressCheckItem.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        XcProgressCheckItem data = getById(id);
        if (data != null) {
            response.setResult(data);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    private XcProgressCheckItem getById(Integer id) {
        try {
            Optional<XcProgressCheckItem> optional = r.findById(id);
            if (optional.isPresent()) {
                return optional.get();
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    @Override
    public BaseResponse findByParentId(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        List<XcProgressCheckItem> data = getItems(id);
        if (data != null) {
            response.setResult(data);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    private List<XcProgressCheckItem> getItems(Integer id) {
        try {
            List<XcProgressCheckItem> items = r.findByParentIdOrderByCreateDateDesc(id);
            if (!items.isEmpty()) {
                return items;
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }
}
