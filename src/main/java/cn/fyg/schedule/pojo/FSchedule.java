package cn.fyg.schedule.pojo;

import cn.fyg.schedule.enums.ScheduleStatusEnum;
import cn.fyg.schedule.pojo.dto.FScheduleDto;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "fschedule")
public class FSchedule {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Column(name = "creator")
    private String creator;
    @Column(name = "start_date")
    @Temporal(TemporalType.DATE)
    private Date startDate;
    @Column(name = "start_time")
    @Temporal(TemporalType.TIME)
    private Date startTime;
    @Column(name = "end_date")
    @Temporal(TemporalType.DATE)
    private Date endDate;
    @Column(name = "end_time")
    @Temporal(TemporalType.TIME)
    private Date endTime;
    @Column(name = "create_date")
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @Column(name = "update_date")
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;
    @Column(name = "title")
    private String title;
    @Column(name = "content")
    private String content;
    @Column(name = "current_status")
    private String currentStatus;
    @Column(name = "sticky_topic")
    private Integer stickyTopic;

    @Column(name = "voucher_id")
    private Integer voucherId;

    @Column(name = "voucher_name")
    private String voucherName;

    @Column(name = "voucher_type")
    private Integer voucherType;

    public static FSchedule initialize(FScheduleDto dto) {
        FSchedule fSchedule = new FSchedule();
        if (dto.getId() != null) {
            fSchedule.setId(dto.getId());
        }
        fSchedule.setCreator(dto.getCreator());
        fSchedule.setStartDate(DateUtil.parseDate(dto.getStartDate()));
        fSchedule.setStartTime(DateUtil.parseTime(dto.getStartTime()));
        fSchedule.setEndDate(DateUtil.parseDate(dto.getEndDate()));
        fSchedule.setEndTime(DateUtil.parseTime(dto.getEndTime()));
        fSchedule.setTitle(dto.getTitle());
        fSchedule.setContent(dto.getContent());
        fSchedule.setVoucherId(dto.getVoucherId() == null || dto.getVoucherId().equals("") || dto.getVoucherId().equals("null") ? null : Integer.valueOf(dto.getVoucherId()));
        fSchedule.setVoucherName(dto.getVoucherId() == null || dto.getVoucherName().equals("") ? "未命名" + RandomUtil.randomInt(1, 100) : dto.getVoucherName());
        fSchedule.setVoucherType(dto.getVoucherType() == null || dto.getVoucherType().equals("") || dto.getVoucherType().equals("null") ? null : Integer.valueOf(dto.getVoucherType()));
        if (dto.getCreateDate() != null) {
            fSchedule.setCreateDate(DateUtil.parse(dto.getCreateDate()));
        }
        fSchedule.setCurrentStatus(ScheduleStatusEnum.valueOf(dto.getValue()).getName());
        fSchedule.setStickyTopic(dto.getStickyTopic() != null?dto.getStickyTopic():0);
        return fSchedule;
    }
}
