package cn.fyg.schedule.controller.xincai;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.project.yx.progressCheck.YxProgressCheckDto;
import cn.fyg.schedule.pojo.dto.project.yx.progressCheck.YxProgressCheckRecordDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.pojo.project.xincai.progressCheck.XcProgressCheckItem;
import cn.fyg.schedule.pojo.project.xincai.progressCheck.XcProgressCheckRecordItem;
import cn.fyg.schedule.pojo.project.xincai.progressCheck.XcProgressCheckRecordSign;
import cn.fyg.schedule.pojo.project.xincai.progressCheck.XcProgressCheckSign;
import cn.fyg.schedule.service.sheets.xincai.progressCheck.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("xc/progress/check")
public class XcProgressCheckController {
    private final XcProgressCheckService service;
    private final XcProgressCheckRecordService recordService;
    private final XcProgressCheckItemService itemService;
    private final XcProgressCheckSignService signService;
    private final XcProgressCheckRecordItemService recordItemService;
    private final XcProgressCheckRecordSignService recordSignService;

    public XcProgressCheckController(XcProgressCheckService service, XcProgressCheckRecordService recordService, XcProgressCheckItemService itemService, XcProgressCheckSignService signService, XcProgressCheckRecordItemService recordItemService, XcProgressCheckRecordSignService recordSignService) {
        this.service = service;
        this.recordService = recordService;
        this.itemService = itemService;
        this.signService = signService;
        this.recordItemService = recordItemService;
        this.recordSignService = recordSignService;
    }


    @PostMapping("filter")
    public BaseResponse filter(YxProgressCheckDto dto, Pagination pagination) {
        return service.filter(dto, pagination);
    }

    @PostMapping("history/inspection/department")
    public BaseResponse listHistoryInspectionDepartment() {
        return service.listInspectionDepartmentHistory();
    }

    @PostMapping("find/id")
    public BaseResponse findById(Integer id) {
        return service.findById(id);
    }

    @PostMapping ("save")
    public BaseResponse saveProgressCheck(YxProgressCheckDto dto) {
        return service.save(dto);
    }

    @PostMapping("delete")
    public BaseResponse deleteProgressCheck(Integer id) {
        return service.delete(id);
    }

    @PostMapping("find/item")
    public BaseResponse findItems(Integer id) {
        return itemService.findByParentId(id);
    }

    @PostMapping("delete/item")
    public BaseResponse deleteItem(Integer id) {
        return itemService.delete(id);
    }

    @PostMapping("save/item")
    public BaseResponse saveItem(XcProgressCheckItem data) {
        return itemService.save(data);
    }

    @PostMapping("find/signature")
    public BaseResponse findSignature(Integer id) {
        return signService.findByParentId(id);
    }

    @PostMapping("delete/signature")
    public BaseResponse deleteSignature(Integer id) {
        return signService.delete(id);
    }

    @PostMapping("save/signature")
    public BaseResponse saveSignature(XcProgressCheckSign data) {
        return signService.save(data);
    }

    @PostMapping("save/record")
    public BaseResponse saveRecord(YxProgressCheckRecordDto dto) {
        return recordService.save(dto);
    }

    @PostMapping("find/record")
    public BaseResponse findRecord(Integer id) {
        return recordService.findByParentId(id);
    }

    @PostMapping("delete/record")
    public BaseResponse deleteRecord(Integer id) {
        return recordService.delete(id);
    }

    @PostMapping("find/record/item")
    public BaseResponse findRecordItem(Integer id) {
        return recordItemService.findByParentId(id);
    }

    @PostMapping("save/record/item")
    public BaseResponse saveRecordItem(XcProgressCheckRecordItem data) {
        return recordItemService.save(data);
    }

    @PostMapping("delete/record/item")
    public BaseResponse deleteRecordItem(Integer id) {
        return recordItemService.delete(id);
    }

    @PostMapping("find/record/sign")
    public BaseResponse findRecordSign(Integer id) {
        return recordSignService.findByParentId(id);
    }

    @PostMapping("save/record/sign")
    public BaseResponse saveRecordSign(XcProgressCheckRecordSign data) {
        return recordSignService.save(data);
    }

    @PostMapping("delete/record/sign")
    public BaseResponse deleteRecordSign(Integer id) {
        return recordSignService.deleteByParentId(id);
    }
}
