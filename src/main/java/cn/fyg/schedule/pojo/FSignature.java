package cn.fyg.schedule.pojo;

import cn.fyg.schedule.pojo.dto.FSignatureDto;
import cn.hutool.core.date.DateUtil;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "fsignature")
public class FSignature {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Column(name = "assignment_id")
    private Integer assignmentId;
    @Column(name = "signature")
    private String signature;
    @Column(name = "signatory")
    private String signatory;
    @Column(name = "type")
    private String type;
    @Column(name = "creator")
    private String creator;
    private String comments;
    @Column(name = "create_date")
    @CreationTimestamp
    @Temporal(TemporalType.DATE)
    private Date createDate;
    @Column(name = "update_date")
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;

    public static FSignature initialize(FSignatureDto dto) {
        FSignature signature = new FSignature();
        if (dto.getId() != null) {
            signature.setId(dto.getId());
        }
        signature.setSignature(dto.getSignature());
        signature.setSignatory(dto.getSignatory());
        signature.setAssignmentId(dto.getAssignmentId());
        signature.setCreator(dto.getCreator());
        signature.setType(dto.getType());
        if (dto.getCreateDate() != null) {
            signature.setCreateDate(DateUtil.parse(dto.getCreateDate()));
        }
        signature.setComments(dto.getComments());
        return signature;
    }
}
