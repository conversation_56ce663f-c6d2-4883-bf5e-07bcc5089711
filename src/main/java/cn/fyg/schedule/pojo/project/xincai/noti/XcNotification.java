package cn.fyg.schedule.pojo.project.xincai.noti;

import cn.fyg.schedule.pojo.dto.project.yx.security.notification.YxSecurityNotificationDto;
import cn.fyg.schedule.utils.DataInitialize;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Setter
@Getter
@ToString
@Table(name = "xc_notification")
public class XcNotification {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    private String creator;
    private String creatorName;
    @Column(name = "yx_project_name")
    private String yxProjectName;
    @Column(name = "yx_time_limit")
    private Integer yxTimeLimit;
    @Column(name = "yx_problem_description")
    private String yxProblemDescription;
    @Column(name = "create_date")
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @Column(name = "update_date")
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;
    @Column(name = "inspection_date")
    @Temporal(TemporalType.DATE)
    private Date inspectionDate;
    private Integer type;
    private String yxEventName;
    private Integer needSign;
    private String lastEditor;
    private Integer dataStatus;
    @Column(name = "event_status")
    private Integer eventStatus;

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "parent_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    @JsonIgnore
    @ToString.Exclude
    List<XcNotiItem> items = new ArrayList<>();

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "parent_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    @JsonIgnore
    @ToString.Exclude
    List<XcNotiSign> signatures = new ArrayList<>();

    public static XcNotification initialize(YxSecurityNotificationDto dto) {
        XcNotification data = new XcNotification();
        DataInitialize.mapDtoToObject(dto, data);
        return data;
    }
}
