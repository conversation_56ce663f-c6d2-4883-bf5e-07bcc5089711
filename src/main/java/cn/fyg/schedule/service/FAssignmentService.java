package cn.fyg.schedule.service;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.FAssignment;

import java.util.List;

public interface FAssignmentService {
    BaseResponse findAllByIdIsNotNull(Integer page, Integer size);
    BaseResponse findById(Integer id);
    BaseResponse findByCreator(String creator, Integer page, Integer size);
    BaseResponse findByScheduleIdAndCreator(Integer id, String creator);
    List<FAssignment> listBySecheduleIdAndCreator(Integer id, String creator);
    BaseResponse save(FAssignment assignment);
    BaseResponse delete(Integer id);
    BaseResponse findAssignmentByVoucher(Integer voucherId, Integer voucherType);
}
