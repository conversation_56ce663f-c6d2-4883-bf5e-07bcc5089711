package cn.fyg.schedule.service.fyapp.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.fyapp.FAppRegisterRepository;
import cn.fyg.schedule.pojo.fyapp.FAppRegister;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.fyapp.FAppRegisterS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class FAppRegisterSI implements FAppRegisterS {
    private final FAppRegisterRepository r;
    public FAppRegisterSI(FAppRegisterRepository r) {
        this.r = r;
    }
    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FAppRegister data) {
        return BaseService.save(data, r, FAppRegister.class);
    }

    @Override
    public BaseResponse findByGroupId(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FAppRegister> list = r.findByGroupIdOrderByAppSortAsc(id);
            baseResponse.setResult(list);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse deleteByAppIdAndGroupId(Integer appId, Integer groupId) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            r.deleteByAppIdAndGroupId(appId, groupId);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse deleteByGroupId(Integer groupId) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            r.deleteByGroupId(groupId);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public List<Integer> getAppIdsByGroupId(Integer id) {
        List<Integer> list = new ArrayList<>();
        try {
            List<FAppRegister> temps = r.findByGroupIdOrderByAppSortAsc(id);
            for (FAppRegister temp : temps) {
                list.add(temp.getAppId());
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return list;
    }
}
