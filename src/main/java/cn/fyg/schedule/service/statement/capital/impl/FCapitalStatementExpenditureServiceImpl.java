package cn.fyg.schedule.service.statement.capital.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.statement.capital.FCapitalStatementExpenditureRepository;
import cn.fyg.schedule.pojo.statement.capital.FCapitalStatementExpenditure;
import cn.fyg.schedule.pojo.statement.capital.FCapitalStatementIncome;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.statement.capital.FCapitalStatementExpenditureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class FCapitalStatementExpenditureServiceImpl implements FCapitalStatementExpenditureService {
    private final FCapitalStatementExpenditureRepository r;
    public FCapitalStatementExpenditureServiceImpl(FCapitalStatementExpenditureRepository r) { this.r = r; }
    @Override
    public BaseResponse save(FCapitalStatementExpenditure data) {
        return BaseService.save(data, r, FCapitalStatementExpenditure.class);
    }

    @Override
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse list(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FCapitalStatementExpenditure> result = r.findByCapitalStatementIdOrderByNumberDesc(id);
            baseResponse.setResult(result);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse findById(Integer id) {
        return null;
    }

    @Override
    public Double sumCurrentDayIncome(Integer id) {
        Double result = 0.00;
        try {
            List<FCapitalStatementExpenditure> list = r.findByCapitalStatementIdOrderByNumberDesc(id);
            for (FCapitalStatementExpenditure expenditure : list) {
                result += expenditure.getCurrentDayAmount();
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return result;
    }

    @Override
    public Double cumCurrentDayIncome(Integer id) {
        Double result = 0.00;
        try {
            List<FCapitalStatementExpenditure> list = r.findByCapitalStatementIdOrderByNumberDesc(id);
            for (FCapitalStatementExpenditure expenditure : list) {
                result += expenditure.getCurrentDayAmount() + expenditure.getCumulativeAmount();
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return result;
    }

    @Override
    public BaseResponse getHistoryAmount(Integer id, String subject) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FCapitalStatementExpenditure> result = r.findByCapitalStatementIdAndCurrencySubject(id, subject);
            Double sum = 0.00;
            if (result.size() > 0) {
                for (FCapitalStatementExpenditure expenditure : result) {
                    sum += expenditure.getCumulativeAmount();
                }
            }
            baseResponse.setResult(sum);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }
}
