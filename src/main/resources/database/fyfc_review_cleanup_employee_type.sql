-- =====================================================
-- FYFC Review 系统数据库清理脚本
-- 移除 EMPLOYEE 评价人类型支持
-- =====================================================

-- 1. 检查是否还有 'employee' 类型的数据（应该为 0）
SELECT 
    'employee' as type_name,
    COUNT(*) as remaining_count,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ 无遗留数据，可以安全移除'
        ELSE '❌ 仍有遗留数据，需要先迁移'
    END as status
FROM `fyfc_evaluation_scores` 
WHERE `type` = 'employee';

-- 2. 显示当前数据分布
SELECT 
    `type`,
    COUNT(*) as count,
    CONCAT(ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM `fyfc_evaluation_scores`), 2), '%') as percentage
FROM `fyfc_evaluation_scores` 
GROUP BY `type`
ORDER BY count DESC;

-- 3. 修改表结构，移除 'employee' 选项
-- 注意：只有在确认没有 'employee' 数据时才执行此操作
ALTER TABLE `fyfc_evaluation_scores` 
MODIFY COLUMN `type` enum('unknown','self','colleague','manager','admin') NOT NULL COMMENT '评价人角色类型';

-- 4. 验证表结构修改结果
SHOW COLUMNS FROM `fyfc_evaluation_scores` LIKE 'type';

-- 5. 最终验证 - 确保所有数据仍然有效
SELECT 
    `type`,
    COUNT(*) as count,
    'Valid' as validation_status
FROM `fyfc_evaluation_scores` 
GROUP BY `type`
UNION ALL
SELECT 
    'TOTAL' as type,
    COUNT(*) as count,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ 所有数据有效'
        ELSE '⚠️ 无数据'
    END as validation_status
FROM `fyfc_evaluation_scores`;

-- 6. 显示清理完成信息
SELECT 
    'Cleanup completed successfully!' as status,
    NOW() as cleanup_time,
    'employee type removed from enum' as action,
    (SELECT COUNT(*) FROM `fyfc_evaluation_scores` WHERE `type` = 'self') as self_records,
    (SELECT COUNT(*) FROM `fyfc_evaluation_scores` WHERE `type` = 'colleague') as colleague_records,
    (SELECT COUNT(*) FROM `fyfc_evaluation_scores` WHERE `type` = 'manager') as manager_records,
    (SELECT COUNT(*) FROM `fyfc_evaluation_scores` WHERE `type` = 'admin') as admin_records;
