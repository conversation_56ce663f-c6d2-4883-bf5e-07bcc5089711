package cn.fyg.schedule.specification.xincai;

import cn.fyg.schedule.pojo.project.xincai.noti.XcNotification;
import cn.fyg.schedule.utils.SpecificationUtil;
import org.springframework.data.jpa.domain.Specification;

import java.util.Date;

public class XcNotificationSpecification {
    public static Specification<XcNotification> ifContain(String column, String expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.like(root.get(column), SpecificationUtil.contains(expression)));
    }

    public static Specification<XcNotification> ifEq(String column, String expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }

    public static Specification<XcNotification> ifEq(String column, Integer expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }
    public static Specification<XcNotification> ifDateEq(String column, Date expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }
    public static Specification<XcNotification> ifDateBetween(String column, Date begin, Date end) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.between(root.get(column), begin, end));
    }
}
