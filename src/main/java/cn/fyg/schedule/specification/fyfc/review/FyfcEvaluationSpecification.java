package cn.fyg.schedule.specification.fyfc.review;

import cn.fyg.schedule.enums.fyfc.review.EvaluationStatus;
import cn.fyg.schedule.pojo.dto.fyfc.review.FyfcEvaluationQueryDto;
import cn.fyg.schedule.pojo.fyfc.review.FyfcEvaluation;
import cn.fyg.schedule.specification.BaseSpecification;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * FYFC 评价 Specification
 * 使用 Hutool 工具包简化查询条件构建
 */
public class FyfcEvaluationSpecification {

    /**
     * 根据查询DTO构建完整的查询条件
     */
    public static Specification<FyfcEvaluation> buildSpecification(FyfcEvaluationQueryDto query) {
        return (Root<FyfcEvaluation> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 部门模糊查询
            if (StrUtil.isNotBlank(query.getDepartment())) {
                predicates.add(cb.like(cb.lower(root.get("department")), 
                    StrUtil.format("%{}%", query.getDepartment().toLowerCase())));
            }

            // 姓名模糊查询
            if (StrUtil.isNotBlank(query.getName())) {
                predicates.add(cb.like(cb.lower(root.get("name")), 
                    StrUtil.format("%{}%", query.getName().toLowerCase())));
            }

            // 状态精确查询
            if (StrUtil.isNotBlank(query.getStatus())) {
                try {
                    EvaluationStatus status = EvaluationStatus.valueOf(query.getStatus().toUpperCase());
                    predicates.add(cb.equal(root.get("status"), status));
                } catch (IllegalArgumentException e) {
                    // 忽略无效的状态值
                }
            }

            // 创建人精确查询
            if (StrUtil.isNotBlank(query.getCreatedBy())) {
                predicates.add(cb.equal(root.get("createdBy"), query.getCreatedBy()));
            }

            // 同事姓名查询
            if (StrUtil.isNotBlank(query.getColleagueName())) {
                predicates.add(cb.equal(root.get("colleagueName"), query.getColleagueName()));
            }

            // 主管姓名查询
            if (StrUtil.isNotBlank(query.getManagerName())) {
                predicates.add(cb.equal(root.get("managerName"), query.getManagerName()));
            }

            // 评价日期范围查询
            if (ObjectUtil.isNotNull(query.getReviewDateStart()) || ObjectUtil.isNotNull(query.getReviewDateEnd())) {
                addDateRangeCondition(predicates, cb, root, "reviewDate", 
                    query.getReviewDateStart(), query.getReviewDateEnd());
            }

            // 创建时间范围查询
            if (ObjectUtil.isNotNull(query.getCreatedAtStart()) || ObjectUtil.isNotNull(query.getCreatedAtEnd())) {
                addDateRangeCondition(predicates, cb, root, "createdAt", 
                    query.getCreatedAtStart(), query.getCreatedAtEnd());
            }

            // 特殊查询类型处理
            addQueryTypeCondition(predicates, cb, root, query);

            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * 添加日期范围条件（支持时间戳）
     */
    private static void addDateRangeCondition(List<Predicate> predicates, CriteriaBuilder cb, 
                                            Root<FyfcEvaluation> root, String attribute, 
                                            Long startTimestamp, Long endTimestamp) {
        if (ObjectUtil.isNotNull(startTimestamp)) {
            Date startDate = new Date(startTimestamp);
            predicates.add(cb.greaterThanOrEqualTo(root.get(attribute), startDate));
        }
        if (ObjectUtil.isNotNull(endTimestamp)) {
            Date endDate = DateUtil.endOfDay(new Date(endTimestamp));
            predicates.add(cb.lessThanOrEqualTo(root.get(attribute), endDate));
        }
    }

    /**
     * 添加特殊查询类型条件
     */
    private static void addQueryTypeCondition(List<Predicate> predicates, CriteriaBuilder cb, 
                                            Root<FyfcEvaluation> root, FyfcEvaluationQueryDto query) {
        String queryType = StrUtil.blankToDefault(query.getQueryType(), "all");
        String currentUser = query.getCurrentUser();

        switch (queryType) {
            case "pending":
                // 待办事项：当前用户相关的未完成评价
                if (StrUtil.isNotBlank(currentUser)) {
                    Predicate pendingPredicate = cb.or(
                        // 作为被评价人且状态为自评
                        cb.and(cb.equal(root.get("name"), currentUser), 
                               cb.equal(root.get("status"), EvaluationStatus.SELF)),
                        // 作为同事且状态为同事评价
                        cb.and(cb.equal(root.get("colleagueName"), currentUser), 
                               cb.equal(root.get("status"), EvaluationStatus.COLLEAGUE)),
                        // 作为主管且状态为主管评价
                        cb.and(cb.equal(root.get("managerName"), currentUser), 
                               cb.equal(root.get("status"), EvaluationStatus.MANAGER))
                    );
                    predicates.add(pendingPredicate);
                }
                break;
            case "completed":
                // 已完成的评价
                predicates.add(cb.equal(root.get("status"), EvaluationStatus.COMPLETED));
                break;
            case "my_evaluations":
                // 我创建的评价
                if (StrUtil.isNotBlank(currentUser)) {
                    predicates.add(cb.equal(root.get("createdBy"), currentUser));
                }
                break;
            case "about_me":
                // 与我相关的评价（作为被评价人、同事或主管）
                if (StrUtil.isNotBlank(currentUser)) {
                    Predicate aboutMePredicate = cb.or(
                        cb.equal(root.get("name"), currentUser),
                        cb.equal(root.get("colleagueName"), currentUser),
                        cb.equal(root.get("managerName"), currentUser)
                    );
                    predicates.add(aboutMePredicate);
                }
                break;
            default:
                // "all" 或其他情况，不添加额外条件
                break;
        }
    }

    /**
     * 按部门查询
     */
    public static Specification<FyfcEvaluation> byDepartment(String department) {
        return BaseSpecification.addSpecificationIfNotEmpty(
            Specification.where(null), "department", department, BaseSpecification::likeIgnoreCase);
    }

    /**
     * 按姓名查询
     */
    public static Specification<FyfcEvaluation> byName(String name) {
        return BaseSpecification.addSpecificationIfNotEmpty(
            Specification.where(null), "name", name, BaseSpecification::likeIgnoreCase);
    }

    /**
     * 按状态查询
     */
    public static Specification<FyfcEvaluation> byStatus(EvaluationStatus status) {
        return BaseSpecification.addSpecificationIfNotEmpty(
            Specification.where(null), "status", status, BaseSpecification::equals);
    }

    /**
     * 按创建人查询
     */
    public static Specification<FyfcEvaluation> byCreatedBy(String createdBy) {
        return BaseSpecification.addSpecificationIfNotEmpty(
            Specification.where(null), "createdBy", createdBy, BaseSpecification::equals);
    }

    /**
     * 按同事姓名查询
     */
    public static Specification<FyfcEvaluation> byColleagueName(String colleagueName) {
        return BaseSpecification.addSpecificationIfNotEmpty(
            Specification.where(null), "colleagueName", colleagueName, BaseSpecification::equals);
    }

    /**
     * 按主管姓名查询
     */
    public static Specification<FyfcEvaluation> byManagerName(String managerName) {
        return BaseSpecification.addSpecificationIfNotEmpty(
            Specification.where(null), "managerName", managerName, BaseSpecification::equals);
    }

    /**
     * 按评价日期范围查询
     */
    public static Specification<FyfcEvaluation> byReviewDateRange(Date startDate, Date endDate) {
        return BaseSpecification.dateRange("reviewDate", startDate, endDate);
    }

    /**
     * 按创建时间范围查询
     */
    public static Specification<FyfcEvaluation> byCreatedAtRange(Date startDate, Date endDate) {
        return BaseSpecification.dateRange("createdAt", startDate, endDate);
    }

    /**
     * 查询用户的待办评价
     */
    public static Specification<FyfcEvaluation> pendingForUser(String userName) {
        return (Root<FyfcEvaluation> root, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
            if (StrUtil.isBlank(userName)) {
                return cb.disjunction(); // 返回 false
            }
            
            return cb.or(
                // 作为被评价人且状态为自评
                cb.and(cb.equal(root.get("name"), userName), 
                       cb.equal(root.get("status"), EvaluationStatus.SELF)),
                // 作为同事且状态为同事评价
                cb.and(cb.equal(root.get("colleagueName"), userName), 
                       cb.equal(root.get("status"), EvaluationStatus.COLLEAGUE)),
                // 作为主管且状态为主管评价
                cb.and(cb.equal(root.get("managerName"), userName), 
                       cb.equal(root.get("status"), EvaluationStatus.MANAGER))
            );
        };
    }

    /**
     * 查询与用户相关的所有评价
     */
    public static Specification<FyfcEvaluation> relatedToUser(String userName) {
        return (Root<FyfcEvaluation> root, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
            if (StrUtil.isBlank(userName)) {
                return cb.disjunction(); // 返回 false
            }

            return cb.or(
                cb.equal(root.get("name"), userName),
                cb.equal(root.get("colleagueName"), userName),
                cb.equal(root.get("managerName"), userName),
                cb.equal(root.get("createdBy"), userName)
            );
        };
    }

    /**
     * 排除指定状态的评价
     */
    public static Specification<FyfcEvaluation> statusNotEquals(EvaluationStatus status) {
        return (Root<FyfcEvaluation> root, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
            if (ObjectUtil.isNull(status)) {
                return cb.conjunction(); // 返回 true，不添加任何条件
            }

            return cb.notEqual(root.get("status"), status);
        };
    }
}
