package cn.fyg.schedule.pojo.fyg.accounting;

import cn.fyg.schedule.enums.fyg.accounting.EquityType;
import cn.fyg.schedule.enums.fyg.accounting.EquityTypeConverter;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

@Entity
@Setter
@Getter
@ToString
@Table(name = "fyg_equity_distribution")
public class FygEquityDistribution {

    // 主键ID，自动生成
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    // 项目ID，关联项目表
    @Column(name = "project_id", nullable = false)
    private Integer projectId;

    // 股东ID，关联对方账户表
    @Column(name = "counterparty_account_id")
    private Integer counterpartyAccountId;

    // 对方户名，关联对方账户表
    @Column(name = "counterparty_account_name")
    private String counterpartyAccountName;

    // 股权类型
    @Convert(converter = EquityTypeConverter.class)
    @Column(name = "equity_type", nullable = false)
    private EquityType equityType;

    // 股权比例，百分比
    @Column(name = "equity_ratio", precision = 5, scale = 2, nullable = false)
    private BigDecimal equityRatio;

    // 股权转让时间，存储日期
    @Column(name = "transfer_date")
    @Temporal(TemporalType.DATE)
    private Date transferDate;

    // 数据状态，1表示正常，0表示删除
    @Column(name = "data_status", columnDefinition = "tinyint(4) default 1")
    private Integer dataStatus;

    // 创建时间，自动生成
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;

    // 更新时间，自动更新
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at", nullable = false)
    private Date updatedAt;

    // 创建者
    @Column(name = "created_by", length = 50)
    private String createdBy;

    // 最后更新者
    @Column(name = "updated_by", length = 50)
    private String updatedBy;

    // 外键关联注解
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", referencedColumnName = "id", insertable = false, updatable = false)
    private FygProjects project;
}

