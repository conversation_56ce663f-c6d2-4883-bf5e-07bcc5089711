package cn.fyg.schedule.service.fg.approval.document;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.fy.approval.document.FgDocumentApprovalDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;

public interface FgDocumentApprovalService {
    BaseResponse save(FgDocumentApprovalDto dto);
    BaseResponse delete(Integer id);
    BaseResponse update(FgDocumentApprovalDto dto);
    BaseResponse update(String spNo, Integer id);
    BaseResponse update(Integer isApplied, Integer id);
    BaseResponse list(Pagination pagination);
    BaseResponse findById(Integer id);
    BaseResponse findBySpNo(String spNo);
    BaseResponse synchronize(String spNo, String templateId, String contents);
    BaseResponse synchronize(String spNo, String templateId, String contents, String records);
    BaseResponse filter(FgDocumentApprovalDto dto, Pagination pagination);
}
