package cn.fyg.schedule.service.wsvoucher;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class WSVoucherInfo {

	private final String bosType = "7B9EBA6F";// bostype

	private String companyNumber;// 公司编码
	private Date bookedDate;// 记账日期
	private Date bizDate;// 业务日期
	private Integer periodYear;// 期间年
	private Integer periodNumber;// 期间月
	private String voucherType;// 凭证类型
	private String voucherNumber;// 凭证号
	
	private Integer entrySeq;// 凭证分录号
	private String voucherAbstract;// 摘要
	private String accountNumber;// 科目编码
	private String currencyNumber;// 币别编码
	private BigDecimal localRate;// 本位币汇率
	private Integer entryDC;// 分录方向
	private BigDecimal originalAmount;// 原币金额
	private BigDecimal qty;// 数量
	private String measurement;// 计量单位编码
	private BigDecimal price;// 单价
	private BigDecimal debitAmount;// 借方金额
	private BigDecimal creditAmount;// 贷方金额
	private String creator;// 制单人
	private String poster;// 过账人
	private String auditor;// 审核人
	private Integer attaches;// 附件数量
	private String description;// 参考信息
	private Integer asstSeq;// 辅助账序号
	private String bizNumber;// 业务编号
	private String settlementType;// 结算方式
	private String settlementNumber;// 结算号
	private String asstActType1;// 核算项目名称1
	private String asstActType2;// 核算项目名称2
	private String asstActType3;// 核算项目名称3
	private String asstActType4;// 核算项目名称4
	private String asstActType5;// 核算项目名称5
	private String asstActType6;// 核算项目名称6
	private String asstActType7;// 核算项目名称7
	private String asstActType8;// 核算项目名称8
	private String asstActNumber1;// 核算对象编码1
	private String asstActNumber2;// 核算对象编码
	private String asstActNumber3;// 核算对象编码
	private String asstActNumber4;// 核算对象编码
	private String asstActNumber5;// 核算对象编码
	private String asstActNumber6;// 核算对象编码
	private String asstActNumber7;// 核算对象编码
	private String asstActNumber8;// 核算对象编码
	private String asstActName1;// 核算对象名称1
	private String asstActName2;// 核算对象名称
	private String asstActName3;// 核算对象名称
	private String asstActName4;// 核算对象名称
	private String asstActName5;// 核算对象名称
	private String asstActName6;// 核算对象名称
	private String asstActName7;// 核算对象名称
	private String asstActName8;// 核算对象名称
	private Integer oppAccountSeq;// 对方分录号
	private String primaryItem;// 主表项目
	private String supplyItem;// 附表项目
	private BigDecimal primaryCoef;// 主表系数
	private BigDecimal supplyCoef;// 附表系数
	private BigDecimal cashflowAmountOriginal;// 现金流量原币金额
	private BigDecimal cashflowAmountLocal;// 现金流量本位币金额
	private BigDecimal cashflowAmountRpt;// 现金流量报告币金额
	private Integer itemFlag;// 现金流量标记
	private String invoiceNumber;// 发票号
	private String icketNumber;// 票证号码
	private Boolean isCheck;// 是否勾稽
	private Integer cussent;// 往来编号
	private String tempNumber;// 临时编码
	private String customerNumber;// 内部业务单位
	private Date assistBizDate;// 辅助账业务日期
	private Date assistEndDate;// 辅助账结束日期
	private String type;// 现金流量性质列
	private String cashAsstActType1;// 现金流量核算项目1
	private String cashAsstActType2;//
	private String cashAsstActType3;//
	private String cashAsstActType4;//
	private String cashAsstActType5;//
	private String cashAsstActType6;//
	private String cashAsstActType7;//
	private String cashAsstActType8;//
	private String cashAsstActNumber1;// 现金流量核算对象编码1
	private String cashAsstActNumber2;//
	private String cashAsstActNumber3;//
	private String cashAsstActNumber4;//
	private String cashAsstActNumber5;//
	private String cashAsstActNumber6;//
	private String cashAsstActNumber7;//
	private String cashAsstActNumber8;//
	private String cashAsstActName1;// 核算对象名称1
	private String cashAsstActName2;//
	private String cashAsstActName3;//
	private String cashAsstActName4;//
	private String cashAsstActName5;//
	private String cashAsstActName6;//
	private String cashAsstActName7;//
	private String cashAsstActName8;//

	private String profitCenterNumber;//
	private String sourceBillId;// 原单ID
	private String voucherId;// 凭证Id
	private String cashAssGrp;// 现金流量项目横表ID
	private String assistAbstract;// 辅助账摘要

}
