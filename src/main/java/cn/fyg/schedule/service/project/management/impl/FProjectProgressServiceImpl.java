package cn.fyg.schedule.service.project.management.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.projectManagement.FProjectProgressRepository;
import cn.fyg.schedule.pojo.dto.project.management.FProjectProgressDto;
import cn.fyg.schedule.pojo.project.management.FProjectProgress;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.project.management.FProjectProgressService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;

@Service
@Slf4j
public class FProjectProgressServiceImpl implements FProjectProgressService {
    private final FProjectProgressRepository r;

    public FProjectProgressServiceImpl(FProjectProgressRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FProjectProgressDto dto) {
        FProjectProgress data = FProjectProgress.initialized(dto);
        return BaseService.save(data, r, FProjectProgress.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findByParentId(Integer parentId) {
        BaseResponse response = BaseResponse.initialize();
        List<FProjectProgress> list = getItemsByParentId(parentId);
        if (list != null) {
            response.setResult(list);
        } else {
            response.setCode(1);
            response.setMsg("查询出错");
        }
        return response;
    }

    private List<FProjectProgress> getItemsByParentId(Integer parentId) {
        try {
            List<FProjectProgress> items = r.findByParentId(parentId);
            return items;
        } catch (Exception e) {
            log.error(e.getLocalizedMessage());
        }
        return null;
    }
}
