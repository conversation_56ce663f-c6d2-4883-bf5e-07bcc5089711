package cn.fyg.schedule.controller.functions;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.service.aliyun.CompareFaceService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("ai")
public class CompareFaceController {
    private final CompareFaceService service;
    public CompareFaceController(CompareFaceService service) {
        this.service = service;
    }
    @PostMapping("compare/face")
    public BaseResponse compareFace(String imgA, String imgB) {
        return service.compareFace(imgA, imgB);
    }
}
