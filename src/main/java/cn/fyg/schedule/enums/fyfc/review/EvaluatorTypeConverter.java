package cn.fyg.schedule.enums.fyfc.review;

import lombok.extern.slf4j.Slf4j;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/**
 * 评价人类型枚举转换器
 * 用于JPA实体与数据库之间的转换
 */
@Slf4j
@Converter(autoApply = true)
public class EvaluatorTypeConverter implements AttributeConverter<EvaluatorType, String> {

    @Override
    public String convertToDatabaseColumn(EvaluatorType attribute) {
        if (attribute == null) {
            return null;
        }
        return attribute.getValue();
    }

    @Override
    public EvaluatorType convertToEntityAttribute(String dbData) {
        if (dbData == null) {
            return null;
        }

        try {
            // 直接匹配枚举值
            for (EvaluatorType type : EvaluatorType.values()) {
                if (type.getValue().equals(dbData)) {
                    return type;
                }
            }

            // 如果没有找到，记录错误并返回默认值
            log.error("未知的评价人类型数据库值: {}, 使用 UNKNOWN 作为默认值", dbData);
            return EvaluatorType.UNKNOWN;

        } catch (Exception e) {
            log.error("转换评价人类型时发生错误: dbData={}", dbData, e);
            return EvaluatorType.UNKNOWN;
        }
    }
}
