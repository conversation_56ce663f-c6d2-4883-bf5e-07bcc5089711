package cn.fyg.schedule.controller.mini.app;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.FScheduleStatus;
import cn.fyg.schedule.pojo.dto.FScheduleStatusDto;
import cn.fyg.schedule.pojo.dto.query.FScheduleQuery;
import cn.fyg.schedule.service.FScheduleStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("schedulestatus")
public class FScheduleStatusController {
    @Autowired private FScheduleStatusService service;

    @PostMapping("save")
    public BaseResponse save(FScheduleStatusDto dto) {
        FScheduleStatus status = FScheduleStatus.initialize(dto);
        return service.save(status);
    }

    @PostMapping("delete")
    public BaseResponse delete(Integer id) {
        return service.delete(id);
    }

    @PostMapping("status")
    public BaseResponse getList(FScheduleQuery query) {
        return service.findByScheduleId(query.getScheduleId());
    }
}
