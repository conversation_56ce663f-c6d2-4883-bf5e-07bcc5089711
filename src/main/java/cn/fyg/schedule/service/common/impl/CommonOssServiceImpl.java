package cn.fyg.schedule.service.common.impl;

import cn.fyg.schedule.config.AliyunOssProperties;
import cn.fyg.schedule.config.FyfcOssUploadProperties;
import cn.fyg.schedule.pojo.dto.fyfc.review.FyfcApiResponseDto;
import cn.fyg.schedule.pojo.dto.fyfc.review.FyfcAttachmentDto;
import cn.fyg.schedule.service.common.ICommonOssService;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.ObjectMetadata;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.List;

/**
 * 通用 OSS 服务实现
 * 提供基础的文件操作功能，不依赖具体业务逻辑
 */
@Slf4j
@Service
public class CommonOssServiceImpl implements ICommonOssService {

    private final AliyunOssProperties ossProperties;
    private final FyfcOssUploadProperties uploadProperties;

    public CommonOssServiceImpl(AliyunOssProperties ossProperties,
                               FyfcOssUploadProperties uploadProperties) {
        this.ossProperties = ossProperties;
        this.uploadProperties = uploadProperties;
    }

    @Override
    public FyfcApiResponseDto<FyfcAttachmentDto> uploadFile(MultipartFile file, String folder, String uploadBy) {
        return uploadFile(file, folder, uploadBy, null);
    }

    @Override
    public FyfcApiResponseDto<FyfcAttachmentDto> uploadFile(MultipartFile file, String folder, String uploadBy, String bucketName) {
        String targetBucket = getTargetBucket(bucketName);
        log.info("通用OSS上传文件: fileName={}, folder={}, uploadBy={}, bucket={}", 
            file.getOriginalFilename(), folder, uploadBy, targetBucket);
        
        try {
            // 参数验证
            if (file.isEmpty()) {
                return FyfcApiResponseDto.error(400, "文件不能为空");
            }
            if (StrUtil.isBlank(folder)) {
                return FyfcApiResponseDto.error(400, "文件夹路径不能为空");
            }
            if (StrUtil.isBlank(uploadBy)) {
                return FyfcApiResponseDto.error(400, "上传人不能为空");
            }
            
            // 文件验证
            FyfcApiResponseDto<String> validationResult = validateFile(file);
            if (!validationResult.getSuccess()) {
                return FyfcApiResponseDto.error(validationResult.getCode(), validationResult.getMessage());
            }
            
            // 生成文件键
            String fileKey = generateFileKey(file.getOriginalFilename(), folder);
            
            // 上传到OSS
            OSS ossClient = createOssClient();
            try {
                // 检查bucket是否存在
                if (!ossClient.doesBucketExist(targetBucket)) {
                    return FyfcApiResponseDto.error(500, "OSS Bucket不存在: " + targetBucket);
                }
                
                // 设置文件元数据
                ObjectMetadata metadata = new ObjectMetadata();
                metadata.setContentLength(file.getSize());
                metadata.setContentType(file.getContentType());
                metadata.addUserMetadata("original-name", encodeUserMetadata(file.getOriginalFilename()));
                metadata.addUserMetadata("upload-by", encodeUserMetadata(uploadBy));
                metadata.addUserMetadata("folder", encodeUserMetadata(folder));
                metadata.addUserMetadata("bucket-name", targetBucket);
                metadata.addUserMetadata("upload-time", String.valueOf(System.currentTimeMillis()));
                
                // 上传文件
                InputStream inputStream = new ByteArrayInputStream(file.getBytes());
                ossClient.putObject(targetBucket, fileKey, inputStream, metadata);
                
                // 创建附件DTO
                FyfcAttachmentDto attachment = new FyfcAttachmentDto();
                attachment.setId(UUID.randomUUID().toString());
                attachment.setFileName(file.getOriginalFilename());
                attachment.setFileKey(fileKey);
                attachment.setBucketName(targetBucket);
                attachment.setFileSize(file.getSize());
                attachment.setFileType(file.getContentType());
                attachment.setUploadTime(System.currentTimeMillis());
                attachment.setUploadBy(uploadBy);
                
                log.info("通用OSS文件上传成功: fileKey={}", fileKey);
                return FyfcApiResponseDto.success(attachment, "文件上传成功");
                
            } finally {
                ossClient.shutdown();
            }
            
        } catch (Exception e) {
            log.error("通用OSS文件上传失败", e);
            return FyfcApiResponseDto.error("文件上传失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<List<FyfcAttachmentDto>> uploadFiles(MultipartFile[] files, String folder, String uploadBy) {
        return uploadFiles(files, folder, uploadBy, null);
    }

    @Override
    public FyfcApiResponseDto<List<FyfcAttachmentDto>> uploadFiles(MultipartFile[] files, String folder, String uploadBy, String bucketName) {
        String targetBucket = getTargetBucket(bucketName);
        log.info("通用OSS批量上传文件: fileCount={}, folder={}, uploadBy={}, bucket={}", 
            files.length, folder, uploadBy, targetBucket);
        
        try {
            if (files == null || files.length == 0) {
                return FyfcApiResponseDto.error(400, "文件列表不能为空");
            }
            
            // 验证批量上传限制
            FyfcApiResponseDto<String> batchValidationResult = validateBatchUpload(files);
            if (!batchValidationResult.getSuccess()) {
                return FyfcApiResponseDto.error(batchValidationResult.getCode(), batchValidationResult.getMessage());
            }
            
            List<FyfcAttachmentDto> attachments = new ArrayList<>();
            List<String> errors = new ArrayList<>();
            
            for (MultipartFile file : files) {
                if (file != null && !file.isEmpty()) {
                    FyfcApiResponseDto<FyfcAttachmentDto> result = uploadFile(file, folder, uploadBy, bucketName);
                    if (result.getSuccess()) {
                        attachments.add(result.getData());
                    } else {
                        errors.add(file.getOriginalFilename() + ": " + result.getMessage());
                    }
                }
            }
            
            if (attachments.isEmpty()) {
                return FyfcApiResponseDto.error(400, "所有文件上传失败: " + String.join(", ", errors));
            }
            
            String message = "批量上传完成";
            if (!errors.isEmpty()) {
                message += "，部分文件失败: " + String.join(", ", errors);
            }
            
            return FyfcApiResponseDto.success(attachments, message);
            
        } catch (Exception e) {
            log.error("通用OSS批量上传文件失败", e);
            return FyfcApiResponseDto.error("批量上传失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<Boolean> deleteFile(String fileKey) {
        return deleteFile(fileKey, null);
    }

    @Override
    public FyfcApiResponseDto<Boolean> deleteFile(String fileKey, String bucketName) {
        String targetBucket = getTargetBucket(bucketName);
        log.info("通用OSS删除文件: fileKey={}, bucket={}", fileKey, targetBucket);
        
        try {
            if (StrUtil.isBlank(fileKey)) {
                return FyfcApiResponseDto.error(400, "文件键不能为空");
            }
            
            OSS ossClient = createOssClient();
            try {
                if (ossClient.doesObjectExist(targetBucket, fileKey)) {
                    ossClient.deleteObject(targetBucket, fileKey);
                    log.info("通用OSS文件删除成功: bucket={}, fileKey={}", targetBucket, fileKey);
                } else {
                    log.warn("通用OSS文件不存在: bucket={}, fileKey={}", targetBucket, fileKey);
                }
            } finally {
                ossClient.shutdown();
            }
            
            return FyfcApiResponseDto.success(true, "文件删除成功");
            
        } catch (Exception e) {
            log.error("通用OSS删除文件失败", e);
            return FyfcApiResponseDto.error("删除文件失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<String> getFileUrl(String fileKey, Integer expireSeconds) {
        return getFileUrl(fileKey, expireSeconds, null);
    }

    @Override
    public FyfcApiResponseDto<String> getFileUrl(String fileKey, Integer expireSeconds, String bucketName) {
        String targetBucket = getTargetBucket(bucketName);
        log.info("通用OSS获取文件URL: fileKey={}, expireSeconds={}, bucket={}", fileKey, expireSeconds, targetBucket);
        
        try {
            if (StrUtil.isBlank(fileKey)) {
                return FyfcApiResponseDto.error(400, "文件键不能为空");
            }
            
            if (expireSeconds == null || expireSeconds <= 0) {
                expireSeconds = 3600; // 默认1小时
            }
            
            OSS ossClient = createOssClient();
            try {
                // 检查文件是否存在
                if (!ossClient.doesObjectExist(targetBucket, fileKey)) {
                    return FyfcApiResponseDto.error(404, "文件不存在");
                }
                
                // 生成签名URL
                Date expiration = new Date(System.currentTimeMillis() + expireSeconds * 1000L);
                URL url = ossClient.generatePresignedUrl(targetBucket, fileKey, expiration);
                
                return FyfcApiResponseDto.success(url.toString(), "获取文件URL成功");
                
            } finally {
                ossClient.shutdown();
            }
            
        } catch (Exception e) {
            log.error("通用OSS获取文件URL失败", e);
            return FyfcApiResponseDto.error("获取文件URL失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<Boolean> fileExists(String fileKey) {
        return fileExists(fileKey, null);
    }

    @Override
    public FyfcApiResponseDto<Boolean> fileExists(String fileKey, String bucketName) {
        String targetBucket = getTargetBucket(bucketName);
        
        try {
            if (StrUtil.isBlank(fileKey)) {
                return FyfcApiResponseDto.error(400, "文件键不能为空");
            }
            
            OSS ossClient = createOssClient();
            try {
                boolean exists = ossClient.doesObjectExist(targetBucket, fileKey);
                return FyfcApiResponseDto.success(exists, exists ? "文件存在" : "文件不存在");
            } finally {
                ossClient.shutdown();
            }
            
        } catch (Exception e) {
            log.error("通用OSS检查文件存在失败", e);
            return FyfcApiResponseDto.error("检查文件失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<FyfcAttachmentDto> getFileInfo(String fileKey) {
        return getFileInfo(fileKey, null);
    }

    @Override
    public FyfcApiResponseDto<FyfcAttachmentDto> getFileInfo(String fileKey, String bucketName) {
        String targetBucket = getTargetBucket(bucketName);
        log.info("通用OSS获取文件信息: fileKey={}, bucket={}", fileKey, targetBucket);

        try {
            if (StrUtil.isBlank(fileKey)) {
                return FyfcApiResponseDto.error(400, "文件键不能为空");
            }

            OSS ossClient = createOssClient();
            try {
                // 检查文件是否存在
                if (!ossClient.doesObjectExist(targetBucket, fileKey)) {
                    return FyfcApiResponseDto.error(404, "文件不存在");
                }

                // 获取文件元数据
                ObjectMetadata metadata = ossClient.getObjectMetadata(targetBucket, fileKey);

                // 创建附件DTO
                FyfcAttachmentDto attachment = new FyfcAttachmentDto();
                attachment.setFileKey(fileKey);
                attachment.setBucketName(targetBucket);
                attachment.setFileSize(metadata.getContentLength());
                attachment.setFileType(metadata.getContentType());

                // 解码用户元数据
                if (metadata.getUserMetadata() != null) {
                    String originalName = metadata.getUserMetadata().get("original-name");
                    if (StrUtil.isNotBlank(originalName)) {
                        attachment.setFileName(decodeUserMetadata(originalName));
                    }

                    String uploadBy = metadata.getUserMetadata().get("upload-by");
                    if (StrUtil.isNotBlank(uploadBy)) {
                        attachment.setUploadBy(decodeUserMetadata(uploadBy));
                    }

                    String uploadTime = metadata.getUserMetadata().get("upload-time");
                    if (StrUtil.isNotBlank(uploadTime)) {
                        try {
                            attachment.setUploadTime(Long.parseLong(uploadTime));
                        } catch (NumberFormatException e) {
                            log.warn("解析上传时间失败: {}", uploadTime);
                            attachment.setUploadTime(System.currentTimeMillis());
                        }
                    } else {
                        // 如果没有上传时间，使用文件的最后修改时间
                        attachment.setUploadTime(metadata.getLastModified().getTime());
                    }
                }

                return FyfcApiResponseDto.success(attachment, "获取文件信息成功");

            } finally {
                ossClient.shutdown();
            }

        } catch (Exception e) {
            log.error("通用OSS获取文件信息失败", e);
            return FyfcApiResponseDto.error("获取文件信息失败: " + e.getMessage());
        }
    }

    @Override
    public String generateFileKey(String originalFileName, String folder) {
        return generateFileKey(originalFileName, folder, null);
    }

    @Override
    public String generateFileKey(String originalFileName, String folder, String businessId) {
        String datePath = DateUtil.format(new Date(), "yyyy/MM/dd");
        String fileExtension = "";
        
        if (StrUtil.isNotBlank(originalFileName) && originalFileName.contains(".")) {
            fileExtension = originalFileName.substring(originalFileName.lastIndexOf("."));
        }
        
        String uuid = UUID.randomUUID().toString().replace("-", "");
        
        // 构建文件路径
        StringBuilder pathBuilder = new StringBuilder();
        pathBuilder.append(folder).append("/").append(datePath);
        
        if (StrUtil.isNotBlank(businessId)) {
            pathBuilder.append("/").append(businessId);
        }
        
        pathBuilder.append("/").append(uuid).append(fileExtension);
        
        return pathBuilder.toString();
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 获取目标bucket名称
     */
    private String getTargetBucket(String bucketName) {
        return StrUtil.isNotBlank(bucketName) ? bucketName : ossProperties.getBucketName();
    }

    /**
     * 创建OSS客户端
     */
    private OSS createOssClient() {
        return new OSSClientBuilder().build(
            ossProperties.getEndpoint(),
            ossProperties.getAccessKeyId(),
            ossProperties.getAccessKeySecret()
        );
    }

    /**
     * 验证单个文件
     */
    private FyfcApiResponseDto<String> validateFile(MultipartFile file) {
        if (!uploadProperties.getEnableSizeCheck()) {
            return FyfcApiResponseDto.success("跳过大小检查");
        }
        
        // 检查文件大小
        if (file.getSize() > uploadProperties.getMaxFileSize()) {
            return FyfcApiResponseDto.error(400, 
                String.format("文件大小超过限制，最大允许 %s，当前文件 %s", 
                    uploadProperties.getFormattedMaxFileSize(),
                    formatFileSize(file.getSize())));
        }
        
        // 检查文件类型
        if (uploadProperties.getEnableTypeCheck()) {
            if (!uploadProperties.isFileTypeAllowed(file.getContentType(), file.getOriginalFilename())) {
                return FyfcApiResponseDto.error(400, 
                    String.format("不允许的文件类型: %s (%s)", 
                        file.getOriginalFilename(), file.getContentType()));
            }
        }
        
        return FyfcApiResponseDto.success("文件验证通过");
    }

    /**
     * 验证批量上传
     */
    private FyfcApiResponseDto<String> validateBatchUpload(MultipartFile[] files) {
        // 检查文件数量
        if (files.length > uploadProperties.getMaxFileCount()) {
            return FyfcApiResponseDto.error(400, 
                String.format("文件数量超过限制，最大允许 %d 个，当前 %d 个", 
                    uploadProperties.getMaxFileCount(), files.length));
        }
        
        // 检查总大小
        long totalSize = 0;
        for (MultipartFile file : files) {
            if (file != null && !file.isEmpty()) {
                totalSize += file.getSize();
            }
        }
        
        if (totalSize > uploadProperties.getMaxBatchTotalSize()) {
            return FyfcApiResponseDto.error(400, 
                String.format("批量上传总大小超过限制，最大允许 %s，当前 %s", 
                    uploadProperties.getFormattedMaxBatchTotalSize(),
                    formatFileSize(totalSize)));
        }
        
        return FyfcApiResponseDto.success("批量上传验证通过");
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(Long sizeInBytes) {
        if (sizeInBytes == null) {
            return "未知";
        }

        if (sizeInBytes < 1024) {
            return sizeInBytes + " B";
        } else if (sizeInBytes < 1024 * 1024) {
            return String.format("%.1f KB", sizeInBytes / 1024.0);
        } else if (sizeInBytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", sizeInBytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", sizeInBytes / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 编码用户元数据，处理中文字符
     * 如果包含非ASCII字符，则进行Base64编码
     */
    private String encodeUserMetadata(String value) {
        if (StrUtil.isBlank(value)) {
            return value;
        }

        try {
            // 检查是否包含非ASCII字符
            if (!value.matches("^[\\x00-\\x7F]*$")) {
                // 包含非ASCII字符，进行Base64编码
                String encoded = Base64.getEncoder().encodeToString(value.getBytes(StandardCharsets.UTF_8));
                log.debug("编码用户元数据: {} -> {}", value, encoded);
                return encoded;
            }
            return value;
        } catch (Exception e) {
            log.warn("编码用户元数据失败: {}", value, e);
            return value;
        }
    }

    /**
     * 解码用户元数据
     * 尝试Base64解码，如果失败则返回原值
     */
    private String decodeUserMetadata(String encodedValue) {
        if (StrUtil.isBlank(encodedValue)) {
            return encodedValue;
        }

        try {
            // 尝试Base64解码
            byte[] decoded = Base64.getDecoder().decode(encodedValue);
            String decodedValue = new String(decoded, StandardCharsets.UTF_8);
            log.debug("解码用户元数据: {} -> {}", encodedValue, decodedValue);
            return decodedValue;
        } catch (IllegalArgumentException e) {
            // 解码失败，可能是原始ASCII字符
            log.debug("用户元数据无需解码: {}", encodedValue);
            return encodedValue;
        } catch (Exception e) {
            log.warn("解码用户元数据失败: {}", encodedValue, e);
            return encodedValue;
        }
    }
}
