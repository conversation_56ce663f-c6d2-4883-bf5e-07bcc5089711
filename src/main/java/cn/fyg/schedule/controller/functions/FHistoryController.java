package cn.fyg.schedule.controller.functions;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.service.core.FHistoryService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("history")
public class FHistoryController {
    private final FHistoryService service;

    public FHistoryController(FHistoryService service) {
        this.service = service;
    }

    @PostMapping("find")
    public BaseResponse findHistoryByHistoryIdAndTableName(String historyId, String tableName) {
        return service.findHistory(historyId, tableName);
    }
}
