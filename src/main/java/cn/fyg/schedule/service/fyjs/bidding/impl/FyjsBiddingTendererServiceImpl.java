package cn.fyg.schedule.service.fyjs.bidding.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.fyjs.bidding.FyjsBiddingTendererRepository;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.dto.fyjs.bidding.FyjsBiddingTendererDto;
import cn.fyg.schedule.pojo.fyjs.bidding.FyjsBiddingTenderer;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.fyjs.bidding.FyjsBiddingTendererService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.sql.SQLIntegrityConstraintViolationException;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class FyjsBiddingTendererServiceImpl implements FyjsBiddingTendererService {
    private final FyjsBiddingTendererRepository r;

    public FyjsBiddingTendererServiceImpl(FyjsBiddingTendererRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse save(FyjsBiddingTendererDto dto) {
        BaseResponse response = BaseResponse.initialize();
        try {
            FyjsBiddingTenderer data = FyjsBiddingTenderer.initialized(dto);
            // 在事务内保存并立即预加载
            FyjsBiddingTenderer savedTenderer = r.save(data);
            preloadLazyCollections(savedTenderer);
            response.setResult(savedTenderer);
        } catch (Exception e) {
            log.error(e.getMessage());
            response.setCode(1);
            response.setMsg("保存失败");
        }
        return response;
    }

    @Override
    public BaseResponse delete(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        try {
            BaseService.deleteWithR(id, r);
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse findById(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        try {
            // 直接在事务内查询和预加载
            Optional<FyjsBiddingTenderer> optional = r.findById(id);
            if (optional.isPresent()) {
                FyjsBiddingTenderer data = optional.get();
                // 立即在事务内预加载懒加载的集合
                preloadLazyCollections(data);
                response.setResult(data);
            } else {
                response.setCode(1);
                response.setMsg("数据不存在");
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse findByParentId(Integer parentId) {
        BaseResponse response = BaseResponse.initialize();
        try {
            // 在事务内完成查询和预加载
            List<FyjsBiddingTenderer> data = r.findByParentIdOrderByWinnerDesc(parentId);
            // 立即在事务内预加载懒加载的集合
            for (FyjsBiddingTenderer tenderer : data) {
                preloadLazyCollections(tenderer);
            }
            response.setResult(data);
        } catch (Exception e) {
            log.error(e.getMessage());
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse findByParentIdAndTendererId(Integer parentId, String tendererId) {
        BaseResponse response = BaseResponse.initialize();
        try {
            // 在事务内完成查询和预加载
            Optional<FyjsBiddingTenderer> optional = r.findByParentIdAndTendererId(parentId, tendererId);
            if (optional.isPresent()) {
                FyjsBiddingTenderer data = optional.get();
                // 立即在事务内预加载懒加载的集合
                preloadLazyCollections(data);
                response.setResult(data);
            } else {
                response.setCode(1);
                response.setMsg("未查到数据");
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    @Override
    public BaseResponse deleteByParentIdAndTendererId(Integer parentId, String tendererId) {
        BaseResponse response = BaseResponse.initialize();
        try {
            Integer count = deleteTenderer(parentId, tendererId);
            response.setResult(count);
            response.setMsg("删除" + count + "条数据。");
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }

    private Integer deleteTenderer(Integer parentId, String tendererId) throws MyException {
        try {
            return r.deleteByParentIdAndTendererId(parentId, tendererId);
        } catch (JpaSystemException e) {
            throw new MyException(500, e.getMessage());
        } catch (Exception e) {
            throw new MyException(500, "无效操作");
        }
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse findByParentIdAndTendererIdAndWinner(Integer parentId, String tendererId, Integer winner) {
        BaseResponse response = BaseResponse.initialize();
        try {
            // 在事务内完成查询和预加载
            List<FyjsBiddingTenderer> data = r.findByParentIdAndTendererIdAndWinner(parentId, tendererId, winner);
            if (data.isEmpty()) {
                response.setCode(1);
                response.setMsg("未查到数据");
            } else {
                // 立即在事务内预加载懒加载的集合
                for (FyjsBiddingTenderer tenderer : data) {
                    preloadLazyCollections(tenderer);
                }
                response.setResult(data);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    /**
     * 预加载懒加载的集合，避免LazyInitializationException
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    private void preloadLazyCollections(FyjsBiddingTenderer tenderer) {
        if (tenderer != null) {
            try {
                // 触发懒加载 - 我们只需要触发加载，不需要使用返回值
                if (tenderer.getDocuments() != null) {
                    tenderer.getDocuments().size(); // 触发懒加载
                }
            } catch (Exception e) {
                log.warn("预加载懒加载集合时出错: {}", e.getMessage());
                // 不抛出异常，允许继续执行
            }
        }
    }
}
