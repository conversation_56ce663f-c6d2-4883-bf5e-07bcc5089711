package cn.fyg.schedule.pojo.fyapp;

import cn.fyg.schedule.pojo.dto.fyapp.FAppUserSettingDto;
import cn.hutool.core.date.DateUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

@Entity
@Setter
@Getter
@ToString
@Table(name = "f_app_user_setting")
public class FAppUserSetting {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    private String userId; //数据平台账号username
    private String appKey; //应用筛选用
    private String appRouter; //对应页面
    private String appRemark; //描述
    private String appSetting; //用户设置json
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;

    public static FAppUserSetting initialized(FAppUserSettingDto dto) {
        FAppUserSetting data = new FAppUserSetting();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        data.setUserId(dto.getUserId());
        data.setAppKey(dto.getAppKey());
        data.setAppRouter(dto.getAppRouter());
        data.setAppRemark(dto.getAppRemark());
        data.setAppSetting(dto.getAppSetting());
        if (dto.getCreateDate() != null) {
            data.setCreateDate(DateUtil.date(dto.getCreateDate()));
        }
        return data;
    }
}
