package cn.fyg.schedule.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class BigDecimalUtils {

    public static BigDecimal safe(BigDecimal value) {
        return value != null ? value : BigDecimal.ZERO;
    }

    /**
     * 转换金额为万元单位，保留两位小数
     *
     * @param amount 金额（单位：元）
     * @return 转换后的金额（单位：万元）
     */
    public static String toWan(BigDecimal amount) {
        return amount != null ? amount.divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_UP).toPlainString() : "0.00";
    }

    /**
     * 安全比较两个 BigDecimal 是否大于 0
     *
     * @param value BigDecimal 值
     * @return 如果大于 0 返回 true，否则返回 false
     * compareTo 方法比较两个 BigDecimal 的值：
     * 返回 0 表示两个值相等。
     * 返回 -1 表示当前值小于 BigDecimal.ZERO。
     * 返回 1 表示当前值大于 BigDecimal.ZERO。
     */
    public static boolean isGreaterThanZero(BigDecimal value) {
        return safe(value).compareTo(BigDecimal.ZERO) <= 0;
//        safe(value).compareTo(BigDecimal.ZERO) > 0;
    }

    public static boolean isEqualToZero(BigDecimal value) {
        return safe(value).compareTo(BigDecimal.ZERO) == 0;
    }

    /**
     * 计算两个 BigDecimal 相加，返回安全结果
     *
     * @param value1 值1
     * @param value2 值2
     * @return 两值相加结果
     */
    public static BigDecimal add(BigDecimal value1, BigDecimal value2) {
        return safe(value1).add(safe(value2));
    }
}
