<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8"/>
    <title>A4 Page</title>
    <style>
        @page {
            size: A4;
            margin: 0;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 12pt;
            line-height: 1.6;
            margin: 0;
            padding: 0;
        }

        .page {
            box-sizing: border-box;
            display: block;
            flex-direction: column;
            height: 297mm;
            margin: 0mm auto;
            padding: 10mm;
            width: 210mm;
        }

        h1 {
            font-size: 24pt;
            margin-bottom: 1em;
            text-align: center;
        }

        .attachment-title {
            font-size: 12pt;
            margin-bottom: 1em;
            text-align: left;
        }

        .content {
            border: 1px solid black;
            /*flex-grow: 1;*/
            padding: 1em;
            height: 237mm;
            position: relative;
        }

        .signature {
            position: absolute;
            bottom: 1em;
            left: 1em;
            font-size: 10pt;
            font-style: italic;
            text-align: center;
            display: flex;
            align-items: center;
            /*gap: 0.5em;*/
        }

        .signature img {
            max-height: 200px;
            margin-bottom: 0.5em;
        }

        .signature label {
            position: absolute;
            bottom: 2em;
        }

        .images {
            display: inline-block;
            flex-wrap: wrap;
            justify-content: center;
            gap: 1em;
            margin-bottom: 1em;
        }

        .images img {
            margin-bottom: -7em;
        }

        .record {
            height: 20%;
            border-bottom: 1px solid black;
            position: relative;
        }

        .record-seal {
            position: absolute;
            display: inline;
            align-items: center;
        }

        .image {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1;
            margin-left: 10em;
        }

        .record-seal img {
            max-height: 200px;
            margin-bottom: 0.5em;
            right: 5em;
        }

        .record-bottom {
            position: absolute;
            bottom: 1em;
        }
        .record-bottom img {
            margin-left: 24em;
        }

        .record-tag {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 8mm;
            width: 20mm;
            border: 4px solid red;
        }

        .record-tag label {
            font-size: 14pt;
            font-weight: bolder;
            color: red;
        }

        .container {
            /*display: flex;*/
            justify-content: center;
            align-items: center;
            height: 46%; /* 可以根据需要调整高度 */
        }

        .container img {
            /*display: flex;*/
            max-width: 100%;
            max-height: 100%;
        }

        p {
            margin-bottom: 1em;
        }
    </style>
</head>
<body>
<div class="page">
    <h1 th:text="${title}"></h1>
    <div class="content">
        <p th:text="'项目部：' + ${data.yxProjectName + ''}">

        <div style="text-indent: 0.8cm">
            <label style="text-decoration: underline;" th:text="${inspectionDate}"></label>，
            对应于 <label style="text-decoration: underline;" th:text="${workDate}"></label> 完成的 <label style="text-decoration: underline;" th:text="${data.workName}"></label>，
            工作进行了检查， 发现存在以下问题，请认真组织整改，并将整改结果于 <label style="text-decoration: underline;" th:text="${data.yxTimeLimit}"></label>
            天内反馈。

        </div>
        <div th:utext="${problems}"></div>
<!--        [[${data}]]-->
        </p>

        <div class="signature">
            <label>[[${data.inspectionDepartment}]] 检查员：</label>
            <div style="margin-top: 1.5em">[[${signDate}]]</div>
<!--            <img th:src="@{/img/security.png}" alt="Signature" />-->
<!--            <img th:src="${seal}" height="200" alt="Signature" />-->
            <div style="margin-left: 3.5em">
                <img th:each="sign, signStat : ${signList}" th:src="${sign.signature}" height="50" th:alt="${sign.signatory}" />
            </div>
        </div>
    </div>
</div>
<div class="page" th:each="item, itemStat : ${items}">
    <div class="attachment-title" th:text="${attachmentTitle}"></div>
    <div class="content">
        <div class="container" th:each="image : ${item}">
            <img width="686" th:src="${'https://weixin.fyg.cn/fyschedule/image/' + image.content}" />
        </div>
    </div>
</div>
<div class="page" th:each="item, itemStat : ${records}">
    <h1 th:text="${recordTitle}"></h1>
    <div class="content">
        <div class="record" th:if="${itemStat.index == 0}">
            <div>方远新材料股份有限公司 [[${data.inspectionDepartment}]]:</div>
            <div>根据你们[[${inspectionDate}]]的《[[${title}]]》中提出的问题，现整改情况如下：</div>
        </div>
        <div class="record" th:each="record : ${item}">
            <div th:text="${record.creatorName}"></div>
            <div>
                <div th:text="${record.records}"></div>
                <div class="record-tag" th:if="${record.recordStatus == 1}"><label>整改通过</label></div>
            </div>
<!--            <img class="image" th:if="${record.recordStatus == 1}" th:src="${seal}" width="200" />-->
            <div class="record-bottom">
                <label th:text="${#dates.format(record.createDate, 'yyyy-MM-dd HH:mm:ss')}"></label>
                <img th:each="sign : ${record.signatures}" width="50" th:src="${sign.signature}" />
            </div>
        </div>
    </div>
</div>
<div class="page" th:each="item, itemStat : ${recordAttachments}">
    <div class="attachment-title" th:text="${recordAttachmentTitle}"></div>
    <div class="content">
        <div class="container" th:each="image : ${item}">
            <img width="686" th:src="${'https://weixin.fyg.cn/fyschedule/image/' + image.content}" />
        </div>
    </div>
</div>
</body>
</html>
