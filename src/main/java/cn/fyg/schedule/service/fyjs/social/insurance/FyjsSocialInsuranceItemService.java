package cn.fyg.schedule.service.fyjs.social.insurance;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.fyjs.social.insurance.FyjsSocialInsuranceItem;

public interface FyjsSocialInsuranceItemService {
    BaseResponse save(FyjsSocialInsuranceItem data);
    BaseResponse delete(Integer id);
    BaseResponse deleteByParentId(Integer id);
    BaseResponse findById(Integer id);
    BaseResponse findByParentId(Integer id);
}
