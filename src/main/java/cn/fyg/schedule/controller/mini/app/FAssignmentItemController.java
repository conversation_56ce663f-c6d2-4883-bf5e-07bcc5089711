package cn.fyg.schedule.controller.mini.app;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.FAssignmentItem;
import cn.fyg.schedule.pojo.dto.FAssignmentItemDto;
import cn.fyg.schedule.service.FAssignmentItemService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("assignmentitem")
public class FAssignmentItemController {
    private final FAssignmentItemService s;

    public FAssignmentItemController(FAssignmentItemService s) {
        this.s = s;
    }

    @PostMapping("save")
    public BaseResponse save(FAssignmentItemDto dto) {
        FAssignmentItem data = FAssignmentItem.initialize(dto);
        return s.save(data);
    }

    @PostMapping("delete")
    public BaseResponse delete(Integer id) {
        return s.delete(id);
    }

    @PostMapping("find")
    public BaseResponse findById(Integer id) {
        return s.findById(id);
    }
    @PostMapping("list")
    public BaseResponse findByAssignmentId(Integer id) {
        return s.findByAssignmentId(id);
    }
}
