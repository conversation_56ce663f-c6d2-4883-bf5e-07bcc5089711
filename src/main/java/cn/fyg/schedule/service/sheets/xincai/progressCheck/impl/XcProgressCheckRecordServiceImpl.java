package cn.fyg.schedule.service.sheets.xincai.progressCheck.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.xincai.progressCheck.XcProgressCheckRecordRepository;
import cn.fyg.schedule.dao.documnets.yuanxin.progressCheck.YxProgressCheckRecordRepository;
import cn.fyg.schedule.pojo.dto.project.yx.progressCheck.YxProgressCheckRecordDto;
import cn.fyg.schedule.pojo.project.xincai.progressCheck.XcProgressCheckRecord;
import cn.fyg.schedule.pojo.project.yuanXin.progressCheck.YxProgressCheckRecord;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.sheets.xincai.progressCheck.XcProgressCheckRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class XcProgressCheckRecordServiceImpl implements XcProgressCheckRecordService {
    private final XcProgressCheckRecordRepository r;

    public XcProgressCheckRecordServiceImpl(XcProgressCheckRecordRepository r) {
        this.r = r;
    }


    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(YxProgressCheckRecordDto dto) {
        XcProgressCheckRecord data = XcProgressCheckRecord.initialize(dto);
        BaseResponse response = BaseService.save(data, r, XcProgressCheckRecord.class);
        // 如果保存成功，预加载懒加载的集合，避免序列化时的LazyInitializationException
        if (response.getCode() == 0 && response.getResult() instanceof XcProgressCheckRecord) {
            XcProgressCheckRecord savedRecord = (XcProgressCheckRecord) response.getResult();
            preloadLazyCollections(savedRecord);
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse findById(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        XcProgressCheckRecord data = getDataById(id);
        if (data != null) {
            // 预加载懒加载的集合，避免LazyInitializationException
            preloadLazyCollections(data);
            response.setResult(data);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse findByParentId(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        List<XcProgressCheckRecord> data = getItems(id, 1);
        if (data != null) {
            // 预加载懒加载的集合，避免LazyInitializationException
            data.forEach(this::preloadLazyCollections);
            response.setResult(data);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public List<XcProgressCheckRecord> listRecordByParentId(Integer id, Integer mark) {
        List<XcProgressCheckRecord> data = getItems(id, mark);
        if (data != null) {
            // 预加载懒加载的集合，避免LazyInitializationException
            data.forEach(this::preloadLazyCollections);
        }
        return data;
    }

    private XcProgressCheckRecord getDataById(Integer id) {
        try {
            Optional<XcProgressCheckRecord> optional = r.findById(id);
            if (optional.isPresent()) {
                return optional.get();
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    /**
     *
     * @param id
     * @param mark 0 sort by asc; 1 sort by desc
     * @return List<YxProgressCheckRecord>
     */
    private List<XcProgressCheckRecord> getItems(Integer id, Integer mark) {
        try {
            List<XcProgressCheckRecord> items = mark == 0 ? r.findByParentIdOrderByCreateDateAsc(id) : r.findByParentIdOrderByCreateDateDesc(id);
            if (!items.isEmpty()) {
                return items;
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    /**
     * 预加载懒加载的集合，避免LazyInitializationException
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    private void preloadLazyCollections(XcProgressCheckRecord record) {
        if (record != null) {
            // 触发懒加载 - 我们只需要触发加载，不需要使用返回值
            if (record.getItems() != null) {
                record.getItems().size(); // 触发懒加载
            }
            if (record.getSignatures() != null) {
                record.getSignatures().size(); // 触发懒加载
            }
        }
    }
}
