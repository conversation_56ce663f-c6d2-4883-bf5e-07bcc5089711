package cn.fyg.schedule.pojo.statement.daily.report;

import cn.fyg.schedule.pojo.dto.statement.daily.report.FDailySellingReportDto;
import cn.hutool.core.date.DateUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

@Entity
@Setter
@Getter
@ToString
@Table(name = "fdailysellingreport")
public class FDailySellingReport {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Column(name = "fname_l2")
    private String fname; //项目名称
    private Integer sumnum; //总套数
    private Double zhz; //总货值
    private Integer ytnum; //已开盘套数
    private Integer jrysnum; //今日已售套数
    private Double jrxsje; //今日销售额
    private Double jrhk; //今日回款
    private Integer jnysnum; //今年已售套数
    private Double jnxsje; //今年销售额
    private Double jnhk; //今年回款
    private Integer ysnum; //总已售套数
    private Double fdealtotalamount; //总销售额
    private Double ljhk; //累计回款
    private Double zpqh; //整盘销售套数去化
    private Double ysks; //已售可收
    private Double wsks; //未售可收
    private String creator;
    @Column(name = "create_date")
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @Column(name = "update_date")
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;
    @Column(name = "data_date")
    @Temporal(TemporalType.DATE)
    private Date dataDate;

    public static FDailySellingReport initialize(FDailySellingReportDto dto) {
        FDailySellingReport data = new FDailySellingReport();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        data.setFname(dto.getFname_l2());
        data.setSumnum(dto.getSumnum());
        data.setZhz(dto.getZhz());
        data.setYtnum(dto.getYtnum());
        data.setJrysnum(dto.getJrysnum());
        data.setJrxsje(dto.getJrxsje());
        data.setJrhk(dto.getJrhk());
        data.setJnysnum(dto.getJnysnum());
        data.setJnxsje(dto.getJnxsje());
        data.setJnhk(dto.getJnhk());
        data.setYsnum(dto.getYsnum());
        data.setFdealtotalamount(dto.getFdealtotalamount());
        data.setLjhk(dto.getLjhk());
        data.setZpqh(dto.getZpqh());
        data.setYsks(dto.getYsks());
        data.setWsks(dto.getWsks());
        data.setCreator(dto.getCreator());
        if (dto.getCreateDate() != null) {
            data.setCreateDate(DateUtil.parse(dto.getCreateDate()));
        }
        data.setDataDate(DateUtil.date(dto.getDataDate()));
        return data;
    }
}
