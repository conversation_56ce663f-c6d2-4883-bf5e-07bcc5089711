package cn.fyg.schedule.service.project.management.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.projectManagement.FProjectStaffRepository;
import cn.fyg.schedule.pojo.dto.project.management.FProjectStaffDto;
import cn.fyg.schedule.pojo.project.management.FProjectStaff;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.project.management.FProjectStaffService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class FProjectStaffServiceImpl implements FProjectStaffService {
    private final FProjectStaffRepository r;

    public FProjectStaffServiceImpl(FProjectStaffRepository r) {
        this.r = r;
    }

    @Override
    public BaseResponse save(FProjectStaffDto dto) {
        FProjectStaff data = FProjectStaff.initialized(dto);
        return BaseService.save(data, r, FProjectStaff.class);
    }

    @Override
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findByParentIdAndIsSiteAdminAndIsActivated(Integer parentId, Integer isActivated, Integer isSiteAdmin) {
        BaseResponse response = BaseResponse.initialize();
        List<FProjectStaff> list = isActivated == null || isSiteAdmin == null ? getItemsByParentId(parentId) : getItemsByParentId(parentId, isActivated, isSiteAdmin);
        if (list != null) {
            response.setResult(list);
        } else {
            response.setCode(1);
            response.setMsg("查询出错");
        }
        return response;
    }

    private List<FProjectStaff> getItemsByParentId(Integer parentId, Integer isActivated, Integer isSiteAdmin) {
        try {
            List<FProjectStaff> items = r.findByParentIdAndIsActivatedAndIsSiteAdminOrderByCreateDateDesc(parentId, isActivated, isSiteAdmin);
            return items;
        } catch (Exception e) {
            log.error(e.getLocalizedMessage());
        }
        return null;
    }

    private List<FProjectStaff> getItemsByParentId(Integer parentId) {
        try {
            List<FProjectStaff> items = r.findByParentId(parentId);
            return items;
        } catch (Exception e) {
            log.error(e.getLocalizedMessage());
        }
        return null;
    }
}
