package cn.fyg.schedule.service.fTask.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.ftask.FTaskApproveRepository;
import cn.fyg.schedule.pojo.ftask.FTaskApprove;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.fTask.FTaskApproveService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;

@Service
@Slf4j
public class FTaskApproveServiceImpl implements FTaskApproveService {
    private final FTaskApproveRepository r;
    public FTaskApproveServiceImpl(FTaskApproveRepository r) {
        this.r = r;
    }
    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FTaskApprove data) {
        BaseResponse response = BaseService.save(data, r, FTaskApprove.class);
        // 如果保存成功，预加载懒加载的集合，避免序列化时的LazyInitializationException
        if (response.getCode() == 0 && response.getResult() instanceof FTaskApprove) {
            FTaskApprove savedApprove = (FTaskApprove) response.getResult();
            preloadLazyCollections(savedApprove);
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse listByTaskId(Integer taskId) {
        BaseResponse response = BaseResponse.initialize();
        try {
            List<FTaskApprove> list = r.findByTaskIdOrderByCreateDateDesc(taskId);
            // 预加载懒加载的items集合，避免LazyInitializationException
            list.forEach(this::preloadLazyCollections);
            response.setResult(list);
        } catch (Exception e) {
            response.setCode(1);
            response.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return response;
    }

    /**
     * 预加载懒加载的集合，避免LazyInitializationException
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    private void preloadLazyCollections(FTaskApprove approve) {
        if (approve != null) {
            // 触发懒加载
            if (approve.getItems() != null) {
                approve.getItems().size();
            }
        }
    }
}
