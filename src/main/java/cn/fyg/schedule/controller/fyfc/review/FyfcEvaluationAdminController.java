package cn.fyg.schedule.controller.fyfc.review;

import cn.fyg.schedule.pojo.dto.fyfc.review.*;
import cn.fyg.schedule.service.fyfc.review.IFyfcEvaluationAdminService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * FYFC 评价管理员控制器
 * 对应 admin/dashboard 页面的 API
 */
@Slf4j
@RestController
@RequestMapping("/api/fyfc/evaluation/admin")
@RequiredArgsConstructor
public class FyfcEvaluationAdminController {

    private final IFyfcEvaluationAdminService adminService;

    /**
     * 管理员分页查询评价数据
     */
    @PostMapping("/search")
    public FyfcApiResponseDto<FyfcPaginatedResponseDto<FyfcEvaluationDto>> searchEvaluations(
            @RequestBody FyfcEvaluationQueryDto queryDto) {
        log.info("管理员查询评价数据: {}", queryDto);
        return adminService.searchEvaluations(queryDto);
    }

    /**
     * 管理员更新评价状态
     */
    @PutMapping("/status/{id}")
    public FyfcApiResponseDto<Boolean> updateEvaluationStatus(
            @PathVariable Integer id,
            @RequestParam String status,
            @RequestParam String operator) {
        log.info("管理员更新评价状态: id={}, status={}, operator={}", id, status, operator);
        return adminService.updateEvaluationStatus(id, status, operator);
    }

    /**
     * 获取评价统计数据
     */
    @GetMapping("/stats")
    public FyfcApiResponseDto<FyfcEvaluationStatsDto> getEvaluationStats() {
        log.info("获取评价统计数据");
        return adminService.getEvaluationStats();
    }

    /**
     * 获取部门统计数据
     */
    @GetMapping("/stats/departments")
    public FyfcApiResponseDto<List<FyfcEvaluationStatsDto.DepartmentStatsDto>> getDepartmentStats() {
        log.info("获取部门统计数据");
        return adminService.getDepartmentStats();
    }

    /**
     * 批量导出评价数据
     */
    @PostMapping("/export")
    public FyfcApiResponseDto<List<FyfcEvaluationDto>> exportEvaluations(
            @RequestBody FyfcEvaluationQueryDto queryDto) {
        log.info("导出评价数据: {}", queryDto);
        return adminService.exportEvaluations(queryDto);
    }

    /**
     * 批量删除评价
     */
    @DeleteMapping("/batch")
    public FyfcApiResponseDto<Integer> batchDeleteEvaluations(
            @RequestBody List<Integer> evaluationIds,
            @RequestParam String operator) {
        log.info("批量删除评价: ids={}, operator={}", evaluationIds, operator);
        return adminService.batchDeleteEvaluations(evaluationIds, operator);
    }

    /**
     * 获取系统配置选项
     */
    @GetMapping("/options")
    public FyfcApiResponseDto<Object> getSystemOptions() {
        log.info("获取系统配置选项");
        return adminService.getSystemOptions();
    }

    /**
     * 重置评价状态
     */
    @PutMapping("/reset/{id}")
    public FyfcApiResponseDto<Boolean> resetEvaluationStatus(
            @PathVariable Integer id,
            @RequestParam String operator) {
        log.info("重置评价状态: id={}, operator={}", id, operator);
        return adminService.resetEvaluationStatus(id, operator);
    }

    /**
     * 获取评价详情（管理员视图）
     */
    @GetMapping("/{id}")
    public FyfcApiResponseDto<FyfcEvaluationDto> getEvaluationById(@PathVariable Integer id) {
        log.info("管理员获取评价详情: id={}", id);
        // 可以调用 commonService 或在 adminService 中添加对应方法
        // 这里暂时返回基础信息，后续可以扩展为管理员专用的详情视图
        return FyfcApiResponseDto.success(null, "功能开发中");
    }

    /**
     * 管理员强制完成评价
     */
    @PutMapping("/force-complete/{id}")
    public FyfcApiResponseDto<Boolean> forceCompleteEvaluation(
            @PathVariable Integer id,
            @RequestParam String operator,
            @RequestParam(required = false) String reason) {
        log.info("管理员强制完成评价: id={}, operator={}, reason={}", id, operator, reason);
        // 直接设置状态为 COMPLETED
        return adminService.updateEvaluationStatus(id, "COMPLETED", operator);
    }

    /**
     * 获取系统运行状态
     */
    @GetMapping("/system/status")
    public FyfcApiResponseDto<Object> getSystemStatus() {
        log.info("获取系统运行状态");
        // TODO: 实现系统状态检查
        return FyfcApiResponseDto.success(null, "系统运行正常");
    }

    /**
     * 数据备份
     */
    @PostMapping("/backup")
    public FyfcApiResponseDto<Object> backupData(@RequestParam String operator) {
        log.info("数据备份: operator={}", operator);
        // TODO: 实现数据备份功能
        return FyfcApiResponseDto.success(null, "备份功能开发中");
    }

    /**
     * 数据清理
     */
    @PostMapping("/cleanup")
    public FyfcApiResponseDto<Object> cleanupData(
            @RequestParam String operator,
            @RequestParam Integer daysToKeep) {
        log.info("数据清理: operator={}, daysToKeep={}", operator, daysToKeep);
        // TODO: 实现数据清理功能
        return FyfcApiResponseDto.success(null, "清理功能开发中");
    }
}
