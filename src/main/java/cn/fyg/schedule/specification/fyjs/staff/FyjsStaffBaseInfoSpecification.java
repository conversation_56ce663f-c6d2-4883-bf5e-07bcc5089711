package cn.fyg.schedule.specification.fyjs.staff;

import cn.fyg.schedule.pojo.fyjs.staff.FyjsStaffBaseInfo;
import cn.fyg.schedule.utils.SpecificationUtil;
import org.springframework.data.jpa.domain.Specification;

import java.util.Date;

public class FyjsStaffBaseInfoSpecification {
    public static Specification<FyjsStaffBaseInfo> ifContain(String column, String expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.like(root.get(column), SpecificationUtil.contains(expression)));
    }

    public static Specification<FyjsStaffBaseInfo> ifDateEq(String column, Date expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }

    public static Specification<FyjsStaffBaseInfo> ifDateBetween(String column, Date startDate, Date endDate) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.between(root.get(column), startDate, endDate));
    }

    public static Specification<FyjsStaffBaseInfo> ifDateOverThan(String column, Date date) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.greaterThanOrEqualTo(root.get(column), date));
    }

    public static Specification<FyjsStaffBaseInfo> ifDateLessThan(String column, Date date) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.lessThanOrEqualTo(root.get(column), date));
    }

    public static Specification<FyjsStaffBaseInfo> ifEq(String column, String expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }

    public static Specification<FyjsStaffBaseInfo> ifEq(String column, Integer expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }

    public static Specification<FyjsStaffBaseInfo> ifNumberBetween(String column, Long min, Long max) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.between(root.get(column), min, max));
    }

    public static Specification<FyjsStaffBaseInfo> ifNumberOverThan(String column, Long num) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.greaterThanOrEqualTo(root.get(column), num));
    }

    public static Specification<FyjsStaffBaseInfo> ifNumberLessThan(String column, Long num) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.lessThanOrEqualTo(root.get(column), num));
    }

    public static Specification<FyjsStaffBaseInfo> ifNumberBetween(String column, Integer min, Integer max) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.between(root.get(column), min, max));
    }

    public static Specification<FyjsStaffBaseInfo> ifNumberOverThan(String column, Integer num) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.greaterThanOrEqualTo(root.get(column), num));
    }

    public static Specification<FyjsStaffBaseInfo> ifNumberLessThan(String column, Integer num) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.lessThanOrEqualTo(root.get(column), num));
    }
}
