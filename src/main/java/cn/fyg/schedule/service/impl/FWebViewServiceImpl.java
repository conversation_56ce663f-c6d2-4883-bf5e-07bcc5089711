package cn.fyg.schedule.service.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.FWebViewRepository;
import cn.fyg.schedule.pojo.FWebView;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.FWebViewService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.Optional;

@Service
@Slf4j
public class FWebViewServiceImpl implements FWebViewService {
    private final FWebViewRepository r;
    public FWebViewServiceImpl(FWebViewRepository r) {
        this.r = r;
    }
    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FWebView data) {
        return BaseService.save(data, r, FWebView.class);
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        try {
            Optional<FWebView> data = r.findById(id);
            if (data.isPresent()) {
                response.setResult(data.get());
            } else {
                response.setCode(1);
                response.setMsg("没找到数据");
            }
        } catch (Exception e) {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }
}
