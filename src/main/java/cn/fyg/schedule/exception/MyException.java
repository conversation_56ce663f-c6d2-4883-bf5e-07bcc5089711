package cn.fyg.schedule.exception;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class MyException extends Exception {
    private Integer code;
    private String errMsg;

    public MyException(String errMsg) {
        super(errMsg);
    }

    public MyException(Integer code, String errMsg) {
        this.code = code;
        this.errMsg = errMsg;
    }

    @Override
    public String toString() {
        return "{" +
                "code=" + code + ", errMsg=" + errMsg +
                "}";
    }
}
