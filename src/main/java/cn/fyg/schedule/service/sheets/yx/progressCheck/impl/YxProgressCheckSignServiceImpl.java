package cn.fyg.schedule.service.sheets.yx.progressCheck.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.yuanxin.progressCheck.YxProgressCheckSignRepository;
import cn.fyg.schedule.pojo.project.yuanXin.progressCheck.YxProgressCheckSign;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.sheets.yx.progressCheck.YxProgressCheckSignService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class YxProgressCheckSignServiceImpl implements YxProgressCheckSignService {
    private final YxProgressCheckSignRepository r;

    public YxProgressCheckSignServiceImpl(YxProgressCheckSignRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(YxProgressCheckSign data) {
        return BaseService.save(data, r, YxProgressCheckSign.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findByParentId(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        List<YxProgressCheckSign> data = getItems(id);
        if (data != null) {
            response.setResult(data);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        YxProgressCheckSign data = getDataById(id);
        if (data != null) {
            response.setResult(data);
        } else {
            response.setCode(1);
            response.setMsg("查询出错");
        }
        return response;
    }

    private YxProgressCheckSign getDataById(Integer id) {
        try {
            Optional<YxProgressCheckSign> optional = r.findById(id);
            if (optional.isPresent()) {
                return optional.get();
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    private List<YxProgressCheckSign> getItems(Integer id) {
        try {
            List<YxProgressCheckSign> items = r.findByParentIdOrderByCreateDateDesc(id);
            if (!items.isEmpty()) {
                return items;
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }
}
