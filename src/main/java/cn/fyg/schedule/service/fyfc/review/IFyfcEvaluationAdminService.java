package cn.fyg.schedule.service.fyfc.review;

import cn.fyg.schedule.pojo.dto.fyfc.review.*;

import java.util.List;

/**
 * FYFC 评价管理员服务接口
 * 对应 admin/dashboard 页面的业务逻辑
 */
public interface IFyfcEvaluationAdminService {

    /**
     * 管理员分页查询评价数据
     * @param queryDto 查询条件
     * @return 统一API响应格式的分页结果
     */
    FyfcApiResponseDto<FyfcPaginatedResponseDto<FyfcEvaluationDto>> searchEvaluations(FyfcEvaluationQueryDto queryDto);

    /**
     * 管理员更新评价状态
     * @param evaluationId 评价ID
     * @param status 新状态
     * @param operatorName 操作人
     * @return 统一API响应格式
     */
    FyfcApiResponseDto<Boolean> updateEvaluationStatus(Integer evaluationId, String status, String operatorName);

    /**
     * 获取评价统计数据（管理员仪表板）
     * @return 统一API响应格式的统计数据
     */
    FyfcApiResponseDto<FyfcEvaluationStatsDto> getEvaluationStats();

    /**
     * 获取部门统计数据
     * @return 统一API响应格式的部门统计
     */
    FyfcApiResponseDto<List<FyfcEvaluationStatsDto.DepartmentStatsDto>> getDepartmentStats();

    /**
     * 批量导出评价数据
     * @param queryDto 查询条件
     * @return 统一API响应格式的导出数据
     */
    FyfcApiResponseDto<List<FyfcEvaluationDto>> exportEvaluations(FyfcEvaluationQueryDto queryDto);

    /**
     * 批量删除评价
     * @param evaluationIds 评价ID列表
     * @param operatorName 操作人
     * @return 统一API响应格式的处理结果
     */
    FyfcApiResponseDto<Integer> batchDeleteEvaluations(List<Integer> evaluationIds, String operatorName);

    /**
     * 获取系统配置选项
     * @return 统一API响应格式的配置选项
     */
    FyfcApiResponseDto<Object> getSystemOptions();

    /**
     * 重置评价状态
     * @param evaluationId 评价ID
     * @param operatorName 操作人
     * @return 统一API响应格式
     */
    FyfcApiResponseDto<Boolean> resetEvaluationStatus(Integer evaluationId, String operatorName);
}
