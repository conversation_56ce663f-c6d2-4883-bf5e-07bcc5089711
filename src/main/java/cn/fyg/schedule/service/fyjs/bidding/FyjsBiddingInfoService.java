package cn.fyg.schedule.service.fyjs.bidding;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.fyjs.bidding.FyjsBiddingInfoDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;

public interface FyjsBiddingInfoService {
    BaseResponse save(FyjsBiddingInfoDto dto);
    BaseResponse filter(Pagination pagination, FyjsBiddingInfoDto dto);
    BaseResponse findById(Integer id);
}
