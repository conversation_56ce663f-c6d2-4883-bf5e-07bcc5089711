package cn.fyg.schedule.pojo.fyapp;

import cn.fyg.schedule.pojo.dto.fyapp.FAppRegisterDto;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.util.Date;

@Entity
@Setter
@Getter
@ToString
@Table(name = "f_app_register")
public class FAppRegister {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    private Integer groupId;
    private Integer appId;
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    private Integer appSort;

    public static FAppRegister initialize(FAppRegisterDto dto) {
        FAppRegister data = new FAppRegister();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        data.setGroupId(dto.getGroupId());
        data.setAppId(dto.getAppId());
        data.setAppSort(dto.getAppSort());
        return data;
    }
}
