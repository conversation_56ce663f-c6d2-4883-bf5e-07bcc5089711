package cn.fyg.schedule.controller.fyjs;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.project.fyjs.sealManagement.FJsSealManagementDto;
import cn.fyg.schedule.pojo.project.fyjs.sealManagement.FJsSealAttachment;
import cn.fyg.schedule.pojo.project.fyjs.sealManagement.FJsSealManagement;
import cn.fyg.schedule.service.fyjs.seal.SealAttachmentService;
import cn.fyg.schedule.service.fyjs.seal.SealService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("js/seal")
public class FJsSealManagementController {
    private final SealService sealService;
    private final SealAttachmentService sealAttachmentService;

    public FJsSealManagementController(SealService sealService, SealAttachmentService sealAttachmentService) {
        this.sealService = sealService;
        this.sealAttachmentService = sealAttachmentService;
    }

    @PostMapping("save")
    public BaseResponse saveSeal(FJsSealManagementDto dto) {
        FJsSealManagement data = FJsSealManagement.initialize(dto);
        return sealService.save(data);
    }

    @PostMapping("delete")
    public BaseResponse deleteSeal(Integer id) {
        return sealService.delete(id);
    }

    @PostMapping("filter")
    public BaseResponse sealFilter(FJsSealManagementDto dto, Integer currentPage,
                                   Integer pageSize, String order, String columnKey) {
        return sealService.filter(dto, currentPage, pageSize, order, columnKey);
    }

    @PostMapping("find/id")
    public BaseResponse findById(Integer id) {
        return sealService.findById(id);
    }

    @PostMapping("save/attachment")
    public BaseResponse saveAttachment(FJsSealAttachment data) {
        return sealAttachmentService.save(data);
    }

    @PostMapping("delete/attachment")
    public BaseResponse deleteAttachment(Integer id) {
        return sealAttachmentService.delete(id);
    }

    @PostMapping("find/attachment")
    public BaseResponse findAttachment(Integer id) {
        return sealAttachmentService.findByParentId(id);
    }
}
