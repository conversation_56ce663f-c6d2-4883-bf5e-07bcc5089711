package cn.fyg.schedule.service.sheets.xincai.progressCheck;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.project.xincai.progressCheck.XcProgressCheckRecordSign;
public interface XcProgressCheckRecordSignService {
    BaseResponse save(XcProgressCheckRecordSign data);
    BaseResponse delete(Integer id);
    BaseResponse findByParentId(Integer id);
    BaseResponse deleteByParentId(Integer id);
}
