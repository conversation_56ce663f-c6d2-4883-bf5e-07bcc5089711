package cn.fyg.schedule.service.sheets.xincai.progressCheck.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.xincai.progressCheck.XcProgressCheckRecordItemRepository;
import cn.fyg.schedule.dao.documnets.yuanxin.progressCheck.YxProgressCheckRecordItemRepository;
import cn.fyg.schedule.pojo.project.xincai.progressCheck.XcProgressCheckRecordItem;
import cn.fyg.schedule.pojo.project.yuanXin.progressCheck.YxProgressCheckRecordItem;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.sheets.xincai.progressCheck.XcProgressCheckRecordItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class XcProgressCheckRecordItemServiceImpl implements XcProgressCheckRecordItemService {
    private final XcProgressCheckRecordItemRepository r;

    public XcProgressCheckRecordItemServiceImpl(XcProgressCheckRecordItemRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(XcProgressCheckRecordItem data) {
        return BaseService.save(data, r, XcProgressCheckRecordItem.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        XcProgressCheckRecordItem data = getDataById(id);
        if (data != null) {
            response.setResult(data);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    private XcProgressCheckRecordItem getDataById(Integer id) {
        try {
            Optional<XcProgressCheckRecordItem> optional = r.findById(id);
            if (optional.isPresent()) {
                return optional.get();
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    @Override
    public BaseResponse findByParentId(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        List<XcProgressCheckRecordItem> data = getItems(id);
        if (data != null) {
            response.setResult(data);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    private List<XcProgressCheckRecordItem> getItems(Integer id) {
        try {
            List<XcProgressCheckRecordItem> items = r.findByParentIdOrderByCreateDateDesc(id);
            if (!items.isEmpty()) {
                return items;
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }
}
