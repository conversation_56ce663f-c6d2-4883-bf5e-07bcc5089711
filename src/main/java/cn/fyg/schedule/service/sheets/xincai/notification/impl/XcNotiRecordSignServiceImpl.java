package cn.fyg.schedule.service.sheets.xincai.notification.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.xincai.notification.XcNotiRecordSignRepository;
import cn.fyg.schedule.pojo.project.xincai.noti.XcNotiRecordSign;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.sheets.xincai.notification.XcNotiRecordSignService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;

@Service
@Slf4j
public class XcNotiRecordSignServiceImpl implements XcNotiRecordSignService {
    private final XcNotiRecordSignRepository r;

    public XcNotiRecordSignServiceImpl(XcNotiRecordSignRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(XcNotiRecordSign data) {
        return BaseService.save(data, r, XcNotiRecordSign.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findByParentId(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        List<XcNotiRecordSign> list = getItems(id);
        if (list != null) {
            baseResponse.setResult(list);
        } else {
            baseResponse.setCode(1);
            baseResponse.setMsg("查询失败");
        }
        return baseResponse;
    }

    private List<XcNotiRecordSign> getItems(Integer id) {
        try {
            List<XcNotiRecordSign> items = r.findByParentIdOrderByCreateDateDesc(id);
            if (!items.isEmpty()) {
                return items;
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse deleteByParentId(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            r.deleteByParentId(id);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg("发生错误:" + e.getMessage());
        }
        return baseResponse;
    }
}
