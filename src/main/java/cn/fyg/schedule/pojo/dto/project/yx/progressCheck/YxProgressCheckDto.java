package cn.fyg.schedule.pojo.dto.project.yx.progressCheck;

import lombok.Data;

@Data
public class YxProgressCheckDto {
    private Integer id;
    private String creator;
    private String creatorName;
    private String yxProjectName;
    private String yxEventName;
    private Integer yxTimeLimit;
    private String yxTimeLimitQuery;
    private String yxProblemDescription;
    private Long inspectionDate;
    private String inspectionDateQuery;
    private Long workDate;
    private String workDateQuery;
    private String workName;
    private String period;
    private Long beginDate;
    private Long endDate;
    private Long createDate;
    private Integer needSign;
    private String lastEditor;
    private Integer dataStatus;
    private Integer eventStatus;
    private String organization;
    private String inspectionDepartment;
}
