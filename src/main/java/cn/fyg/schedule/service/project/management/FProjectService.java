package cn.fyg.schedule.service.project.management;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.project.management.FProjectDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;

public interface FProjectService {
    BaseResponse save(FProjectDto dto);
    BaseResponse findById(Integer id);
    BaseResponse filter(FProjectDto dto, Pagination pagination);
    BaseResponse filter(FProjectDto dto);
    BaseResponse delete(Integer id);
    BaseResponse findOneByName(String name);
}
