package cn.fyg.schedule.pojo.fyfc.review;

import cn.fyg.schedule.enums.fyfc.review.EvaluatorType;
import cn.fyg.schedule.enums.fyfc.review.EvaluatorTypeConverter;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 评价得分表实体
 * 对应数据库表: fyfc_evaluation_scores
 */
@Entity
@Data
@Getter
@Setter
@ToString
@Table(name = "fyfc_evaluation_scores")
public class FyfcEvaluationScore {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /**
     * 评价主表ID
     */
    @Column(name = "evaluation_id", nullable = false)
    private Integer evaluationId;

    /**
     * 评价人姓名
     */
    @Column(name = "evaluator", length = 50)
    private String evaluator;

    /**
     * 评价人角色类型
     */
    @Convert(converter = EvaluatorTypeConverter.class)
    @Column(name = "type", nullable = false)
    private EvaluatorType type;

    /**
     * 工作业绩得分(0-60分)
     */
    @Column(name = "performance_score", precision = 5, scale = 2)
    private BigDecimal performanceScore;

    /**
     * 工作态度得分(0-10分)
     */
    @Column(name = "attitude_score", precision = 4, scale = 2)
    private BigDecimal attitudeScore;

    /**
     * 工作能力得分(0-10分)
     */
    @Column(name = "ability_score", precision = 4, scale = 2)
    private BigDecimal abilityScore;

    /**
     * 个人成长得分(0-10分)
     */
    @Column(name = "growth_score", precision = 4, scale = 2)
    private BigDecimal growthScore;

    /**
     * 小计得分
     */
    @Column(name = "score", precision = 6, scale = 2)
    private BigDecimal score;

    /**
     * 评价人签名
     */
    @Column(name = "signature", length = 100)
    private String signature;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at")
    private Date updatedAt;

    /**
     * 关联的评价主表
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "evaluation_id", insertable = false, updatable = false)
    private FyfcEvaluation evaluation;
}
