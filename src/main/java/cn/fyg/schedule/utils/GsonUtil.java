package cn.fyg.schedule.utils;

import com.google.gson.*;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/*
 * 封装的GSON解析工具类，提供泛型参数
 */
public class GsonUtil {
	// 将Json数据解析成相应的映射对象
	public static <T> T parseJsonWithGson(String jsonData, Class<T> type) {
		Gson gson = new Gson();
		T result = gson.fromJson(jsonData, type);
		return result;
	}

	public static <T> T parseJsonWithGsonBuilder(String jsonData, Class<T> type) {
		Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
		T result = gson.fromJson(jsonData, type);
		return result;
	}

	// 将Json数组解析成相应的映射对象列表
	public static <T> List<T> parseJsonArrayWithGson(String jsonData,
			Class<T> type) {
		Gson gson = new Gson();
		List<T> result = gson.fromJson(jsonData, new TypeToken<List<T>>() {

		}.getType());
		return result;
	}

	private static final Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
//	private static final Gson gson = new GsonBuilder().registerTypeAdapter(Date.class, new GsonUTCDateAdapter()).create();

	/**
	 * 转成Json字符串
	 */
	public static String toJson(Object object) {
		return gson.toJson(object);
	}

	/**
	 * Json转Java对象
	 */
	public static <T> T fromJson(String json, Class<T> clz) {

		return gson.fromJson(json, clz);
	}

	/**
	 * Json转List集合
	 */
	public static <T> List<T> jsonToList(String json, Class<T> clz) {
		Type type = new TypeToken<List<T>>() {
		}.getType();
		return gson.fromJson(json, type);
	}

	/**
	 * Json转List集合,遇到解析不了的，就使用这个
	 */
	public static <T> List<T> fromJsonList(String json, Class<T> cls) {
		List<T> mList = new ArrayList<T>();
		JsonArray array = JsonParser.parseString(json).getAsJsonArray();
		Gson mGson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
		for (final JsonElement elem : array) {
			mList.add(mGson.fromJson(elem, cls));
		}
		return mList;
	}

	/**
	 * Json转换成Map的List集合对象
	 */
	public static <T> List<Map<String, T>> toListMap(String json, Class<T> clz) {
		Type type = new TypeToken<List<Map<String, T>>>() {
		}.getType();
		return gson.fromJson(json, type);
	}

	/**
	 * Json转Map对象
	 */
	public static <T> Map<String, T> toMap(String json, Class<T> clz) {
		Type type = new TypeToken<Map<String, T>>() {
		}.getType();
		return gson.fromJson(json, type);
	}

	public static class GsonUTCDateAdapter implements JsonSerializer<Date>,JsonDeserializer<Date> {
		private final DateFormat dateFormat;
		public GsonUTCDateAdapter() {
			dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINA); //This is the format I need 
			dateFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
			//This is the key line which converts the date to UTC which cannot be accessed with the default serializer
		}

		@Override
		public synchronized Date deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context)
				throws JsonParseException {
			// TODO Auto-generated method stub
			try {
				return dateFormat.parse(json.getAsString());
			} catch (ParseException e) {
				throw new JsonParseException(e);
			}
		}

		@Override
		public synchronized JsonElement serialize(Date src, Type typeOfSrc, JsonSerializationContext context) {
			// TODO Auto-generated method stub
			return new JsonPrimitive(dateFormat.format(src));
		} 
	}
}
