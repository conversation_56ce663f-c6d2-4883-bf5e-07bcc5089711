package cn.fyg.schedule.controller.fyfc.review;

import cn.fyg.schedule.pojo.dto.fyfc.review.FyfcApiResponseDto;
import cn.fyg.schedule.service.fyfc.review.IFyfcOssService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * FYFC OSS 测试控制器
 * 用于测试 OSS 服务功能
 */
@Slf4j
@RestController
@RequestMapping("/api/fyfc/oss/test")
public class FyfcOssTestController {

    private final IFyfcOssService ossService;

    public FyfcOssTestController(IFyfcOssService ossService) {
        this.ossService = ossService;
    }

    /**
     * 测试文件上传
     */
    @PostMapping("/upload")
    public FyfcApiResponseDto<?> testUpload(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "evaluationId", defaultValue = "1") Integer evaluationId,
            @RequestParam(value = "uploadBy", defaultValue = "testUser") String uploadBy) {
        
        log.info("测试文件上传: fileName={}, size={}", file.getOriginalFilename(), file.getSize());
        
        return ossService.uploadFile(file, evaluationId, uploadBy);
    }

    /**
     * 测试获取文件URL
     */
    @GetMapping("/url")
    public FyfcApiResponseDto<String> testGetUrl(
            @RequestParam("fileKey") String fileKey,
            @RequestParam(value = "expireSeconds", defaultValue = "3600") Integer expireSeconds) {
        
        log.info("测试获取文件URL: fileKey={}", fileKey);
        
        return ossService.getFileUrl(fileKey, expireSeconds);
    }

    /**
     * 测试获取评价附件
     */
    @GetMapping("/attachments/{evaluationId}")
    public FyfcApiResponseDto<?> testGetAttachments(@PathVariable Integer evaluationId) {
        log.info("测试获取评价附件: evaluationId={}", evaluationId);
        
        return ossService.getEvaluationAttachments(evaluationId);
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public FyfcApiResponseDto<String> healthCheck() {
        return FyfcApiResponseDto.success("OSS服务运行正常", "OSS Service is healthy");
    }
}
