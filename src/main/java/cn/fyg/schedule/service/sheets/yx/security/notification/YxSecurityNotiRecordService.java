package cn.fyg.schedule.service.sheets.yx.security.notification;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.project.yx.security.notification.YxSecurityNotiRecordDto;
import cn.fyg.schedule.pojo.project.yuanXin.security.notification.YxSecurityNotiRecord;

import java.util.List;

public interface YxSecurityNotiRecordService {
    BaseResponse save(YxSecurityNotiRecordDto dto);
    BaseResponse delete(Integer id);
    BaseResponse findById(Integer id);
    BaseResponse findByParentId(Integer id);
    List<YxSecurityNotiRecord> listRecordByParentId(Integer id, Integer mark);
    BaseResponse ifApproved(Integer id, Integer recordStatus);
    List<Integer> listParentIdByCreatorPhone(String creatorPhone);
}
