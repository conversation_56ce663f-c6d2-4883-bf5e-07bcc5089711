package cn.fyg.schedule.controller.fyg.accounting;

import cn.fyg.schedule.pojo.dto.fyg.accounting.equity.EquityDistributionDTO;
import cn.fyg.schedule.reponse.BaseResponse;
import cn.fyg.schedule.service.fyg.accounting.FygEquityDistributionService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/fyg/accounting/equity-distribution")
public class FygEquityDistributionController {
    private final FygEquityDistributionService service;
    public FygEquityDistributionController(FygEquityDistributionService service) {
        this.service = service;
    }

    @PutMapping("save")
    public BaseResponse<EquityDistributionDTO> save(EquityDistributionDTO dto) {
        if (dto.getId() == null) {
            return service.save(dto);
        } else {
            return service.save(dto, dto.getId());
        }
    }
    @PostMapping("list")
    public BaseResponse<List<EquityDistributionDTO>> list(Integer dataStatus, Integer projectId) {
        return service.list(dataStatus, projectId);
    }

    @PostMapping("find/equity")
    public BaseResponse<EquityDistributionDTO> findProjectCounterparty(Integer projectId, String counterpartyAccountName, String equityType) {
        return service.findByProjectAndCounterparty(projectId, counterpartyAccountName, equityType);
    }

    @PutMapping("update-status")
    public BaseResponse<EquityDistributionDTO> updateStatus(Integer id, Integer dataStatus, String updatedBy) {
        return service.setDataStatus(id, dataStatus, updatedBy);
    }

}
