package cn.fyg.schedule.pojo.project.yinLong;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

@Entity
@Setter
@Getter
@ToString
@Table(name = "f_yinlong_signature")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class FYinLongSignature {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Column(name = "seal_id")
    private Integer sealId;
    @Column(name = "signature")
    private String signature;
    @Column(name = "signatory")
    private String signatory;
    @Column(name = "type")
    private String type;
    @Column(name = "creator")
    private String creator;
    private String comments;
    @Column(name = "create_date")
    @CreationTimestamp
    @Temporal(TemporalType.DATE)
    private Date createDate;
    @Column(name = "update_date")
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;
}
