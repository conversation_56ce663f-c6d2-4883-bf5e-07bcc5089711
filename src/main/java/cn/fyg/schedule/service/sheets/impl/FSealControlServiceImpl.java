package cn.fyg.schedule.service.sheets.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.yuanxin.FSealControlItemRepository;
import cn.fyg.schedule.dao.documnets.yuanxin.FSealControlRepository;
import cn.fyg.schedule.dao.documnets.yuanxin.FSealControlSignatureRepository;
import cn.fyg.schedule.pojo.project.yuanXin.FSealControl;
import cn.fyg.schedule.pojo.project.yuanXin.FSealControlItem;
import cn.fyg.schedule.pojo.project.yuanXin.FSealControlSignature;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.sheets.FSealControlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class FSealControlServiceImpl implements FSealControlService {
    private final FSealControlRepository r;

    private final FSealControlItemRepository itemR;

    private final FSealControlSignatureRepository signatureR;

    public FSealControlServiceImpl(FSealControlRepository r, FSealControlItemRepository itemR, FSealControlSignatureRepository signatureR) {
        this.r = r;
        this.itemR = itemR;
        this.signatureR = signatureR;
    }

    @Override
    public BaseResponse save(FSealControl data) {
        return BaseService.save(data, r, FSealControl.class);
    }

    @Override
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Optional<FSealControl> result = r.findById(id);
//            result.ifPresent(baseResponse::setResult);
            if (result.isPresent()) {
                List<FSealControlItem> items = itemR.findBySealIdOrderByCreateDate(id);
                List<FSealControlSignature> signatureList = signatureR.findBySealIdOrderByCreateDate(id);
                FSealControl data = result.get();
                data.setItems(items);
                data.setSignatureList(signatureList);
                baseResponse.setResult(data);
            }
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse findAll(Integer page, Integer size) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Sort sort = Sort.by(Sort.Direction.DESC, "createDate");
            Pageable pageable = PageRequest.of(page - 1, size, sort);
            Page<FSealControl> data = r.findAllByIdIsNotNull(pageable);
            baseResponse.setTotalCount(data.getTotalElements());
            baseResponse.setResult(data.getContent());
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse findAll() {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FSealControl> data = r.findAllByOrderByNumberDesc();
            baseResponse.setResult(data);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse getDepartmentNameHistoryList() {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<String> data = r.getDepartmentNameHistoryList();
            baseResponse.setResult(data);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse getSealTypeHistoryList() {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<String> data = r.getSealTypeHistoryList();
            baseResponse.setResult(data);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse saveItem(FSealControlItem data) {
        return BaseService.save(data, itemR, FSealControlItem.class);
    }

    @Override
    public BaseResponse deleteItem(Integer id) {
       return BaseService.delete(id, itemR);
    }

    @Override
    public BaseResponse findItems(Integer sealId) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FSealControlItem> data = itemR.findBySealIdOrderByCreateDate(sealId);
            baseResponse.setResult(data);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse saveSignature(FSealControlSignature data) {
        return BaseService.save(data, signatureR, FSealControlSignature.class);
    }

    @Override
    public BaseResponse deleteSignature(Integer id) {
        return BaseService.delete(id, signatureR);
    }

    @Override
    public BaseResponse findSignatureList(Integer sealId) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FSealControlSignature> data = signatureR.findBySealIdOrderByCreateDate(sealId);
            baseResponse.setResult(data);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }
}
