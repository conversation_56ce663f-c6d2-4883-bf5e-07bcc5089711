package cn.fyg.schedule.service.fg.approval.document;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.fg.approval.document.FgDocumentApprovalItem;

public interface FgDocumentApprovalItemService {
    BaseResponse save(FgDocumentApprovalItem data);
    FgDocumentApprovalItem saveItem(FgDocumentApprovalItem data) throws MyException;
    BaseResponse findTopByParentId(Integer id);
}
