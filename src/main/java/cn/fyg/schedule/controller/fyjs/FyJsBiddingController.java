package cn.fyg.schedule.controller.fyjs;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.fyjs.bidding.FyjsBiddingInfoDto;
import cn.fyg.schedule.pojo.dto.fyjs.bidding.FyjsBiddingOpenDto;
import cn.fyg.schedule.pojo.dto.fyjs.bidding.FyjsBiddingTendererDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.pojo.fyjs.bidding.FyjsBiddingDocument;
import cn.fyg.schedule.pojo.fyjs.bidding.FyjsBiddingItem;
import cn.fyg.schedule.pojo.fyjs.bidding.FyjsBiddingOpen;
import cn.fyg.schedule.service.fyjs.bidding.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("fyjs/bidding")
public class FyJsBiddingController {
    private final FyjsBiddingOpenService biddingOpenService;
    private final FyjsBiddingTendererService biddingTendererService;
    private final FyjsBiddingDocumentService biddingDocumentService;
    private final FyjsBiddingItemService biddingItemService;
    private final FyjsBiddingInfoService biddingInfoService;

    public FyJsBiddingController(FyjsBiddingOpenService biddingOpenService, FyjsBiddingTendererService biddingTendererService, FyjsBiddingDocumentService biddingDocumentService, FyjsBiddingItemService biddingItemService, FyjsBiddingInfoService biddingInfoService) {
        this.biddingOpenService = biddingOpenService;
        this.biddingTendererService = biddingTendererService;
        this.biddingDocumentService = biddingDocumentService;
        this.biddingItemService = biddingItemService;
        this.biddingInfoService = biddingInfoService;
    }

    /**
     * info
     */
    @PostMapping("info/save")
    public BaseResponse saveInfo(FyjsBiddingInfoDto dto) {
        return biddingInfoService.save(dto);
    }

    @PostMapping("info/find/id")
    public BaseResponse findById(Integer id) {
        return biddingInfoService.findById(id);
    }

    @PostMapping("info/filter")
    public BaseResponse infoFilter(FyjsBiddingInfoDto dto, Pagination pagination) {
        return biddingInfoService.filter(pagination, dto);
    }

    /**
     * item
     */
    @PostMapping("item/save")
    public BaseResponse saveItem(FyjsBiddingItem data) {
        return biddingItemService.save(data);
    }

    @PostMapping("item/delete")
    public BaseResponse deleteItem(Integer id) {
        return biddingItemService.delete(id);
    }

    @PostMapping("item/list/parent/id")
    public BaseResponse itemListByParentId(Integer parentId) {
        return biddingItemService.findByParentId(parentId);
    }

    /**
     * third party (opener)
     */
    @PostMapping("opener/save")
    public BaseResponse openerSave(FyjsBiddingOpenDto dto) {
       return biddingOpenService.save(dto);
    }

    @PostMapping("opener/delete")
    public BaseResponse openerDelete(Integer id) {
        return biddingOpenService.delete(id);
    }
    @PostMapping("opener/delete/parent/id")
    public BaseResponse openerDelete(Integer id, String openerId) {
        return biddingOpenService.deleteByParentIdAndThirdPartyId(id, openerId);
    }
    @PostMapping("opener/find/parent/id")
    public BaseResponse openerFindByParentId(Integer parentId) {
        return biddingOpenService.findByParentId(parentId);
    }

    @PostMapping("opener/find/parent/third/party/id")
    public BaseResponse openerFindByParentIdAndThirdPartyId(Integer parentId, String thirdPartyId) {
        return biddingOpenService.findByParentIdAndThirdPartyId(parentId, thirdPartyId);
    }

    /**
     * tenderer
     */
    @PostMapping("tenderer/save")
    public BaseResponse saveTenderer(FyjsBiddingTendererDto dto) {
        return biddingTendererService.save(dto);
    }

    @PostMapping("tenderer/delete")
    public BaseResponse deleteTenderer(Integer id) {
        return biddingTendererService.delete(id);
    }

    @PostMapping("tenderer/delete/parent/id")
    public BaseResponse deleteTenderer(Integer id, String tendererId) {
        return biddingTendererService.deleteByParentIdAndTendererId(id, tendererId);
    }

    @PostMapping("tenderer/list/parent/id")
    public BaseResponse tendererListByParentId(Integer parentId) {
        return biddingTendererService.findByParentId(parentId);
    }

    @PostMapping("tenderer/find/id")
    public BaseResponse tendererFindById(Integer id) {
        return biddingTendererService.findById(id);
    }

    @PostMapping("tenderer/find/parent/tender/id")
    public BaseResponse tendererFindByParentIdAndTendererId(Integer parentId, String tendererId) {
        return biddingTendererService.findByParentIdAndTendererId(parentId, tendererId);
    }

    @PostMapping("tenderer/list/parent/tender/id/winner")
    public BaseResponse tendererListByParentIdAndTendererId(Integer parentId, String tendererId, Integer winner) {
        return biddingTendererService.findByParentIdAndTendererIdAndWinner(parentId, tendererId, winner);
    }

    /**
     * document
     */
    @PostMapping("document/save")
    public BaseResponse saveDocument(FyjsBiddingDocument data) {
        return biddingDocumentService.save(data);
    }

    @PostMapping("document/delete")
    public BaseResponse deleteDocument(Integer id) {
        return biddingDocumentService.delete(id);
    }

    @PostMapping("document/list/parent/id")
    public BaseResponse documentListByParentId(Integer parentId) {
        return biddingDocumentService.findByParentId(parentId);
    }

}
