package cn.fyg.schedule.service.fyg.accounting.impl;

import cn.fyg.schedule.dao.fyg.accounting.FygEquityDistributionRepository;
import cn.fyg.schedule.enums.fyg.accounting.EquityType;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.dto.fyg.accounting.equity.EquityDistributionDTO;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.pojo.fyg.accounting.FygEquityDistribution;
import cn.fyg.schedule.reponse.BaseResponse;
import cn.fyg.schedule.service.fyg.accounting.FygEquityDistributionService;
import cn.fyg.schedule.utils.ToEntityUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FygEquityDistributionServiceImpl implements FygEquityDistributionService {
    private final FygEquityDistributionRepository r;

    public FygEquityDistributionServiceImpl(FygEquityDistributionRepository r) {
        this.r = r;
    }

    @Override
    public BaseResponse<List<EquityDistributionDTO>> list(Integer dataStatus, Integer projectId) {
        BaseResponse<List<EquityDistributionDTO>> resp = new BaseResponse<>();
        try {
            List<FygEquityDistribution> list = r.findByDataStatusAndProjectId(dataStatus, projectId);
            List<EquityDistributionDTO> result = list.stream().map(this::toDto).collect(Collectors.toList());
            resp.initialize(200, "查询成功", result);
        } catch (JpaSystemException e) {
            resp.initialize(3001, "查询失败", null);
        }
        return resp;
    }

    @Override
    public BaseResponse<EquityDistributionDTO> findByProjectAndCounterparty(Integer projectId, String counterpartyAccountName, String equityType) {
        BaseResponse<EquityDistributionDTO> resp = new BaseResponse<>();
        try {
            Optional<FygEquityDistribution> optionalEntity = r.findByProjectIdAndCounterpartyAccountNameAndEquityType(
                    projectId, counterpartyAccountName, EquityType.valueOf(equityType));

            if (optionalEntity.isPresent()) {
                FygEquityDistribution entity = optionalEntity.get();
                resp.initialize(200, "查询成功", toDto(entity));
                // 如果这里是方法的最后一部分，可以直接返回resp，否则继续后续操作
            } else {
                resp.initialize(3001, "未找到该记录", null);
                // 处理未找到实体的情况，可能是返回resp或进行其他操作
            }
        } catch (JpaSystemException e) {
            resp.initialize(3001, "查询失败", null);
        }
        return resp;
    }

    @Override
    public BaseResponse<EquityDistributionDTO> findById(Integer id) {
        return null;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse<EquityDistributionDTO> save(EquityDistributionDTO value) {
        BaseResponse<EquityDistributionDTO> resp = new BaseResponse<>();
        FygEquityDistribution entity = toEntity(value);
        if (entity == null)
            resp.initialize(3001, "参数错误", null);
        else
            try {
                FygEquityDistribution data = save(entity);
                resp.initialize(200, "保存成功", toDto(data));
            } catch (MyException e) {
                resp.initialize(e.getCode(), e.getMessage(), null);
            }
        return resp;
    }

    @Override
    public BaseResponse<Void> filter(EquityDistributionDTO query, Pagination pagination) {
        return null;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse<EquityDistributionDTO> setDataStatus(Integer id, Integer dataStatus, String updatedBy) {
        BaseResponse<EquityDistributionDTO> resp = new BaseResponse<>();
        try {
            int i = r.updateDataStatus(id, dataStatus, updatedBy);
            if (i > 0) {
                resp.initialize(200, "删除成功" + i, null);
            } else {
                resp.initialize(3001, "删除失败", null);
            }
        } catch (JpaSystemException e) {
            log.error("删除失败", e);
            resp.initialize(3001, "删除失败", null);
        }
        return resp;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse<EquityDistributionDTO> save(EquityDistributionDTO value, Integer id) {
        BaseResponse<EquityDistributionDTO> resp = new BaseResponse<>();
        try {
            FygEquityDistribution data = update(value, id);
            resp.initialize(200, "更新成功", toDto(data));
        } catch (MyException e) {
            resp.initialize(e.getCode(), e.getMessage(), null);
        }
        return resp;
    }

    private EquityDistributionDTO toDto(FygEquityDistribution entity) {
        EquityDistributionDTO dto = ToEntityUtil.entityToDTO(entity, new EquityDistributionDTO());
        assert dto != null;
        if (entity.getProject() != null) {
            dto.setProjectName(entity.getProject().getProjectName());
        }
        return dto;
    }

    private FygEquityDistribution toEntity(EquityDistributionDTO dto) {
        return ToEntityUtil.DTOtoEntity(dto, new FygEquityDistribution());
    }

    private FygEquityDistribution save(FygEquityDistribution entity) throws MyException {
        try {
            return r.save(entity);
        } catch (JpaSystemException e) {
            throw new MyException(3001, "保存失败");
        }
    }

    private FygEquityDistribution update(EquityDistributionDTO dto, Integer id) throws MyException {
        try {
            FygEquityDistribution source = toEntity(dto);
            FygEquityDistribution target = getById(id);
            BeanUtils.copyProperties(source, target, "id", "createdAt", "createdBy");
            return save(target);
        } catch (JpaSystemException e) {
            throw new MyException(3001, "更新失败");
        }
    }

    private FygEquityDistribution getById(Integer id) throws MyException {
        try {
            return r.findById(id).orElseThrow(() -> new MyException(5001, "查询失败"));
        } catch (JpaSystemException e) {
            throw new MyException(5001, "id不存在");
        }
    }
}
