package cn.fyg.schedule.pojo.dto.query;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class FScheduleQuery extends Base{
    private Integer scheduleId;
    private String startDate;
    private String endDate;
    private String duration;
    private String searchText;
    private String currentStatus;
}
