package cn.fyg.schedule.service.cp.approval;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.cp.approval.CpApprovalTemplateRepository;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.cp.approval.CpApprovalTemplate;
import cn.fyg.schedule.pojo.dto.cp.approval.CpApprovalTemplateDto;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.specification.cp.approval.CpApprovalTemplateSpecification;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.repository.core.RepositoryCreationException;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class CpApprovalTemplateServiceImpl implements CpApprovalTemplateService {
    private final CpApprovalTemplateRepository r;

    public CpApprovalTemplateServiceImpl(CpApprovalTemplateRepository r) {
        this.r = r;
    }

    @Override
    public BaseResponse listTemplateByDataStatus(Integer dataStatus) {
        return null;
    }

    @Override
    public BaseResponse save(CpApprovalTemplateDto dto) {
        CpApprovalTemplate data = CpApprovalTemplate.initialize(dto);
        return BaseService.save(data, r, CpApprovalTemplate.class);
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            CpApprovalTemplate data = getById(id);
            baseResponse.setResult(data);
        } catch (MyException e) {
            baseResponse.setCode(e.getCode());
            baseResponse.setMsg(e.getErrMsg());
        }

        return baseResponse;
    }

    private CpApprovalTemplate getById(Integer id) throws MyException {
        try {
            Optional<CpApprovalTemplate> optional = r.findById(id);
            if (optional.isPresent()) {
                return optional.get();
            } else {
                throw new MyException(201, "没有找到数据");
            }
        } catch (MyException e) {
            log.error(e.getErrMsg());
            throw new MyException(500, "查询失败");
        }
    }
    @Override
    public BaseResponse findByTemplateIdAndDataStatus(String templateId, Integer dataStatus) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            CpApprovalTemplate data = ifHadTemplate(templateId, dataStatus);
            if (data != null) {
                baseResponse.setResult(data);
                baseResponse.setMsg("已存在模板id为" + data.getTemplateId() + "的模板数据。");
            } else {
                baseResponse.setCode(1);
                baseResponse.setMsg("未查询到数据");
            }
        } catch (MyException e) {
            baseResponse.setCode(e.getCode());
            baseResponse.setMsg(e.getErrMsg());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse getApiByTemplateId(String templateId) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            CpApprovalTemplate data = ifHadTemplate(templateId, 0);
            if (data != null) {
                baseResponse.setResult(data.getApiAddress());
                baseResponse.setMsg("已存在模板id为" + data.getTemplateId() + "的模板数据。");
            } else {
                baseResponse.setCode(1);
                baseResponse.setMsg("未查询到数据");
            }
        } catch (MyException e) {
            baseResponse.setCode(e.getCode());
            baseResponse.setMsg(e.getErrMsg());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse filter(CpApprovalTemplateDto dto) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Specification<CpApprovalTemplate> specification = getSpecification(dto);
            baseResponse.setResult(getListByFilter(specification));
        } catch (MyException e) {
            baseResponse.setCode(e.getCode());
            baseResponse.setMsg(e.getErrMsg());
        }
        return baseResponse;
    }

    @Override
    public CpApprovalTemplate getByTemplateId(String templateId, Integer dataStatus) throws MyException {
        try {
            Optional<CpApprovalTemplate> optional = r.findByTemplateIdAndDataStatus(templateId, dataStatus);
            return optional.orElse(null);
        } catch (JpaSystemException e) {
            throw new MyException(500, "操作错误");
        }
    }

    private List<CpApprovalTemplate> getListByFilter(Specification<CpApprovalTemplate> specification) throws MyException {
        try {
            return r.findAll(specification);
        } catch (RepositoryCreationException e) {
            throw new MyException(500, e.getMessage());
        }
    }

    private Specification<CpApprovalTemplate> getSpecification(CpApprovalTemplateDto dto) {
        return Specification.where(StrUtil.isEmpty(dto.getTemplateId()) ? null : CpApprovalTemplateSpecification.ifContain("templateId", dto.getTemplateId()))
                .and(StrUtil.isEmpty(dto.getTemplateName()) ? null : CpApprovalTemplateSpecification.ifContain("templateName", dto.getTemplateName()))
                .and(dto.getTemplateStatus() == null ? null : CpApprovalTemplateSpecification.ifEq("templateStatus", dto.getTemplateStatus()))
                .and(dto.getDataStatus() == null ? null : CpApprovalTemplateSpecification.ifEq("dataStatus", dto.getDataStatus()))
                .and(StrUtil.isEmpty(dto.getRemarks()) ? null : CpApprovalTemplateSpecification.ifContain("remarks", dto.getRemarks()));
    }

    private CpApprovalTemplate ifHadTemplate(String templateId, Integer dataStatus) throws MyException {
        try {
            Optional<CpApprovalTemplate> optional = r.findByTemplateIdAndDataStatus(templateId, dataStatus);
            return optional.orElse(null);
        } catch (RepositoryCreationException e) {
            log.error(e.getMessage());
            throw new MyException(500, "出错");
        }
    }
}
