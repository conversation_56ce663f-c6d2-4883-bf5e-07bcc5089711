package cn.fyg.schedule.service.sheets.xincai.notification.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.xincai.notification.XcNotificationRepository;
import cn.fyg.schedule.pojo.dto.project.yx.security.notification.YxSecurityNotificationDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.pojo.project.xincai.noti.XcNotification;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.sheets.xincai.notification.XcNotificationService;
import cn.fyg.schedule.specification.xincai.XcNotificationSpecification;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class XcNotificationServiceImpl implements XcNotificationService {
    private final XcNotificationRepository r;

    public XcNotificationServiceImpl(XcNotificationRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(YxSecurityNotificationDto dto) {
        XcNotification data = XcNotification.initialize(dto);
        return BaseService.save(data, r, XcNotification.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        XcNotification data = getDataById(id);
        if (data != null) {
            response.setResult(data);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    private XcNotification getDataById(Integer id) {
        try {
            Optional<XcNotification> optional = r.findById(id);
            if (optional.isPresent()) {
                return optional.get();
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    @Override
    public BaseResponse filter(YxSecurityNotificationDto dto, Pagination pagination) {
        BaseResponse response = BaseResponse.initialize();
        try {
            Sort sort = Sort.unsorted();
            if (!StrUtil.isEmpty(pagination.getColumnKey())) {
                if (!pagination.getOrder().equals("false")) {
                    sort = Sort.by(pagination.getOrder().equals("ascend") ? Sort.Direction.ASC : Sort.Direction.DESC, pagination.getColumnKey());
                }
            }
            Pageable pageable = PageRequest.of(pagination.getCurrentPage(), pagination.getPageSize(), sort);
            Specification<XcNotification> specification = getSpecification(dto);
            Page<XcNotification> data = r.findAll(specification, pageable);
            response.setResult(data);
        } catch (Exception e) {
            log.info(e.getMessage());
            response.setCode(1);
            response.setMsg("failed");
        }
        return response;
    }

    private Specification<XcNotification> getSpecification(YxSecurityNotificationDto dto) {
        Specification<XcNotification> specification = Specification.where(XcNotificationSpecification.ifEq("type", dto.getType()))
                .and(dto.getDataStatus() == null ? null : XcNotificationSpecification.ifEq("dataStatus", dto.getDataStatus()))
                .and(dto.getEventStatus() == null ? null : XcNotificationSpecification.ifEq("eventStatus", dto.getEventStatus()))
                .and(StrUtil.isEmpty(dto.getCreatorName()) ? null : XcNotificationSpecification.ifContain( "creatorName", dto.getCreatorName()))
                .and(StrUtil.isEmpty(dto.getYxProjectName()) ? null : XcNotificationSpecification.ifContain("yxProjectName", dto.getYxProjectName()))
                .and(StrUtil.isEmpty(dto.getYxProblemDescription()) ? null : XcNotificationSpecification.ifContain("yxProblemDescription", dto.getYxProblemDescription()))
                .and(dto.getInspectionDate() == null ? null : XcNotificationSpecification.ifDateEq("inspectionDate", DateUtil.date(dto.getInspectionDate())));
        return specification;
    }

    @Override
    public BaseResponse getEventNameList(Integer type) {
        BaseResponse response = BaseResponse.initialize();
        try {
            List<String> list = r.getEventNameHistoryList(type);
            response.setResult(list);
        } catch (Exception e) {
            response.setCode(1);
            response.setMsg("查询出错");
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public XcNotification getById(Integer id) {
        try {
            Optional<XcNotification> optional = r.findById(id);
            if (optional.isPresent()) {
                XcNotification data = optional.get();
                // 预加载懒加载的集合，避免LazyInitializationException
                preloadLazyCollections(data);
                return data;
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    /**
     * 预加载懒加载的集合，避免LazyInitializationException
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    private void preloadLazyCollections(XcNotification data) {
        if (data != null) {
            // 触发懒加载 - 我们只需要触发加载，不需要使用返回值
            if (data.getItems() != null) {
                data.getItems().size(); // 触发懒加载
            }
            if (data.getSignatures() != null) {
                data.getSignatures().size(); // 触发懒加载
            }
        }
    }
}
