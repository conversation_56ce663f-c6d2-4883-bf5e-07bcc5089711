package cn.fyg.schedule.controller.functions;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.core.FIdCard;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdcardUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("base/id")
@Slf4j
public class IdCardController {
    @PostMapping("info")
    public BaseResponse getIdCardInfo(String idCard) {
        BaseResponse response = BaseResponse.initialize();
        if (IdcardUtil.isValidCard(idCard)) {
            FIdCard data = FIdCard.initialized(idCard);
            response.setResult(data);
        } else {
            response.setCode(1);
            response.setMsg("身份证无效");
        }
        return response;
    }
}
