package cn.fyg.schedule.service.sheets.yx.security.notification.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.yuanxin.security.notification.YxSecurityNotificationRepository;
import cn.fyg.schedule.pojo.dto.project.yx.security.notification.YxSecurityNotificationDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.pojo.project.yuanXin.security.notification.YxSecurityNotification;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.sheets.yx.security.notification.YxSecurityNotificationService;
import cn.fyg.schedule.specification.yx.YxSecurityNotificationSpecification;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class YxSecurityNotificationServiceImpl implements YxSecurityNotificationService {
    private final YxSecurityNotificationRepository r;

    public YxSecurityNotificationServiceImpl(YxSecurityNotificationRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse save(YxSecurityNotificationDto dto) {
        BaseResponse response = BaseResponse.initialize();
        try {
            YxSecurityNotification data = YxSecurityNotification.initialize(dto);
            // 在事务内保存并立即预加载
            YxSecurityNotification savedData = r.save(data);
            preloadLazyCollections(savedData);
            response.setResult(savedData);
        } catch (Exception e) {
            log.error(e.getMessage());
            response.setCode(1);
            response.setMsg("保存失败");
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse findById(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        try {
            // 直接在事务内查询和预加载
            Optional<YxSecurityNotification> optional = r.findById(id);
            if (optional.isPresent()) {
                YxSecurityNotification data = optional.get();
                // 立即在事务内预加载懒加载的集合
                preloadLazyCollections(data);
                response.setResult(data);
            } else {
                response.setCode(1);
                response.setMsg("数据不存在");
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    private YxSecurityNotification getDataById(Integer id) {
        try {
            Optional<YxSecurityNotification> optional = r.findById(id);
            if (optional.isPresent()) {
                return optional.get();
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse filter(YxSecurityNotificationDto dto, Pagination pagination) {
        BaseResponse response = BaseResponse.initialize();
        try {
            Sort sort = Sort.unsorted();
            if (!StrUtil.isEmpty(pagination.getColumnKey())) {
                if (!pagination.getOrder().equals("false")) {
                    sort = Sort.by(pagination.getOrder().equals("ascend") ? Sort.Direction.ASC : Sort.Direction.DESC, pagination.getColumnKey());
                }
            }
            Pageable pageable = PageRequest.of(pagination.getCurrentPage(), pagination.getPageSize(), sort);
            Specification<YxSecurityNotification> specification = getSpecification(dto);
            Page<YxSecurityNotification> data = r.findAll(specification, pageable);
            // 立即在事务内预加载懒加载的集合
            for (YxSecurityNotification notification : data.getContent()) {
                preloadLazyCollections(notification);
            }
            response.setResult(data);
        } catch (Exception e) {
            log.error(e.getMessage());
            response.setCode(1);
            response.setMsg("failed");
        }
        return response;
    }

    @Override
    public BaseResponse getProjectNameList(Integer type) {
        BaseResponse response = BaseResponse.initialize();
        try {
            List<String> list = r.getProjectNameHistoryList(type);
            response.setResult(list);
        } catch (Exception e) {
            response.setCode(1);
            response.setMsg("查询出错");
        }
        return response;
    }

    @Override
    public BaseResponse getEventNameList(Integer type) {
        BaseResponse response = BaseResponse.initialize();
        try {
            List<String> list = r.getEventNameHistoryList(type);
            response.setResult(list);
        } catch (Exception e) {
            response.setCode(1);
            response.setMsg("查询出错");
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public YxSecurityNotification getById(Integer id) {
        try {
            Optional<YxSecurityNotification> optional = r.findById(id);
            if (optional.isPresent()) {
                YxSecurityNotification data = optional.get();
                // 预加载懒加载的集合，避免LazyInitializationException
                preloadLazyCollections(data);
                return data;
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse findByIdIn(Integer type, List<Integer> ids) {
        BaseResponse response = BaseResponse.initialize();
        try {
            // 在事务内完成查询和预加载
            List<YxSecurityNotification> list = r.findByDataStatusAndTypeAndIdIn(0, type, ids);
            if (!list.isEmpty()) {
                // 立即在事务内预加载懒加载的集合
                for (YxSecurityNotification notification : list) {
                    preloadLazyCollections(notification);
                }
                response.setResult(list);
            } else {
                response.setCode(1);
                response.setMsg("未查到数据");
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }




    private Specification<YxSecurityNotification> getSpecification(YxSecurityNotificationDto dto) {
        Specification<YxSecurityNotification> specification = Specification.where(YxSecurityNotificationSpecification.ifEq("type", dto.getType()))
                .and(dto.getDataStatus() == null ? null : YxSecurityNotificationSpecification.ifEq("dataStatus", dto.getDataStatus()))
                .and(dto.getEventStatus() == null ? null : YxSecurityNotificationSpecification.ifEq("eventStatus", dto.getEventStatus()))
                .and(StrUtil.isEmpty(dto.getCreatorName()) ? null : YxSecurityNotificationSpecification.ifContain( "creatorName", dto.getCreatorName()))
                .and(StrUtil.isEmpty(dto.getYxProjectName()) ? null : YxSecurityNotificationSpecification.ifContain("yxProjectName", dto.getYxProjectName()))
                .and(StrUtil.isEmpty(dto.getYxProblemDescription()) ? null : YxSecurityNotificationSpecification.ifContain("yxProblemDescription", dto.getYxProblemDescription()))
                .and(dto.getInspectionDate() == null ? null : YxSecurityNotificationSpecification.ifDateEq("inspectionDate", DateUtil.date(dto.getInspectionDate())));
        return specification;
    }

    /**
     * 预加载懒加载的集合，避免LazyInitializationException
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    private void preloadLazyCollections(YxSecurityNotification data) {
        if (data != null) {
            // 触发懒加载 - 我们只需要触发加载，不需要使用返回值
            if (data.getItems() != null) {
                data.getItems().size(); // 触发懒加载
            }
            if (data.getSignatures() != null) {
                data.getSignatures().size(); // 触发懒加载
            }
        }
    }

}
