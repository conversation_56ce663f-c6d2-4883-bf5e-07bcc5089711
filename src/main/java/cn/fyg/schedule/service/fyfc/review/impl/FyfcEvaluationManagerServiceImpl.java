package cn.fyg.schedule.service.fyfc.review.impl;

import cn.fyg.schedule.dao.fyfc.review.FyfcEvaluationRepository;
import cn.fyg.schedule.dao.fyfc.review.FyfcEvaluationScoreRepository;
import cn.fyg.schedule.dao.fyfc.review.FyfcEvaluationStatusHistoryRepository;
import cn.fyg.schedule.enums.fyfc.review.EvaluationStatus;
import cn.fyg.schedule.pojo.dto.fyfc.review.*;
import cn.fyg.schedule.pojo.fyfc.review.FyfcEvaluation;
import cn.fyg.schedule.pojo.fyfc.review.FyfcEvaluationStatusHistory;
import cn.fyg.schedule.service.fyfc.review.IFyfcEvaluationCommonService;
import cn.fyg.schedule.service.fyfc.review.IFyfcEvaluationManagerService;
import cn.fyg.schedule.service.fyfc.review.IFyfcEvaluationScoreService;
import cn.fyg.schedule.util.fyfc.review.FyfcEvaluationConverter;
import cn.fyg.schedule.specification.fyfc.review.FyfcEvaluationSpecification;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * FYFC 评价主管服务实现类
 * 对应 manager/dashboard 页面的业务逻辑
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FyfcEvaluationManagerServiceImpl implements IFyfcEvaluationManagerService {

    private final FyfcEvaluationRepository evaluationRepository;
    private final FyfcEvaluationScoreRepository scoreRepository;
    private final FyfcEvaluationStatusHistoryRepository statusHistoryRepository;
    private final IFyfcEvaluationCommonService commonService;
    private final IFyfcEvaluationScoreService scoreService;
    private final FyfcEvaluationConverter evaluationConverter;

    @Override
    public FyfcApiResponseDto<FyfcPaginatedResponseDto<FyfcEvaluationDto>> getPendingEvaluations(String managerName, FyfcEvaluationQueryDto queryDto) {
        log.info("主管查询待评价记录: managerName={}, query={}", managerName, queryDto);
        
        try {
            if (StrUtil.isBlank(managerName)) {
                return FyfcApiResponseDto.error(400, "主管姓名不能为空");
            }
            
            // 设置查询条件：主管姓名和非完成状态
            queryDto.setManagerName(managerName);
            // 不设置 queryType，而是明确排除已完成状态
            
            // 构建查询条件
            Specification<FyfcEvaluation> spec = FyfcEvaluationSpecification.buildSpecification(queryDto);

            // 添加排除已完成和主管评价状态的条件
            spec = spec.and(FyfcEvaluationSpecification.statusNotEquals(EvaluationStatus.COMPLETED));
            
            // 构建分页和排序
            Sort sort = buildSort(queryDto.getSortBy(), queryDto.getSortDirection());
            Pageable pageable = PageRequest.of(
                queryDto.getPage() - 1,
                queryDto.getSize(),
                sort
            );
            
            // 执行查询
            Page<FyfcEvaluation> page = evaluationRepository.findAll(spec, pageable);
            
            // 转换为DTO
            List<FyfcEvaluationDto> dtoList = page.getContent().stream()
                .map(evaluationConverter::convertToEvaluationDto)
                .collect(Collectors.toList());
            
            FyfcPaginatedResponseDto<FyfcEvaluationDto> paginatedResponse = 
                FyfcPaginatedResponseDto.of(dtoList, queryDto.getPage(), queryDto.getSize(), page.getTotalElements());
            
            return FyfcApiResponseDto.success(paginatedResponse, "查询成功");
        } catch (Exception e) {
            log.error("主管查询待评价记录失败", e);
            return FyfcApiResponseDto.error("查询失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<FyfcPaginatedResponseDto<FyfcEvaluationDto>> getCompletedEvaluations(String managerName, FyfcEvaluationQueryDto queryDto) {
        log.info("主管查询已完成评价: managerName={}, query={}", managerName, queryDto);
        
        try {
            if (StrUtil.isBlank(managerName)) {
                return FyfcApiResponseDto.error(400, "主管姓名不能为空");
            }
            
            // 设置查询条件：主管姓名和已完成状态
            queryDto.setManagerName(managerName);
            queryDto.setStatus("completed");
            
            // 构建查询条件
            Specification<FyfcEvaluation> spec = FyfcEvaluationSpecification.buildSpecification(queryDto);
            
            // 构建分页和排序
            Sort sort = buildSort(queryDto.getSortBy(), queryDto.getSortDirection());
            Pageable pageable = PageRequest.of(
                queryDto.getPage() - 1,
                queryDto.getSize(),
                sort
            );
            
            // 执行查询
            Page<FyfcEvaluation> page = evaluationRepository.findAll(spec, pageable);
            
            // 转换为DTO
            List<FyfcEvaluationDto> dtoList = page.getContent().stream()
                .map(evaluationConverter::convertToEvaluationDto)
                .collect(Collectors.toList());
            
            FyfcPaginatedResponseDto<FyfcEvaluationDto> paginatedResponse = 
                FyfcPaginatedResponseDto.of(dtoList, queryDto.getPage(), queryDto.getSize(), page.getTotalElements());
            
            return FyfcApiResponseDto.success(paginatedResponse, "查询成功");
        } catch (Exception e) {
            log.error("主管查询已完成评价失败", e);
            return FyfcApiResponseDto.error("查询失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public FyfcApiResponseDto<Boolean> submitManagerScore(FyfcScoreFormDto scoreForm, String managerName) {
        log.info("主管提交评分: form={}, manager={}", scoreForm, managerName);
        
        try {
            if (ObjectUtil.isNull(scoreForm) || StrUtil.isBlank(managerName)) {
                return FyfcApiResponseDto.error(400, "参数不能为空");
            }
            
            // 验证是否为主管评价
            Optional<FyfcEvaluation> optionalEvaluation = evaluationRepository.findById(scoreForm.getEvaluationId());
            if (!optionalEvaluation.isPresent()) {
                return FyfcApiResponseDto.error(404, "评价记录不存在");
            }
            
            FyfcEvaluation evaluation = optionalEvaluation.get();
            if (!managerName.equals(evaluation.getManagerName())) {
                return FyfcApiResponseDto.error(403, "您只能对自己负责的员工进行评分");
            }
            
            // 检查评价状态
            if (evaluation.getStatus() == EvaluationStatus.COMPLETED) {
                return FyfcApiResponseDto.error(400, "当前评价状态不允许主管评分");
            }
            
            // 调用评分服务（状态流转由 ScoreService 处理）
            return scoreService.submitScore(scoreForm, managerName);
        } catch (Exception e) {
            log.error("主管提交评分失败", e);
            return FyfcApiResponseDto.error("提交失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public FyfcApiResponseDto<Integer> batchApproveEvaluations(List<Integer> evaluationIds, String managerName) {
        log.info("主管批量审核评价: evaluationIds={}, manager={}", evaluationIds, managerName);
        
        try {
            if (CollUtil.isEmpty(evaluationIds) || StrUtil.isBlank(managerName)) {
                return FyfcApiResponseDto.error(400, "参数不能为空");
            }
            
            int successCount = 0;
            for (Integer evaluationId : evaluationIds) {
                try {
                    Optional<FyfcEvaluation> optionalEvaluation = evaluationRepository.findById(evaluationId);
                    if (!optionalEvaluation.isPresent()) {
                        log.warn("评价记录不存在: {}", evaluationId);
                        continue;
                    }
                    
                    FyfcEvaluation evaluation = optionalEvaluation.get();
                    
                    // 检查是否是该主管负责的评价
                    if (!managerName.equals(evaluation.getManagerName())) {
                        log.warn("主管无权限审核该评价: evaluationId={}, manager={}", evaluationId, managerName);
                        continue;
                    }
                    
                    // 检查当前状态是否可以审核
                    if (evaluation.getStatus() != EvaluationStatus.MANAGER) {
                        log.warn("评价状态不允许审核: evaluationId={}, status={}", evaluationId, evaluation.getStatus());
                        continue;
                    }
                    
                    // 更新状态为已完成
                    EvaluationStatus oldStatus = evaluation.getStatus();
                    evaluation.setStatus(EvaluationStatus.COMPLETED);
                    evaluation.setUpdatedBy(managerName);
                    evaluationRepository.save(evaluation);
                    
                    // 记录状态变更历史
                    recordStatusChange(evaluationId, oldStatus, EvaluationStatus.COMPLETED, managerName, "主管批量审核");
                    
                    successCount++;
                } catch (Exception e) {
                    log.error("审核评价失败: evaluationId={}", evaluationId, e);
                }
            }
            
            log.info("批量审核完成: 总数={}, 成功={}", evaluationIds.size(), successCount);
            return FyfcApiResponseDto.success(successCount, String.format("审核完成，成功审核%d条记录", successCount));
        } catch (Exception e) {
            log.error("批量审核评价失败", e);
            return FyfcApiResponseDto.error("审核失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<FyfcEvaluationStatsDto> getEvaluationStats(String managerName) {
        log.info("获取主管评价统计: {}", managerName);
        
        try {
            if (StrUtil.isBlank(managerName)) {
                return FyfcApiResponseDto.error(400, "主管姓名不能为空");
            }
            
            FyfcEvaluationStatsDto stats = new FyfcEvaluationStatsDto();
            
            // 查询该主管负责的评价
            Specification<FyfcEvaluation> spec = FyfcEvaluationSpecification.byManagerName(managerName);
            List<FyfcEvaluation> evaluations = evaluationRepository.findAll(spec);
            
            // 总评价数
            stats.setTotalEvaluations((long) evaluations.size());
            
            // 已完成评价数
            long completedCount = evaluations.stream()
                .filter(e -> e.getStatus() == EvaluationStatus.COMPLETED)
                .count();
            stats.setCompletedEvaluations(completedCount);
            
            // 待审核评价数
            long pendingCount = evaluations.stream()
                .filter(e -> e.getStatus() == EvaluationStatus.MANAGER)
                .count();
            stats.setInProgressEvaluations(pendingCount);
            
            // 计算完成率
            if (stats.getTotalEvaluations() > 0) {
                BigDecimal completionRate = BigDecimal.valueOf(completedCount)
                    .divide(BigDecimal.valueOf(stats.getTotalEvaluations()), 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
                stats.setCompletionRate(completionRate);
            } else {
                stats.setCompletionRate(BigDecimal.ZERO);
            }
            
            // TODO: 实现其他统计数据
            
            return FyfcApiResponseDto.success(stats, "查询成功");
        } catch (Exception e) {
            log.error("获取主管评价统计失败", e);
            return FyfcApiResponseDto.error("查询失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<List<String>> getManagedEmployees(String managerName) {
        log.info("获取主管负责的员工列表: {}", managerName);
        
        try {
            if (StrUtil.isBlank(managerName)) {
                return FyfcApiResponseDto.error(400, "主管姓名不能为空");
            }
            
            List<String> employees = evaluationRepository.findEmployeesByManager(managerName);
            
            return FyfcApiResponseDto.success(employees, "查询成功");
        } catch (Exception e) {
            log.error("获取主管负责员工列表失败", e);
            return FyfcApiResponseDto.error("查询失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<FyfcEvaluationDetailDto> getEvaluationDetail(Integer evaluationId, String managerName) {
        log.info("主管查看评价详情: evaluationId={}, manager={}", evaluationId, managerName);
        
        try {
            if (ObjectUtil.isNull(evaluationId) || StrUtil.isBlank(managerName)) {
                return FyfcApiResponseDto.error(400, "参数不能为空");
            }
            
            Optional<FyfcEvaluation> optionalEvaluation = evaluationRepository.findById(evaluationId);
            if (!optionalEvaluation.isPresent()) {
                return FyfcApiResponseDto.error(404, "评价记录不存在");
            }
            
            FyfcEvaluation evaluation = optionalEvaluation.get();
            
            // 检查权限
            if (!managerName.equals(evaluation.getManagerName())) {
                return FyfcApiResponseDto.error(403, "您只能查看自己负责的员工评价");
            }
            
            FyfcEvaluationDetailDto detailDto = evaluationConverter.convertToEvaluationDetailDto(evaluation);
            
            return FyfcApiResponseDto.success(detailDto, "查询成功");
        } catch (Exception e) {
            log.error("主管查看评价详情失败", e);
            return FyfcApiResponseDto.error("查询失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public FyfcApiResponseDto<Boolean> rejectEvaluation(Integer evaluationId, String managerName, String reason) {
        log.info("主管驳回评价: evaluationId={}, manager={}, reason={}", evaluationId, managerName, reason);
        
        try {
            if (ObjectUtil.isNull(evaluationId) || StrUtil.isBlank(managerName)) {
                return FyfcApiResponseDto.error(400, "参数不能为空");
            }
            
            Optional<FyfcEvaluation> optionalEvaluation = evaluationRepository.findById(evaluationId);
            if (!optionalEvaluation.isPresent()) {
                return FyfcApiResponseDto.error(404, "评价记录不存在");
            }
            
            FyfcEvaluation evaluation = optionalEvaluation.get();
            
            // 检查权限
            if (!managerName.equals(evaluation.getManagerName())) {
                return FyfcApiResponseDto.error(403, "您只能驳回自己负责的员工评价");
            }
            
            // 检查当前状态
            if (evaluation.getStatus() != EvaluationStatus.MANAGER) {
                return FyfcApiResponseDto.error(400, "当前状态不允许驳回");
            }
            
            // 退回到同事评价状态
            EvaluationStatus oldStatus = evaluation.getStatus();
            evaluation.setStatus(EvaluationStatus.COLLEAGUE);
            evaluation.setUpdatedBy(managerName);
            evaluationRepository.save(evaluation);
            
            // 记录状态变更历史
            String remark = StrUtil.isNotBlank(reason) ? "主管驳回: " + reason : "主管驳回";
            recordStatusChange(evaluationId, oldStatus, EvaluationStatus.COLLEAGUE, managerName, remark);
            
            return FyfcApiResponseDto.success(true, "评价已驳回");
        } catch (Exception e) {
            log.error("主管驳回评价失败", e);
            return FyfcApiResponseDto.error("驳回失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<Object> getPendingTaskStats(String managerName) {
        log.info("获取主管待办事项统计: {}", managerName);
        
        try {
            if (StrUtil.isBlank(managerName)) {
                return FyfcApiResponseDto.error(400, "主管姓名不能为空");
            }
            
            // 查询待审核的评价数量
            Specification<FyfcEvaluation> pendingSpec = FyfcEvaluationSpecification.pendingForUser(managerName);
            long pendingCount = evaluationRepository.count(pendingSpec);
            
            // 构建待办统计信息
            Object taskStats = buildPendingTaskStats(managerName, pendingCount);
            
            return FyfcApiResponseDto.success(taskStats, "查询成功");
        } catch (Exception e) {
            log.error("获取主管待办事项统计失败", e);
            return FyfcApiResponseDto.error("查询失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<List<FyfcEvaluationDto>> exportSubordinateEvaluations(String managerName, FyfcEvaluationQueryDto queryDto) {
        log.info("主管导出下属评价数据: manager={}, query={}", managerName, queryDto);
        
        try {
            if (StrUtil.isBlank(managerName)) {
                return FyfcApiResponseDto.error(400, "主管姓名不能为空");
            }
            
            // 设置查询条件
            queryDto.setManagerName(managerName);
            
            // 构建查询条件
            Specification<FyfcEvaluation> spec = FyfcEvaluationSpecification.buildSpecification(queryDto);
            
            // 构建排序
            Sort sort = buildSort(queryDto.getSortBy(), queryDto.getSortDirection());
            
            // 执行查询（不分页，导出全部）
            List<FyfcEvaluation> evaluations = evaluationRepository.findAll(spec, sort);
            
            // 转换为DTO
            List<FyfcEvaluationDto> dtoList = evaluations.stream()
                .map(evaluationConverter::convertToEvaluationDto)
                .collect(Collectors.toList());
            
            return FyfcApiResponseDto.success(dtoList, "导出成功");
        } catch (Exception e) {
            log.error("主管导出下属评价数据失败", e);
            return FyfcApiResponseDto.error("导出失败: " + e.getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 构建排序条件
     */
    private Sort buildSort(String sortBy, String sortDirection) {
        Sort.Direction direction = "asc".equalsIgnoreCase(sortDirection) 
            ? Sort.Direction.ASC 
            : Sort.Direction.DESC;
        
        String sortField = ObjectUtil.defaultIfNull(sortBy, "createdAt");
        return Sort.by(direction, sortField);
    }





    /**
     * 记录状态变更历史
     */
    private void recordStatusChange(Integer evaluationId, EvaluationStatus oldStatus, EvaluationStatus newStatus, String operatorName, String remark) {
        FyfcEvaluationStatusHistory history = new FyfcEvaluationStatusHistory();
        history.setEvaluationId(evaluationId);
        history.setPreviousStatus(oldStatus);
        history.setNewStatus(newStatus);
        history.setChangedBy(operatorName);
        history.setRemark(remark);
        
        statusHistoryRepository.save(history);
    }

    /**
     * 构建待办事项统计
     */
    private Object buildPendingTaskStats(String managerName, long pendingCount) {
        // TODO: 实现待办事项统计构建逻辑
        return new Object();
    }


}
