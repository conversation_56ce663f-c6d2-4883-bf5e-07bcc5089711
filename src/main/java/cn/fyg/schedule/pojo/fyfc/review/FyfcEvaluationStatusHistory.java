package cn.fyg.schedule.pojo.fyfc.review;

import cn.fyg.schedule.enums.fyfc.review.EvaluationStatus;
import cn.fyg.schedule.enums.fyfc.review.EvaluationStatusConverter;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.util.Date;

/**
 * 评价状态变更历史表实体
 * 对应数据库表: fyfc_evaluation_status_history
 */
@Entity
@Data
@Getter
@Setter
@ToString
@Table(name = "fyfc_evaluation_status_history")
public class FyfcEvaluationStatusHistory {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /**
     * 评价主表ID
     */
    @Column(name = "evaluation_id", nullable = false)
    private Integer evaluationId;

    /**
     * 之前状态
     */
    @Convert(converter = EvaluationStatusConverter.class)
    @Column(name = "previous_status")
    private EvaluationStatus previousStatus;

    /**
     * 新状态
     */
    @Convert(converter = EvaluationStatusConverter.class)
    @Column(name = "new_status", nullable = false)
    private EvaluationStatus newStatus;

    /**
     * 状态变更人
     */
    @Column(name = "changed_by", length = 50, nullable = false)
    private String changedBy;

    /**
     * 变更时间
     */
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "changed_at", nullable = false, updatable = false)
    private Date changedAt;

    /**
     * 变更备注
     */
    @Column(name = "remark", length = 255)
    private String remark;

    /**
     * 关联的评价主表
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "evaluation_id", insertable = false, updatable = false)
    private FyfcEvaluation evaluation;
}
