package cn.fyg.schedule.service.core;

import cn.fyg.schedule.service.sheets.yx.security.notification.YxSecurityNotiRecordSignService;
import cn.fyg.schedule.service.sheets.yx.security.notification.YxSecurityNotificationService;

import org.dom4j.DocumentException;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;

@Component
public class PdfGenerator {
    private final YxSecurityNotiRecordSignService yxSecurityNotiRecordSignService;
    private final YxSecurityNotificationService yxSecurityNotificationService;

    public PdfGenerator(YxSecurityNotiRecordSignService yxSecurityNotiRecordSignService, YxSecurityNotificationService yxSecurityNotificationService) {
        this.yxSecurityNotiRecordSignService = yxSecurityNotiRecordSignService;
        this.yxSecurityNotificationService = yxSecurityNotificationService;
    }

    public void generatePdf(HttpServletResponse response) throws IOException, DocumentException {

        // Create a new Document object
//        Document document = new Document(PageSize.A4, 18, 18, 27, 27);
//
//        // Create a new PdfWriter object
//        OutputStream out = response.getOutputStream();
//        PdfWriter.getInstance(document, out);
//
//        // Set the content type and the filename for the response
//        response.setContentType("application/pdf");
//        response.setHeader("Content-disposition", "attachment;filename=myDocument.pdf");
//
//        // Open the Document
//        document.open();
//
//        String path = getResourcePath("quality.png", "/static/img/");
//        // Create a Paragraph object with text and an image
//        Paragraph paragraph = new Paragraph();
//        paragraph.add(new Phrase("This is some text "));
//        Image image = Image.getInstance(path); // Replace with the path to your image file
//        image.scaleToFit(100, 100); // Scale the image to fit within 100x100 pixels
//        Chunk chunk = new Chunk(image, 0, 0, true);
//        paragraph.add(chunk);
//        paragraph.add(new Phrase(" more text."));
//
//        // Add the Paragraph to the Document
//        document.add(paragraph);
//
//        // Create a new PdfPTable object with 3 columns
//        PdfPTable table = new PdfPTable(2);
//
//        // Add some content to the table
//        for (int i = 1; i <= 10; i++) {
//            PdfPCell cell = new PdfPCell();
//            cell.addElement(new Phrase("This is some text "));
//            Image image2 = Image.getInstance(path); // Replace with the path to your image file
//            image2.scaleToFit(100, 100); // Scale the image to fit within 100x100 pixels
//            cell.addElement(new Chunk(image2, 0, 0, true));
//            cell.addElement(new Phrase(" more text."));
//            table.addCell(cell);
//        }
//
//        // Add the table to the Document
//        document.add(table);
//
//        // Start a new page
//        document.newPage();
//
//        // Add some content to the second page
//        Paragraph paragraph2 = new Paragraph("This is some content on the second page.");
//        document.add(paragraph2);
//
//        // Close the Document
//        document.close();
    }

    private String getResourcePath(String fileName, String documentName) throws IOException {
        File directory = new File("src/main/resources");
        String reportPath = directory.getCanonicalPath();
        String resource = reportPath + documentName + fileName;
        return resource;
    }
}
