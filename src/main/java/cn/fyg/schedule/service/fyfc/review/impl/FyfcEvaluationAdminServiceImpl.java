package cn.fyg.schedule.service.fyfc.review.impl;

import cn.fyg.schedule.dao.fyfc.review.FyfcEvaluationRepository;
import cn.fyg.schedule.dao.fyfc.review.FyfcEvaluationScoreRepository;
import cn.fyg.schedule.dao.fyfc.review.FyfcEvaluationStatusHistoryRepository;
import cn.fyg.schedule.enums.fyfc.review.EvaluationStatus;
import cn.fyg.schedule.pojo.dto.fyfc.review.*;
import cn.fyg.schedule.pojo.fyfc.review.FyfcEvaluation;
import cn.fyg.schedule.pojo.fyfc.review.FyfcEvaluationStatusHistory;
import cn.fyg.schedule.service.fyfc.review.IFyfcEvaluationAdminService;
import cn.fyg.schedule.service.fyfc.review.IFyfcEvaluationCommonService;
import cn.fyg.schedule.service.fyfc.review.IFyfcEvaluationScoreService;
import cn.fyg.schedule.util.fyfc.review.FyfcEvaluationConverter;
import cn.fyg.schedule.specification.fyfc.review.FyfcEvaluationSpecification;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * FYFC 评价管理员服务实现类
 * 对应 admin/dashboard 页面的业务逻辑
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FyfcEvaluationAdminServiceImpl implements IFyfcEvaluationAdminService {

    private final FyfcEvaluationRepository evaluationRepository;
    private final FyfcEvaluationScoreRepository scoreRepository;
    private final FyfcEvaluationStatusHistoryRepository statusHistoryRepository;
    private final IFyfcEvaluationCommonService commonService;
    private final IFyfcEvaluationScoreService scoreService;
    private final FyfcEvaluationConverter evaluationConverter;

    @Override
    public FyfcApiResponseDto<FyfcPaginatedResponseDto<FyfcEvaluationDto>> searchEvaluations(FyfcEvaluationQueryDto queryDto) {
        log.info("管理员查询评价数据: {}", queryDto);
        
        try {
            // 构建查询条件
            Specification<FyfcEvaluation> spec = FyfcEvaluationSpecification.buildSpecification(queryDto);
            
            // 构建分页和排序
            Sort sort = buildSort(queryDto.getSortBy(), queryDto.getSortDirection());
            Pageable pageable = PageRequest.of(
                queryDto.getPage() - 1, // Spring Data JPA 页码从0开始
                queryDto.getSize(),
                sort
            );
            
            // 执行查询
            Page<FyfcEvaluation> page = evaluationRepository.findAll(spec, pageable);
            
            // 转换为DTO
            List<FyfcEvaluationDto> dtoList = page.getContent().stream()
                .map(evaluationConverter::convertToEvaluationDtoWithScores)
                .collect(Collectors.toList());
            
            FyfcPaginatedResponseDto<FyfcEvaluationDto> paginatedResponse = 
                FyfcPaginatedResponseDto.of(dtoList, queryDto.getPage(), queryDto.getSize(), page.getTotalElements());
            
            return FyfcApiResponseDto.success(paginatedResponse, "查询成功");
        } catch (Exception e) {
            log.error("管理员查询评价数据失败", e);
            return FyfcApiResponseDto.error("查询失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public FyfcApiResponseDto<Boolean> updateEvaluationStatus(Integer evaluationId, String status, String operatorName) {
        log.info("管理员更新评价状态: evaluationId={}, status={}, operator={}", evaluationId, status, operatorName);
        
        try {
            if (ObjectUtil.isNull(evaluationId) || StrUtil.isBlank(status) || StrUtil.isBlank(operatorName)) {
                return FyfcApiResponseDto.error(400, "参数不能为空");
            }
            
            Optional<FyfcEvaluation> optionalEvaluation = evaluationRepository.findById(evaluationId);
            if (!optionalEvaluation.isPresent()) {
                return FyfcApiResponseDto.error(404, "评价记录不存在");
            }
            
            FyfcEvaluation evaluation = optionalEvaluation.get();
            EvaluationStatus oldStatus = evaluation.getStatus();
            EvaluationStatus newStatus;
            
            try {
                newStatus = EvaluationStatus.valueOf(status.toUpperCase());
            } catch (IllegalArgumentException e) {
                return FyfcApiResponseDto.error(400, "无效的状态值: " + status);
            }
            
            // 更新状态
            evaluation.setStatus(newStatus);
            evaluation.setUpdatedBy(operatorName);
            evaluationRepository.save(evaluation);
            
            // 记录状态变更历史
            recordStatusChange(evaluationId, oldStatus, newStatus, operatorName, "管理员操作");
            
            return FyfcApiResponseDto.success(true, "状态更新成功");
        } catch (Exception e) {
            log.error("更新评价状态失败", e);
            return FyfcApiResponseDto.error("更新失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<FyfcEvaluationStatsDto> getEvaluationStats() {
        log.info("获取评价统计数据");
        
        try {
            FyfcEvaluationStatsDto stats = new FyfcEvaluationStatsDto();
            
            // 总评价数
            long totalEvaluations = evaluationRepository.count();
            stats.setTotalEvaluations(totalEvaluations);
            
            // 已完成评价数
            long completedEvaluations = evaluationRepository.countByStatus(EvaluationStatus.COMPLETED);
            stats.setCompletedEvaluations(completedEvaluations);
            
            // 进行中评价数
            long inProgressEvaluations = totalEvaluations - completedEvaluations;
            stats.setInProgressEvaluations(inProgressEvaluations);
            
            // 完成率
            if (totalEvaluations > 0) {
                BigDecimal completionRate = BigDecimal.valueOf(completedEvaluations)
                    .divide(BigDecimal.valueOf(totalEvaluations), 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
                stats.setCompletionRate(completionRate);
            } else {
                stats.setCompletionRate(BigDecimal.ZERO);
            }
            
            // 获取部门统计
            List<FyfcEvaluationStatsDto.DepartmentStatsDto> departmentStats = getDepartmentStatsInternal();
            stats.setDepartmentStats(departmentStats);
            
            // TODO: 实现其他统计数据（月度统计、得分分布等）
            
            return FyfcApiResponseDto.success(stats, "查询成功");
        } catch (Exception e) {
            log.error("获取评价统计失败", e);
            return FyfcApiResponseDto.error("查询失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<List<FyfcEvaluationStatsDto.DepartmentStatsDto>> getDepartmentStats() {
        log.info("获取部门统计数据");
        
        try {
            List<FyfcEvaluationStatsDto.DepartmentStatsDto> departmentStats = getDepartmentStatsInternal();
            return FyfcApiResponseDto.success(departmentStats, "查询成功");
        } catch (Exception e) {
            log.error("获取部门统计失败", e);
            return FyfcApiResponseDto.error("查询失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<List<FyfcEvaluationDto>> exportEvaluations(FyfcEvaluationQueryDto queryDto) {
        log.info("导出评价数据: {}", queryDto);
        
        try {
            // 构建查询条件
            Specification<FyfcEvaluation> spec = FyfcEvaluationSpecification.buildSpecification(queryDto);
            
            // 构建排序
            Sort sort = buildSort(queryDto.getSortBy(), queryDto.getSortDirection());
            
            // 执行查询（不分页，导出全部）
            List<FyfcEvaluation> evaluations = evaluationRepository.findAll(spec, sort);
            
            // 转换为DTO
            List<FyfcEvaluationDto> dtoList = evaluations.stream()
                .map(evaluationConverter::convertToEvaluationDtoWithScores)
                .collect(Collectors.toList());
            
            return FyfcApiResponseDto.success(dtoList, "导出成功");
        } catch (Exception e) {
            log.error("导出评价数据失败", e);
            return FyfcApiResponseDto.error("导出失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public FyfcApiResponseDto<Integer> batchDeleteEvaluations(List<Integer> evaluationIds, String operatorName) {
        log.info("批量删除评价: ids={}, operator={}", evaluationIds, operatorName);
        
        try {
            if (CollUtil.isEmpty(evaluationIds)) {
                return FyfcApiResponseDto.error(400, "评价ID列表不能为空");
            }
            
            int successCount = 0;
            for (Integer evaluationId : evaluationIds) {
                try {
                    Optional<FyfcEvaluation> optionalEvaluation = evaluationRepository.findById(evaluationId);
                    if (!optionalEvaluation.isPresent()) {
                        log.warn("评价记录不存在: {}", evaluationId);
                        continue;
                    }
                    
                    // 删除相关的评分记录
                    scoreRepository.deleteByEvaluationId(evaluationId);
                    
                    // 删除状态历史记录
                    statusHistoryRepository.deleteByEvaluationId(evaluationId);
                    
                    // 删除评价记录
                    evaluationRepository.deleteById(evaluationId);
                    
                    successCount++;
                } catch (Exception e) {
                    log.error("删除评价失败: evaluationId={}", evaluationId, e);
                }
            }
            
            log.info("批量删除完成: 总数={}, 成功={}", evaluationIds.size(), successCount);
            return FyfcApiResponseDto.success(successCount, String.format("删除完成，成功删除%d条记录", successCount));
        } catch (Exception e) {
            log.error("批量删除评价失败", e);
            return FyfcApiResponseDto.error("删除失败: " + e.getMessage());
        }
    }

    @Override
    public FyfcApiResponseDto<Object> getSystemOptions() {
        log.info("获取系统配置选项");
        
        try {
            // 获取各种配置选项
            FyfcApiResponseDto<List<String>> departments = commonService.getDepartmentList();
            FyfcApiResponseDto<List<FyfcEvaluationStatusDto>> statuses = commonService.getStatusOptions();
            FyfcApiResponseDto<List<FyfcUserRoleDto>> roles = commonService.getUserRoleOptions();
            
            // 构建系统选项对象
            Object systemOptions = buildSystemOptions(departments.getData(), statuses.getData(), roles.getData());
            
            return FyfcApiResponseDto.success(systemOptions, "查询成功");
        } catch (Exception e) {
            log.error("获取系统配置选项失败", e);
            return FyfcApiResponseDto.error("查询失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public FyfcApiResponseDto<Boolean> resetEvaluationStatus(Integer evaluationId, String operatorName) {
        log.info("重置评价状态: evaluationId={}, operator={}", evaluationId, operatorName);
        
        try {
            if (ObjectUtil.isNull(evaluationId) || StrUtil.isBlank(operatorName)) {
                return FyfcApiResponseDto.error(400, "参数不能为空");
            }
            
            Optional<FyfcEvaluation> optionalEvaluation = evaluationRepository.findById(evaluationId);
            if (!optionalEvaluation.isPresent()) {
                return FyfcApiResponseDto.error(404, "评价记录不存在");
            }
            
            FyfcEvaluation evaluation = optionalEvaluation.get();
            EvaluationStatus oldStatus = evaluation.getStatus();
            
            // 重置为自评状态
            evaluation.setStatus(EvaluationStatus.SELF);
            evaluation.setUpdatedBy(operatorName);
            evaluationRepository.save(evaluation);
            
            // 删除所有评分记录
            scoreRepository.deleteByEvaluationId(evaluationId);
            
            // 记录状态变更历史
            recordStatusChange(evaluationId, oldStatus, EvaluationStatus.SELF, operatorName, "管理员重置状态");
            
            return FyfcApiResponseDto.success(true, "状态重置成功");
        } catch (Exception e) {
            log.error("重置评价状态失败", e);
            return FyfcApiResponseDto.error("重置失败: " + e.getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 构建排序条件
     */
    private Sort buildSort(String sortBy, String sortDirection) {
        Sort.Direction direction = "asc".equalsIgnoreCase(sortDirection) 
            ? Sort.Direction.ASC 
            : Sort.Direction.DESC;
        
        // 默认排序字段
        String sortField = ObjectUtil.defaultIfNull(sortBy, "createdAt");
        
        // 验证排序字段（防止 SQL 注入）
        if (!isValidSortField(sortField)) {
            sortField = "createdAt";
        }
        
        return Sort.by(direction, sortField);
    }

    /**
     * 验证排序字段是否合法
     */
    private boolean isValidSortField(String field) {
        List<String> allowedFields = CollUtil.newArrayList(
            "id", "department", "name", "reviewDate", "score",
            "status", "createdAt", "updatedAt", "createdBy"
        );
        return allowedFields.contains(field);
    }



    /**
     * 记录状态变更历史
     */
    private void recordStatusChange(Integer evaluationId, EvaluationStatus oldStatus, EvaluationStatus newStatus, String operatorName, String remark) {
        FyfcEvaluationStatusHistory history = new FyfcEvaluationStatusHistory();
        history.setEvaluationId(evaluationId);
        history.setPreviousStatus(oldStatus);
        history.setNewStatus(newStatus);
        history.setChangedBy(operatorName);
        history.setRemark(remark);
        
        statusHistoryRepository.save(history);
    }

    /**
     * 获取部门统计数据（内部方法）
     */
    private List<FyfcEvaluationStatsDto.DepartmentStatsDto> getDepartmentStatsInternal() {
        // TODO: 实现部门统计逻辑
        List<Object[]> departmentData = evaluationRepository.getDepartmentStatistics();
        
        return departmentData.stream()
            .map(data -> new FyfcEvaluationStatsDto.DepartmentStatsDto(
                (String) data[0],  // department
                (Long) data[1],    // count
                (BigDecimal) data[2], // averageScore
                (Long) data[3],    // completedCount
                (BigDecimal) data[4]  // completionRate
            ))
            .collect(Collectors.toList());
    }

    /**
     * 构建系统选项对象
     */
    private Object buildSystemOptions(List<String> departments, List<FyfcEvaluationStatusDto> statuses, List<FyfcUserRoleDto> roles) {
        // TODO: 实现系统选项构建逻辑
        return new Object();
    }
}
