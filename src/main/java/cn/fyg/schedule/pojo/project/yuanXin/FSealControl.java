package cn.fyg.schedule.pojo.project.yuanXin;

import cn.fyg.schedule.pojo.dto.FSealControlDto;
import cn.fyg.schedule.pojo.project.yinLong.FYinLongItem;
import cn.fyg.schedule.pojo.project.yinLong.FYinLongSignature;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@Entity
@Table(name = "fsealcontrol")
public class FSealControl {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Column(name = "number")
    private String number;
    @Column(name = "creator")
    private String creator;
    @Column(name = "department_name")
    private String departmentName;
    @Column(name = "seal_type")
    private String sealType;
    @Column(name = "contacts")
    private String contacts;
    @Column(name = "counts")
    private Integer counts;
    @Column(name = "audit_dept")
    private String auditDept;
    @Column(name = "seal_content")
    private String sealContent;
    @Column(name = "transaction_date")
    @Temporal(TemporalType.DATE)
    private Date transactionDate;
    @Column(name = "transaction_time")
    @Temporal(TemporalType.TIME)
    private Date transactionTime;
    @Column(name = "create_date")
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @Column(name = "update_date")
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;

//    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
//    @JoinColumn(name = "seal_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
//    @ToString.Exclude

    @Transient
    List<FSealControlSignature> signatureList = new ArrayList<>();

//    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
//    @JoinColumn(name = "seal_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
//    @ToString.Exclude
    @Transient
    List<FSealControlItem> items = new ArrayList<>();

    public static FSealControl initialize(FSealControlDto dto) {
        FSealControl result = new FSealControl();
        if (dto.getId() != null) {
            result.setId(dto.getId());
        }
        result.setCreator(dto.getCreator());
        result.setNumber(dto.getNumber());
        result.setDepartmentName(dto.getDepartmentName());
        result.setSealType(dto.getSealType());
        result.setContacts(dto.getContacts());
        result.setCounts(dto.getCounts());
        result.setAuditDept(dto.getAuditDept());
        result.setSealContent(dto.getSealContent());
        if (!StrUtil.isEmpty(dto.getTransactionDate())) {
            result.setTransactionDate(DateUtil.parseDate(dto.getTransactionDate()));
        }
        if (!StrUtil.isEmpty(dto.getTransactionTime())) {
            String time = dto.getTransactionTime() + ":00";
            result.setTransactionTime(DateUtil.parseTime(time));
        }
        if (!StrUtil.isEmpty(dto.getCreateDate())) {
            result.setCreateDate(DateUtil.parse(dto.getCreateDate()));
        }
        return result;
    }
}
