package cn.fyg.schedule.controller.fyjs;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.construction.FBuildingPermit;
import cn.fyg.schedule.pojo.construction.FBuildingPermitItem;
import cn.fyg.schedule.pojo.dto.construction.FBuildingPermitDto;
import cn.fyg.schedule.service.construction.FBuildingPermitItemService;
import cn.fyg.schedule.service.construction.FBuildingPermitService;
import cn.fyg.schedule.utils.GsonUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

@RestController
@RequestMapping("building/permit")
public class FBuildingPermitController {
    private Date STARTDATE = DateUtil.parseDate(DateUtil.format(DateUtil.date(), "yyyy-MM-dd"));
    private Integer BEGINING = 1;
    private final FBuildingPermitService fBuildingPermitService;
    private final FBuildingPermitItemService fBuildingPermitItemService;

    public FBuildingPermitController(FBuildingPermitService fBuildingPermitService,
                                     FBuildingPermitItemService fBuildingPermitItemService) {
        this.fBuildingPermitService = fBuildingPermitService;
        this.fBuildingPermitItemService = fBuildingPermitItemService;
    }

    @PostMapping("save")
    public BaseResponse save(FBuildingPermitDto dto) {
        FBuildingPermit data = FBuildingPermit.initialize(dto);
        return fBuildingPermitService.save(data);
    }

    @PostMapping("batch/save")
    public BaseResponse batchSave(String list, String creator) {
        return fBuildingPermitService.batchSave(GsonUtil.fromJsonList(list, FBuildingPermit.class), creator);
    }

    @PostMapping("delete")
    public BaseResponse delete(Integer id) {
        return fBuildingPermitService.delete(id);
    }

    @PostMapping("find/id")
    public BaseResponse findById(Integer id) {
        return fBuildingPermitService.findById(id);
    }

    @PostMapping("company/list")
    public BaseResponse getCompanyList() {
        return fBuildingPermitService.getCompanyHistoryList();
    }

    @PostMapping("list/company")
    public BaseResponse listCompanyIn(String companies, String approvalStatus) {
        return fBuildingPermitService.listCompanyIn(StrUtil.split(companies, ","), approvalStatus);
    }

    @PostMapping("item/save")
    public BaseResponse itemSave(FBuildingPermitItem data) {
        return fBuildingPermitItemService.save(data);
    }

    @PostMapping("item/delete")
    public BaseResponse itemDelete(Integer id) {
        return fBuildingPermitItemService.delete(id);
    }

    @PostMapping("number")
    public BaseResponse getNumber() {

        BaseResponse baseResponse = BaseResponse.initialize();
        String number = DateUtil.format(DateUtil.date(), "yyyyMMdd");
        number = BEGINING<10?number + "0" + BEGINING: number + BEGINING;
        Date current = DateUtil.parseDate(DateUtil.format(DateUtil.date(), "yyyy-MM-dd"));
        BEGINING = STARTDATE.compareTo(current) < 0? 1: BEGINING + 1;
        STARTDATE = STARTDATE.compareTo(current) < 0? current : STARTDATE;
        baseResponse.setResult(number);
        return baseResponse;
    }
}
