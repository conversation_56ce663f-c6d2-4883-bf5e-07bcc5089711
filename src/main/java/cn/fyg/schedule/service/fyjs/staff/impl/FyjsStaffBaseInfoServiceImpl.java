package cn.fyg.schedule.service.fyjs.staff.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.fyjs.staff.FyjsStaffBaseInfoRepository;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.dto.fyjs.staff.FyjsStaffBaseInfoDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.pojo.fyjs.staff.FyjsStaffBaseInfo;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.fyjs.staff.FyjsStaffBaseInfoService;
import cn.fyg.schedule.specification.fyjs.staff.FyjsStaffBaseInfoSpecification;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.Optional;

@Service
@Slf4j
public class FyjsStaffBaseInfoServiceImpl implements FyjsStaffBaseInfoService {
    private final FyjsStaffBaseInfoRepository r;

    public FyjsStaffBaseInfoServiceImpl(FyjsStaffBaseInfoRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FyjsStaffBaseInfoDto dto) {
        BaseResponse baseResponse = BaseResponse.initialize();
        FyjsStaffBaseInfo data = FyjsStaffBaseInfo.initialized(dto);
        if (dto.getId() != null) {
            FyjsStaffBaseInfo previousData = getById(dto.getId());
            if (previousData != null) {
                if (!previousData.equals(data)) {
                    data.setId(null);
                    previousData.setDataStatus(2);
                    save(previousData);
                }
            }
        }
        FyjsStaffBaseInfo result = save(data);
        if (result != null) {
            baseResponse.setResult(result);
        } else {
            baseResponse.setCode(1);
            baseResponse.setMsg("操作失败");
        }
        return baseResponse;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(FyjsStaffBaseInfoDto dto) {
        FyjsStaffBaseInfo data = FyjsStaffBaseInfo.initialized(dto);
        return BaseService.save(data, r, FyjsStaffBaseInfo.class);
    }

    private FyjsStaffBaseInfo save(FyjsStaffBaseInfo data) {
        try {
            FyjsStaffBaseInfo result = r.save(data);
            return result;
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        FyjsStaffBaseInfo result = getById(id);
        if (result != null) {
            response.setResult(result);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    private FyjsStaffBaseInfo getById(Integer id) {
        try {
            Optional<FyjsStaffBaseInfo> optional = r.findById(id);
            if (optional.isPresent()) {
                return optional.get();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    @Override
    public BaseResponse filter(Pagination pagination, FyjsStaffBaseInfoDto dto) {
        BaseResponse response = BaseResponse.initialize();
        try {
            Sort sort = Sort.unsorted();
            if (!StrUtil.isEmpty(pagination.getColumnKey())) {
                if (!pagination.getOrder().equals("false")) {
                    sort = Sort.by(pagination.getOrder().equals("ascend") ? Sort.Direction.ASC : Sort.Direction.DESC, pagination.getColumnKey());
                }
            }
            Pageable pageable = PageRequest.of(pagination.getCurrentPage(), pagination.getPageSize(), sort);
            Specification<FyjsStaffBaseInfo> specification = getSpecification(dto);
            Page<FyjsStaffBaseInfo> data = r.findAll(specification, pageable);
            response.setResult(data);
        } catch (Exception e) {
            log.info(e.getMessage());
            response.setCode(1);
            response.setMsg("查询出错");
        }
        return response;
    }

    @Override
    public BaseResponse findByIdCardAndStatus(String idCard, Integer dataStatus) {
        BaseResponse response = BaseResponse.initialize();
        FyjsStaffBaseInfo result = getByIdCardAndStatus(idCard, dataStatus);
        if (result != null) {
            response.setResult(result);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    @Override
    public BaseResponse updateAvatar(Integer id, String avatar) {
        BaseResponse response = BaseResponse.initialize();
        try {
            response.setResult(update(id, avatar));
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }

    private int update(Integer id, String avatar) throws MyException {
        try {
            return r.updateAvatar(avatar, id);
        } catch (JpaSystemException e) {
            log.error(e.getMessage());
            throw new MyException(442, "update avatar error");
        }
    }

    private FyjsStaffBaseInfo getByIdCardAndStatus(String idCard, Integer dataStatus) {
        try {
            Optional<FyjsStaffBaseInfo> optional = r.findFirstByStaffIdCardAndDataStatusOrderByCreateDateDesc(idCard, dataStatus);
            if (optional.isPresent()) {
                return optional.get();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    private Specification<FyjsStaffBaseInfo> getSpecification(FyjsStaffBaseInfoDto dto) {
        return Specification.where(dto.getDataStatus() == null ? null : FyjsStaffBaseInfoSpecification.ifEq("dataStatus", dto.getDataStatus()))
                .and(dto.getId() == null ? null : FyjsStaffBaseInfoSpecification.ifEq("id", dto.getId()))
                .and(StrUtil.isEmpty(dto.getCreatorName()) ? null : FyjsStaffBaseInfoSpecification.ifContain("creatorName", dto.getCreatorName()))
                .and(StrUtil.isEmpty(dto.getCreator()) ? null : FyjsStaffBaseInfoSpecification.ifContain("creator", dto.getCreator()))
                .and(StrUtil.isEmpty(dto.getStaffName()) ? null : FyjsStaffBaseInfoSpecification.ifContain("staffName", dto.getStaffName()))
                .and(StrUtil.isEmpty(dto.getStaffIdCard()) ? null : FyjsStaffBaseInfoSpecification.ifContain("staffIdCard", dto.getStaffIdCard()))
                .and(dto.getAgeStart() == null && dto.getAgeStop() == null ? null :
                        dto.getAgeStart() != null && dto.getAgeStop() == null ? FyjsStaffBaseInfoSpecification.ifNumberOverThan("age", dto.getAgeStart()) :
                                dto.getAgeStart() == null ? FyjsStaffBaseInfoSpecification.ifNumberLessThan("age", dto.getAgeStop()) :
                                        FyjsStaffBaseInfoSpecification.ifNumberBetween("age", dto.getAgeStart(), dto.getAgeStop()))
                .and(dto.getAgeInFyStart() == null && dto.getAgeInFyStop() == null ? null :
                        dto.getAgeInFyStart() != null && dto.getAgeInFyStop() == null ? FyjsStaffBaseInfoSpecification.ifNumberOverThan("ageInFy", dto.getAgeInFyStart()) :
                                dto.getAgeStart() == null ? FyjsStaffBaseInfoSpecification.ifNumberLessThan("ageInFy", dto.getAgeInFyStop()) :
                                        FyjsStaffBaseInfoSpecification.ifNumberBetween("ageInFy", dto.getAgeInFyStart(), dto.getAgeInFyStop()))
                .and(StrUtil.isEmpty(dto.getHouseholdRegistration()) ? null : FyjsStaffBaseInfoSpecification.ifContain("householdRegistration", dto.getHouseholdRegistration()))
                .and(StrUtil.isEmpty(dto.getHouseholdRegistrationType()) ? null : FyjsStaffBaseInfoSpecification.ifContain("householdRegistrationType", dto.getHouseholdRegistrationType()))
                .and(StrUtil.isEmpty(dto.getAcademicCareer()) ? null : FyjsStaffBaseInfoSpecification.ifContain("academicCareer", dto.getAcademicCareer()))
                .and(StrUtil.isEmpty(dto.getPoliticsStatus()) ? null : FyjsStaffBaseInfoSpecification.ifContain("politicsStatus", dto.getPoliticsStatus()))
                .and(StrUtil.isEmpty(dto.getPhoneNumber()) ? null : FyjsStaffBaseInfoSpecification.ifContain("phoneNumber", dto.getPhoneNumber()))
                .and(StrUtil.isEmpty(dto.getStaffProperty()) ? null : FyjsStaffBaseInfoSpecification.ifContain("staffProperty", dto.getStaffProperty()))
                .and(dto.getGender() == null ? null : FyjsStaffBaseInfoSpecification.ifEq("gender", dto.getGender()))
                .and(dto.getIsOnPost() == null ? null : FyjsStaffBaseInfoSpecification.ifEq("isOnPost", dto.getIsOnPost()))
                .and(StrUtil.isEmpty(dto.getBelongTo()) ? null : FyjsStaffBaseInfoSpecification.ifContain("belongTo", dto.getBelongTo()))
                .and(StrUtil.isEmpty(dto.getStaffRemarks()) ? null : FyjsStaffBaseInfoSpecification.ifContain("staffRemarks", dto.getStaffRemarks()))
                .and(dto.getBirthStart() == null && dto.getBirthStop() == null ? null :
                        dto.getBirthStart() != null && dto.getBirthStop() == null ? FyjsStaffBaseInfoSpecification.ifDateOverThan("birth", DateUtil.date(dto.getBirthStart())) :
                                dto.getBirthStart() == null ? FyjsStaffBaseInfoSpecification.ifDateLessThan("birth", DateUtil.date(dto.getBirthStop())) :
                                        FyjsStaffBaseInfoSpecification.ifDateBetween("birth", DateUtil.date(dto.getBirthStart()), DateUtil.date(dto.getBirthStop())))
                .and(dto.getEntryDateStart() == null && dto.getEntryDateStop() == null ? null :
                        dto.getEntryDateStart() != null && dto.getEntryDateStop() == null ? FyjsStaffBaseInfoSpecification.ifDateOverThan("entryDate", DateUtil.date(dto.getEntryDateStart())) :
                                dto.getEntryDateStart() == null ? FyjsStaffBaseInfoSpecification.ifDateLessThan("entryDate", DateUtil.date(dto.getEntryDateStop())) :
                                        FyjsStaffBaseInfoSpecification.ifDateBetween("entryDate", DateUtil.date(dto.getEntryDateStart()), DateUtil.date(dto.getEntryDateStop())))
                .and(dto.getDateBecomeRegularStart() == null && dto.getDateBecomeRegularStop() == null ? null :
                        dto.getDateBecomeRegularStart() != null && dto.getDateBecomeRegularStop() == null ? FyjsStaffBaseInfoSpecification.ifDateOverThan("dateBecomeRegular", DateUtil.date(dto.getDateBecomeRegularStart())) :
                                dto.getDateBecomeRegularStart() == null ? FyjsStaffBaseInfoSpecification.ifDateLessThan("dateBecomeRegular", DateUtil.date(dto.getDateBecomeRegularStop())) :
                                        FyjsStaffBaseInfoSpecification.ifDateBetween("dateBecomeRegular", DateUtil.date(dto.getDateBecomeRegularStart()), DateUtil.date(dto.getDateBecomeRegularStop())));
    }
}
