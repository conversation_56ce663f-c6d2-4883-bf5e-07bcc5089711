package cn.fyg.schedule.pojo;

import cn.fyg.schedule.pojo.dto.FScheduleItemDto;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "fscheduleitem")
public class FScheduleItem {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    private Integer scheduleId;
    private String type;
    private String content;
    @Column(name = "create_date")
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;

    public static FScheduleItem initialize(FScheduleItemDto dto) {
        FScheduleItem data = new FScheduleItem();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        data.setScheduleId(dto.getScheduleId());
        data.setType(dto.getType());
        data.setContent(dto.getContent());
        return data;
    }
}
