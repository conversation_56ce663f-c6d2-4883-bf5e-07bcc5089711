package cn.fyg.schedule.service.cp.approval;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.cp.approval.CpApprovalTemplate;
import cn.fyg.schedule.pojo.dto.cp.approval.CpApprovalTemplateDto;

public interface CpApprovalTemplateService {
    BaseResponse listTemplateByDataStatus(Integer dataStatus);
    BaseResponse save(CpApprovalTemplateDto dto);

    BaseResponse findById(Integer id);

    BaseResponse findByTemplateIdAndDataStatus(String templateId, Integer dataStatus);
    BaseResponse getApiByTemplateId(String templateId);

    BaseResponse filter(CpApprovalTemplateDto dto);

    CpApprovalTemplate getByTemplateId(String templateId, Integer dataStatus) throws MyException;
}
