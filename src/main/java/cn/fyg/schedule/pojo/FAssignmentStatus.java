package cn.fyg.schedule.pojo;

import cn.fyg.schedule.enums.AssignmentStatusEnum;
import cn.fyg.schedule.pojo.dto.FAssignmentStatusDto;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.util.Date;
@Data
@Entity
@Table(name = "fassignmentstatus")
public class FAssignmentStatus {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Column(name = "assignment_id")
    private Integer assignmentId;
    @Column(name = "name")
    private String name;
    @Column(name = "number")
    private String number;
    @Column(name = "create_date")
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;

    public static FAssignmentStatus initialize(FAssignmentStatusDto dto) {
        FAssignmentStatus fAssignmentStatus = new FAssignmentStatus();
        if (dto.getId() != null) {
            fAssignmentStatus.setId(dto.getId());
        }
        fAssignmentStatus.setAssignmentId(dto.getAssignmentId());
        fAssignmentStatus.setName(AssignmentStatusEnum.valueOf(dto.getValue()).getName());
        fAssignmentStatus.setNumber(AssignmentStatusEnum.valueOf(dto.getValue()).getNumber());
        return fAssignmentStatus;
    }
}
