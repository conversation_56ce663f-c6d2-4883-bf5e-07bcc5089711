package cn.fyg.schedule.enums.fyfc.review;

import cn.fyg.schedule.enums.BaseEnum;
import lombok.Getter;

/**
 * 评价状态枚举
 * 对应数据库中的 status 字段
 */
@Getter
public enum EvaluationStatus implements BaseEnum<String> {
    SELF("self", "自评阶段"),
    COLLEAGUE("colleague", "同事评价阶段"),
    MANAGER("manager", "主管评价阶段"),
    COMPLETED("completed", "评价完成");

    private final String value;
    private final String description;

    EvaluationStatus(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String toString() {
        return value;
    }
}
