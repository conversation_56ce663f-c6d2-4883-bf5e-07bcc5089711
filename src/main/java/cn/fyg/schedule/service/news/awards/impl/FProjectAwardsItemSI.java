package cn.fyg.schedule.service.news.awards.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.news.awards.FProjectAwardsItemR;
import cn.fyg.schedule.pojo.news.awads.FProjectAwardsItem;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.news.awards.FProjectAwardsItemS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class FProjectAwardsItemSI implements FProjectAwardsItemS {
    private final FProjectAwardsItemR r;
    public FProjectAwardsItemSI(FProjectAwardsItemR r) {
        this.r = r;
    }
    @Override
    public BaseResponse save(FProjectAwardsItem data) {
        return BaseService.save(data, r, FProjectAwardsItem.class);
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Optional<FProjectAwardsItem> data = r.findById(id);
            if (data.isPresent()) {
                baseResponse.setResult(data.get());
            }
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse listByAwardId(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FProjectAwardsItem> data = r.findByAwardId(id);
            baseResponse.setResult(data);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }
}
