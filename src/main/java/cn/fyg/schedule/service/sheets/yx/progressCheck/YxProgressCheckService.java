package cn.fyg.schedule.service.sheets.yx.progressCheck;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.project.yx.progressCheck.YxProgressCheckDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.pojo.project.yuanXin.progressCheck.YxProgressCheck;

import java.util.List;

public interface YxProgressCheckService {
    BaseResponse save(YxProgressCheckDto dto);
    BaseResponse delete(Integer id);
    BaseResponse findById(Integer id);
    BaseResponse filter(YxProgressCheckDto dto, Pagination pagination);
    YxProgressCheck getById(Integer id);

    BaseResponse listInspectionDepartmentHistory();

    BaseResponse findByIdIn(List<Integer> ids);
}
