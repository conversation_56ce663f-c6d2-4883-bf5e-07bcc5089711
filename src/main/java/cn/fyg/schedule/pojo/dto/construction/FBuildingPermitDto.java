package cn.fyg.schedule.pojo.dto.construction;

import cn.fyg.schedule.pojo.dto.BaseDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class FBuildingPermitDto extends BaseDto {
    private String company;
    private String number;
    private Long setUpDate;
    private String approvalStatus;
    private Long approvalDate;
    private String type;
    private String region;
    private String projectName;
    private String projectNano;
    private String principal;
    private String businessManager;
    private Double contractValue;
    private Double coveredArea;
    private String performanceScale;
    private String operationMode;
    private Integer timeLimit;
    private Long bidingWinDate;
    private Long startingDate;
    private Long completionDate;
    private String projectManager;
    private String municipalManager;
    private String technicalDirector;
    private String qualityInspector;
    private String constructionCrew;
    private String safetySupervisor;
    private String otherMember;
    private String changingInfo;
    private String contacts;
    private String phone;
    private Long estimatedCompletionDate;
    private String constructorCreditTarget;
    private String highQualityTarget;
    private String safetyStandardisation;
    private String noPollutionConstruction;
    private String newTechnology;
    private String intelligence;
}
