package cn.fyg.schedule.controller.yuanxin;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.ftask.FTaskTypeRepository;
import cn.fyg.schedule.pojo.dto.ftask.FTaskApproveDto;
import cn.fyg.schedule.pojo.dto.ftask.FTaskManagementDto;
import cn.fyg.schedule.pojo.ftask.*;
import cn.fyg.schedule.service.fTask.FTaskApproveAttachmentService;
import cn.fyg.schedule.service.fTask.FTaskApproveService;
import cn.fyg.schedule.service.fTask.FTaskAttachmentService;
import cn.fyg.schedule.service.fTask.FTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@RestController
@RequestMapping("task")
@Slf4j
public class FTaskController {
    private final FTaskTypeRepository r;
    private final FTaskService ts;
    private final FTaskAttachmentService tatts;
    private final FTaskApproveService tapps;
    private final FTaskApproveAttachmentService taas;

    public FTaskController(FTaskTypeRepository r,
                           FTaskService ts,
                           FTaskAttachmentService tatts,
                           FTaskApproveAttachmentService taas,
                           FTaskApproveService tapps) {
        this.r = r;
        this.ts = ts;
        this.tatts = tatts;
        this.taas = taas;
        this.tapps = tapps;
    }

    @PostMapping("ch")
    public String getTaskCh(String typeKey) {
       FTaskType taskType = getTaskType(typeKey);
       return taskType.getTypeCh();
    }

    @PostMapping("create")
    public BaseResponse createTask(FTaskManagementDto dto) {
//        BaseResponse response = BaseResponse.initialize();
//        FTaskType taskType = getTaskType(dto.getTypeKey());
//        if (taskType == null) {
//            response.setCode(1);
//            response.setMsg("初始化单据类型失败");
//        } else {
//            FTaskManagement data = FTaskManagement.initialize(dto);
//            data.setTypeCh(taskType.getTypeCh());
//            response = ts.save(data);
//        }
//        return response;
        FTaskManagement data = FTaskManagement.initialize(dto);
        return ts.save(data);
    }

    @PostMapping("delete")
    public BaseResponse delete(Integer id) {
        return ts.delete(id);
    }

    @PostMapping("find")
    public BaseResponse findTaskById(Integer id) {
        return ts.findById(id);
    }

    @PostMapping("list/all")
    public BaseResponse listAllByType(String typeKey) {
        return ts.listTaskByType(typeKey);
    }

    @PostMapping("create/attachment")
    public BaseResponse createAttachment(FTaskAttachment data) {
        return tatts.save(data);
    }

    @PostMapping("delete/attachment")
    public BaseResponse deleteAttachment(Integer id) {
        return tatts.delete(id);
    }

    @PostMapping("create/approve")
    public BaseResponse createApprove(FTaskApproveDto dto) {
        FTaskApprove data = FTaskApprove.initialize(dto);
        return tapps.save(data);
    }

    @PostMapping("delete/approve")
    public BaseResponse deleteApprove(Integer id) {
        return tapps.delete(id);
    }

    @PostMapping("create/approve/attachment")
    public BaseResponse createApproveAttachment(FTaskApproveAttachment data) {
        return taas.save(data);
    }

    @PostMapping("delete/approve/attachment")
    public BaseResponse deleteApproveAttachment(Integer id) {
        return taas.delete(id);
    }

    private FTaskType getTaskType(String typeKey) {
        try {
            Optional<FTaskType> oData = r.findByTypeKey(typeKey);
            if (oData.isPresent()) {
                return oData.get();
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }
}
