package cn.fyg.schedule.controller.fg.approval.document;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.fy.approval.document.FgDocumentApprovalDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.service.fg.approval.document.FgDocumentApprovalService;
import cn.hutool.core.util.StrUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("fg/approval/document")
public class FgDocumentApprovalController {
    private final FgDocumentApprovalService service;

    public FgDocumentApprovalController(FgDocumentApprovalService service) {
        this.service = service;
    }

    @PostMapping("save")
    public BaseResponse save(FgDocumentApprovalDto dto) {
        return service.save(dto);
    }

    @PostMapping("update")
    public BaseResponse update(FgDocumentApprovalDto dto) {
        return service.update(dto);
    }

    @PostMapping("update/sp/no")
    public BaseResponse update(String spNo, Integer id) {
        return service.update(spNo, id);
    }

    @PostMapping("update/apply/status")
    public BaseResponse update(Integer isApplied, Integer id) {
        return service.update(isApplied, id);
    }

    @PostMapping("delete")
    public BaseResponse delete(Integer id) {
        return service.delete(id);
    }

    @PostMapping("list")
    public BaseResponse list(Pagination pagination) {
        return service.list(pagination);
    }

    @PostMapping("find/id")
    public BaseResponse findById(Integer id) {
        return service.findById(id);
    }

    @PostMapping("find/sp/no")
    public BaseResponse findBySpNo(String spNo) {
        return service.findBySpNo(spNo);
    }

    @PostMapping("filter")
    public BaseResponse filter(FgDocumentApprovalDto dto, Pagination pagination) {
        return service.filter(dto, pagination);
    }

    @PostMapping("synchronize")
    public BaseResponse synchronize(String spNo, String templateId, String contents, String records) {
        if (StrUtil.isEmpty(records)) {
            return service.synchronize(spNo, templateId, contents);
        } else {
            return service.synchronize(spNo, templateId, contents, records);
        }
    }

}
