package cn.fyg.schedule.service.aliyun.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.config.AliyunEcsProperties;
import cn.fyg.schedule.service.aliyun.CompareFaceService;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.facebody.model.v20191230.CompareFaceRequest;
import com.aliyuncs.facebody.model.v20191230.CompareFaceResponse;
import com.aliyuncs.profile.DefaultProfile;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CompareFaceServiceImpl implements CompareFaceService {
    private final AliyunEcsProperties properties;
    public CompareFaceServiceImpl(AliyunEcsProperties properties) {
        this.properties = properties;
    }
    @Override
    public BaseResponse compareFace(String imgA, String imgB) {
        return this.doCompareFace(imgA, imgB);
    }
    private BaseResponse doCompareFace(String imgA, String imgB) {
        BaseResponse baseResponse = BaseResponse.initialize();
        DefaultProfile profile = DefaultProfile.getProfile(properties.getRegionId(), properties.getAccessKeyId(), properties.getAccessKeySecret());
        IAcsClient client = new DefaultAcsClient(profile);
        CompareFaceRequest request = new CompareFaceRequest();
//        request.setImageURLA("https://fcompareface.oss-cn-shanghai.aliyuncs.com/test/WechatIMG178.jpeg");
//        request.setImageURLB("https://fcompareface.oss-cn-shanghai.aliyuncs.com/test/%E6%8A%A5%E5%90%8D%E7%85%A7%E7%89%87.jpg");
        request.setImageURLA(imgA);
        request.setImageURLB(imgB);
        try {
            CompareFaceResponse response = client.getAcsResponse(request);
            baseResponse.setResult(response);
            log.info(new Gson().toJson(response));
        } catch (ServerException e) {
            e.printStackTrace();
        } catch (ClientException e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getErrMsg());
            baseResponse.setResult(e.getRequestId());
            log.info("ErrCode:" + e.getErrCode());
            log.info("ErrMsg:" + e.getErrMsg());
            log.info("RequestId:" + e.getRequestId());
        }
        return baseResponse;
    }
}
