package cn.fyg.schedule.service.yx.couthersign.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.yx.countersign.YxCountersignDetailRepository;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.dto.yx.countersign.CountersignDetailDto;
import cn.fyg.schedule.pojo.dto.yx.countersign.YxAuditor;
import cn.fyg.schedule.pojo.dto.yx.countersign.YxCountersignDetailDto;
import cn.fyg.schedule.pojo.yx.countersign.YxCountersignDetail;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.yx.couthersign.YxCountersignDetailService;
import cn.fyg.schedule.utils.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;

@Service
@Slf4j
public class YxCountersignDetailServiceImpl implements YxCountersignDetailService {
    private final YxCountersignDetailRepository r;

    public YxCountersignDetailServiceImpl(YxCountersignDetailRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(YxCountersignDetailDto dto) {
        BaseResponse response = BaseResponse.initialize();
        try {
            response.setResult(save(YxCountersignDetail.initialized(dto)));
        } catch (MyException e) {
            BaseService.setResponse(response, e.getCode(), e.getErrMsg());
        }
        return response;
    }

    private YxCountersignDetail save(YxCountersignDetail data) throws MyException {
        try {
            return r.save(data);
        } catch (JpaSystemException e) {
            throw new MyException(500, "操作失败");
        }
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        try {
            BaseService.deleteWithR(id, r);
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse deleteByParentId(Integer parentId) {
        BaseResponse response = BaseResponse.initialize();
        try {
            response.setResult(handleWithParentId(parentId));
        } catch (MyException e) {
            BaseService.setResponse(response, e.getCode(), e.getErrMsg());
        }
        return response;
    }

    private Integer handleWithParentId(Integer parentId) throws MyException {
        try {
            return r.deleteWhereParentIdEq(parentId);
        } catch (JpaSystemException e) {
            log.error(e.getMessage());
            throw new MyException(500, "操作失败");
        }
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse listByParentId(Integer parentId) {
        BaseResponse response = BaseResponse.initialize();
        try {
            List<YxCountersignDetail> data = findByParentId(parentId);
            // 预加载懒加载的集合，避免LazyInitializationException
            data.forEach(this::preloadLazyCollections);
            response.setResult(data);
        } catch (MyException e) {
            BaseService.setResponse(response, e.getCode(), e.getErrMsg());
        }
        return response;
    }

    @Override
    public BaseResponse listAuditorByParentId(Integer parentId) {
        BaseResponse response = BaseResponse.initialize();
        try {
            response.setResult(listAuditor(parentId));
        } catch (MyException e) {
            BaseService.setResponse(response, e.getCode(), e.getErrMsg());
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public List<YxCountersignDetail> listByParentIdAndFunctionIds(Integer parentId, List<Integer> ids) throws MyException{
        try {
            List<YxCountersignDetail> data = r.findByParentIdAndFunctionIdIn(parentId, ids);
            // 预加载懒加载的集合，避免LazyInitializationException
            data.forEach(this::preloadLazyCollections);
            return data;
        } catch (JpaSystemException e) {
            throw new MyException(500, "操作失败");
        }
    }

    @Override
    public Integer updateAuditor(String auditorId, Integer parentId, List<Integer> ids) throws MyException {
        try {
            return r.updateAuditorId(auditorId, parentId, ids);
        } catch (JpaSystemException e) {
            throw new MyException(500, "操作失败");
        }
    }

    @Override
    public Integer updateAuditor(String auditorId, Integer parentId, Integer functionId) throws MyException {
        try {
            return r.updateAuditorId(auditorId, parentId, functionId);
        } catch (JpaSystemException e) {
            throw new MyException(500, "操作失败");
        }
    }

    @Override
    public Integer updateDetail(String auditorSign, Integer judgment, Integer id) throws MyException {
        try {
            return r.updateDetail(auditorSign, judgment, id);
        } catch (JpaSystemException e) {
            throw new MyException(500, "操作失败");
        }
    }

    @Override
    public Integer updateDetail(String auditorSign, String comments, Integer judgment, Integer id) throws MyException {
        try {
            return r.updateDetail(auditorSign, comments, judgment, id);
        } catch (JpaSystemException e) {
            throw new MyException(500, "操作失败");
        }
    }

    @Override
    public BaseResponse updateDetail(String auditData) {
        BaseResponse response = BaseResponse.initialize();
        try {
            List<CountersignDetailDto> list = GsonUtil.fromJsonList(auditData, CountersignDetailDto.class);
            int count = 0;
            for (CountersignDetailDto data : list) {
                count += updateDetail(data.getAuditorSign(), data.getComments(), data.getJudgment(), data.getId());
            }
            response.setResult(count);
            response.setMsg("操作成功, 受影响的数据数量:" + count);
        } catch (MyException e) {
            BaseService.setResponse(response, e.getCode(), e.getErrMsg());
        } catch (Exception e) {
            BaseService.setResponse(response, 500, "系统错误");
        }
        return response;
    }

    private List<YxAuditor> listAuditor(Integer parentId) throws MyException {
        try {
            return r.listAuditorByParentId(parentId);
        } catch (JpaSystemException e) {
            log.error(e.getMessage());
            throw new MyException(500, "操作失败");
        }
    }

    private List<YxCountersignDetail> findByParentId(Integer parentId) throws MyException {
        try {
            return r.findByParentIdOrderByCreateDate(parentId);
        } catch (JpaSystemException e) {
            log.error(e.getMessage());
            throw new MyException(500, "操作失败");
        }
    }

    /**
     * 预加载懒加载的集合，避免LazyInitializationException
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    private void preloadLazyCollections(YxCountersignDetail detail) {
        if (detail != null) {
            // 触发懒加载 - 我们只需要触发加载，不需要使用返回值
            // 注意：departmentFunction字段有@JsonIgnore注解，通常不会被序列化，但为了安全起见还是预加载
            if (detail.getDepartmentFunction() != null) {
                detail.getDepartmentFunction().getId(); // 触发懒加载
            }
        }
    }
}
