package cn.fyg.schedule.controller.fyfc.review;

import cn.fyg.schedule.pojo.dto.fyfc.review.*;
import cn.fyg.schedule.service.fyfc.review.IFyfcEvaluationStaffService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * FYFC 评价员工控制器
 * 对应 staff/dashboard 和 staff/edit 页面的 API
 */
@Slf4j
@RestController
@RequestMapping("/api/fyfc/evaluation/staff")
@RequiredArgsConstructor
public class FyfcEvaluationStaffController {

    private final IFyfcEvaluationStaffService staffService;

    // ==================== Staff Dashboard API ====================

    /**
     * 员工查询自己的评价历史
     */
    @PostMapping("/history")
    public FyfcApiResponseDto<FyfcPaginatedResponseDto<FyfcEvaluationDto>> getEvaluationHistory(
            @RequestParam String userName,
            @RequestBody FyfcEvaluationQueryDto queryDto) {
        log.info("员工查询评价历史: userName={}", userName);
        return staffService.getEvaluationHistory(userName, queryDto);
    }

    /**
     * 员工查询待办评价
     */
    @GetMapping("/pending")
    public FyfcApiResponseDto<List<FyfcEvaluationDto>> getPendingEvaluations(
            @RequestParam String userName) {
        log.info("查询员工待办评价: userName={}", userName);
        return staffService.getPendingEvaluations(userName);
    }

    /**
     * 获取员工评价统计
     */
    @GetMapping("/stats")
    public FyfcApiResponseDto<FyfcEvaluationStatsDto> getEvaluationStats(
            @RequestParam String userName) {
        log.info("获取员工评价统计: userName={}", userName);
        return staffService.getEvaluationStats(userName);
    }

    // ==================== Staff Edit API ====================

    /**
     * 创建新的评价
     */
    @PostMapping("/create")
    public FyfcApiResponseDto<FyfcEvaluationDto> createEvaluation(
            @RequestBody FyfcEvaluationFormDto formDto,
            @RequestParam String creator) {
        log.info("创建新评价: creator={}", creator);
        return staffService.createEvaluation(formDto, creator);
    }

    /**
     * 更新评价基本信息
     */
    @PutMapping("/update")
    public FyfcApiResponseDto<Boolean> updateEvaluation(
            @RequestBody FyfcEvaluationUpdateDto updateDto,
            @RequestParam String updater) {
        log.info("更新评价: id={}, updater={}", updateDto.getId(), updater);
        return staffService.updateEvaluation(updateDto, updater);
    }

    /**
     * 获取评价详情
     */
    @GetMapping("/{id}")
    public FyfcApiResponseDto<FyfcEvaluationDetailDto> getEvaluationDetail(
            @PathVariable Integer id,
            @RequestParam String userName) {
        log.info("获取评价详情: id={}, userName={}", id, userName);
        return staffService.getEvaluationDetail(id, userName);
    }

    /**
     * 提交自评分数
     */
    @PostMapping("/score/self")
    public FyfcApiResponseDto<Boolean> submitSelfScore(
            @RequestBody FyfcScoreFormDto scoreForm,
            @RequestParam String evaluator) {
        log.info("提交自评分数: evaluationId={}, evaluator={}", scoreForm.getEvaluationId(), evaluator);
        return staffService.submitSelfScore(scoreForm, evaluator);
    }

    /**
     * 提交同事评分
     */
    @PostMapping("/score/colleague")
    public FyfcApiResponseDto<Boolean> submitColleagueScore(
            @RequestBody FyfcScoreFormDto scoreForm,
            @RequestParam String evaluator) {
        log.info("提交同事评分: evaluationId={}, evaluator={}", scoreForm.getEvaluationId(), evaluator);
        return staffService.submitColleagueScore(scoreForm, evaluator);
    }

    /**
     * 删除评价
     */
    @DeleteMapping("/{id}")
    public FyfcApiResponseDto<Boolean> deleteEvaluation(
            @PathVariable Integer id,
            @RequestParam String operator) {
        log.info("删除评价: id={}, operator={}", id, operator);
        return staffService.deleteEvaluation(id, operator);
    }

    /**
     * 检查用户权限
     */
    @GetMapping("/{id}/permission")
    public FyfcApiResponseDto<Boolean> checkUserPermission(
            @PathVariable Integer id,
            @RequestParam String userName) {
        log.info("检查用户权限: id={}, userName={}", id, userName);
        return staffService.checkUserPermission(id, userName);
    }

    /**
     * 获取表单初始数据
     */
    @GetMapping("/form/init")
    public FyfcApiResponseDto<Object> getFormInitData(@RequestParam String userName) {
        log.info("获取表单初始数据: userName={}", userName);
        return staffService.getFormInitData(userName);
    }

    // ==================== 辅助 API ====================

    /**
     * 预览评价（不保存）
     */
    @PostMapping("/preview")
    public FyfcApiResponseDto<Object> previewEvaluation(@RequestBody FyfcEvaluationFormDto formDto) {
        log.info("预览评价");
        // TODO: 实现评价预览功能
        return FyfcApiResponseDto.success(null, "预览功能开发中");
    }

    /**
     * 保存草稿
     */
    @PostMapping("/draft")
    public FyfcApiResponseDto<Object> saveDraft(
            @RequestBody FyfcEvaluationFormDto formDto,
            @RequestParam String userName) {
        log.info("保存草稿: userName={}", userName);
        // TODO: 实现草稿保存功能
        return FyfcApiResponseDto.success(null, "草稿功能开发中");
    }

    /**
     * 获取草稿列表
     */
    @GetMapping("/drafts")
    public FyfcApiResponseDto<List<Object>> getDrafts(@RequestParam String userName) {
        log.info("获取草稿列表: userName={}", userName);
        // TODO: 实现草稿列表功能
        return FyfcApiResponseDto.success(null, "草稿功能开发中");
    }

    /**
     * 复制评价
     */
    @PostMapping("/{id}/copy")
    public FyfcApiResponseDto<FyfcEvaluationDto> copyEvaluation(
            @PathVariable Integer id,
            @RequestParam String userName) {
        log.info("复制评价: id={}, userName={}", id, userName);
        // TODO: 实现评价复制功能
        return FyfcApiResponseDto.success(null, "复制功能开发中");
    }

    /**
     * 获取评价模板
     */
    @GetMapping("/templates")
    public FyfcApiResponseDto<List<Object>> getEvaluationTemplates() {
        log.info("获取评价模板");
        // TODO: 实现评价模板功能
        return FyfcApiResponseDto.success(null, "模板功能开发中");
    }
}
