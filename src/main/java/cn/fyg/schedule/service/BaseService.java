package cn.fyg.schedule.service;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.wx.ImageChat;
import cn.fyg.schedule.pojo.wx.ImageContent;
import cn.fyg.schedule.pojo.wx.TextChat;
import cn.fyg.schedule.utils.ImageUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.convert.ConvertException;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.repository.CrudRepository;
import org.springframework.orm.jpa.JpaSystemException;

import java.awt.image.BufferedImage;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Slf4j
public class BaseService {
    public static void setResponse(BaseResponse response, Integer code, String msg) {
        response.setCode(code);
        response.setMsg(msg);
    }
    public static <T> T getById(Integer id, CrudRepository<T, Integer> repository) throws MyException {
        try {
            Optional<T> optional = repository.findById(id);
            if (optional.isPresent()) {
                return optional.get();
            } else {
                throw new MyException(201, "未查到数据");
            }
        } catch (JpaSystemException e) {
            log.info(e.getMessage());
            throw new MyException(500, "查询出错");
        }
    }

    public static <T> void deleteWithR(Integer id, CrudRepository<T, Integer> r) throws MyException {
        try {
            r.deleteById(id);
        } catch (JpaSystemException e) {
            throw new MyException(300, e.getMessage());
        }  catch (Exception e) {
            throw new MyException(500, "系统错误");
        }
    }
    public static <T> BaseResponse delete(Integer id, CrudRepository<T, Integer> r) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            r.deleteById(id);
            baseResponse.setCode(0);
            baseResponse.setMsg("success");
            baseResponse.setResult(null);

        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            baseResponse.setResult(null);
            log.info(e.getMessage());
        }
        return baseResponse;
    }
    public static <T> T saveWithR(T data, CrudRepository<T, Integer> r, Class<T> type) throws MyException {
        try {
            return Convert.convert(type, r.save(data));
        } catch (JpaSystemException e) {
            throw new MyException(300, e.getMessage());
        } catch (ConvertException e) {
            throw new MyException(500, "类型转换出错");
        } catch (Exception e) {
            throw new MyException(500, "系统错误");
        }
    }
    public static <T> BaseResponse save(T data, CrudRepository<T, Integer> r, Class<T> type) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            baseResponse.setCode(0);
            baseResponse.setMsg("success");
            baseResponse.setResult(Convert.convert(type, r.save(data)));
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            baseResponse.setResult(null);
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    public static void setList(String type, String content, Date dateTime, List<Object> new_list) {
        switch (type) {
            case "text":
                TextChat textChat = new TextChat();
                textChat.setNews_type("text");
                textChat.setNews_centent(content);
                textChat.setNews_createdate(DateUtil.format(dateTime, "yyyy-MM-dd HH:mm:ss"));
                new_list.add(textChat);
                break;
            case "image":
                ImageChat imageChat = new ImageChat();
                imageChat.setNews_type("image");
                imageChat.setNews_centent(initImageInfo("https://weixin.fyg.cn/fyschedule/image/" + content));
                imageChat.setNews_createdate(DateUtil.format(dateTime, "yyyy-MM-dd HH:mm:ss"));
                new_list.add(imageChat);
                break;
        }
    }

    private static ImageContent initImageInfo(String src) {
        BufferedImage bufferedImage = ImageUtil.initImage(src);
        ImageContent imageContent = new ImageContent();
        imageContent.setSrc(src);
        assert bufferedImage != null;
        imageContent.setHeight(bufferedImage.getHeight());
        imageContent.setWidth(bufferedImage.getWidth());
        return imageContent;
    }
}
