# FYFC 评价系统 API 完整指南

## 📋 **目录**

1. [API 概述](#api-概述)
2. [评价管理 API](#评价管理-api)
3. [OSS 文件管理 API](#oss-文件管理-api)
4. [数据类型定义](#数据类型定义)
5. [错误处理](#错误处理)
6. [最佳实践](#最佳实践)

---

## 🎯 **API 概述**

### **基础信息**

- **Base URL**: `https://localhost:7001/fyschedule2`
- **Content-Type**: `application/json` (除文件上传外)
- **认证方式**: Session/Cookie
- **响应格式**: 统一的 `FyfcApiResponseDto` 格式

### **统一响应格式**

```typescript
interface FyfcApiResponseDto<T> {
  success: boolean;
  code?: number;
  message: string;
  data?: T;
  timestamp?: number;
}
```

### **分页响应格式**

```typescript
interface FyfcPaginatedResponseDto<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
}
```

---

## 📊 **评价管理 API**

### **1. 管理员接口**

#### **搜索评价列表**
```http
GET /api/fyfc/evaluation/admin/search
```

**查询参数：**
```typescript
interface FyfcEvaluationQueryDto {
  name?: string;           // 姓名模糊查询
  department?: string;     // 部门精确查询
  status?: string;         // 状态筛选
  startDate?: string;      // 开始日期 (yyyy-MM-dd)
  endDate?: string;        // 结束日期 (yyyy-MM-dd)
  page?: number;           // 页码 (从0开始)
  size?: number;           // 每页大小
}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "content": [
      {
        "id": 6,
        "name": "张三",
        "department": "技术部",
        "position": "高级工程师",
        "status": "PENDING",
        "reviewDate": 1749188994768,
        "createdAt": 1749188994768,
        "score": 85.5,
        "attachments": [
          {
            "id": "uuid",
            "fileName": "自评报告.docx",
            "fileKey": "fyfc/evaluation/6/2025/06/06/file.docx",
            "fileSize": 16316,
            "uploadTime": 1749188994768,
            "uploadBy": "张三"
          }
        ],
        "scores": [
          {
            "id": 1,
            "evaluatorUsername": "zhangsan",
            "evaluatorName": "张三",
            "evaluatorType": "EMPLOYEE",
            "score": 85.5,
            "comment": "工作表现优秀",
            "submittedAt": 1749188994768
          }
        ]
      }
    ],
    "totalElements": 1,
    "totalPages": 1,
    "size": 10,
    "number": 0
  }
}
```

#### **导出评价数据**
```http
POST /api/fyfc/evaluation/admin/export
Content-Type: application/json

{
  "name": "张三",
  "department": "技术部",
  "status": "COMPLETED"
}
```

#### **更新评价状态**
```http
PUT /api/fyfc/evaluation/admin/status
Content-Type: application/json

{
  "evaluationId": 6,
  "status": "COMPLETED",
  "operatorName": "admin"
}
```

### **2. 员工接口**

#### **获取待评价列表**
```http
GET /api/fyfc/evaluation/staff/pending/{username}
```

#### **获取评价详情**
```http
GET /api/fyfc/evaluation/staff/detail/{evaluationId}?username={username}
```

#### **提交自评**
```http
POST /api/fyfc/evaluation/staff/submit
Content-Type: application/json

{
  "evaluationId": 6,
  "score": 85.5,
  "comment": "本年度工作总结...",
  "username": "zhangsan"
}
```

### **3. 经理接口**

#### **获取待审核列表**
```http
GET /api/fyfc/evaluation/manager/pending/{managerUsername}
```

#### **获取已完成列表**
```http
GET /api/fyfc/evaluation/manager/completed/{managerUsername}
```

#### **提交经理评价**
```http
POST /api/fyfc/evaluation/manager/submit
Content-Type: application/json

{
  "evaluationId": 6,
  "score": 88.0,
  "comment": "员工表现优秀，建议晋升",
  "managerUsername": "manager01"
}
```

### **4. 通用接口**

#### **获取评价信息**
```http
GET /api/fyfc/evaluation/common/{evaluationId}
```

#### **获取状态历史**
```http
GET /api/fyfc/evaluation/common/status-history/{evaluationId}
```

#### **获取评分记录**
```http
GET /api/fyfc/evaluation/common/scores/{evaluationId}
```

---

## 📁 **OSS 文件管理 API**

### **1. 文件上传**

#### **单文件上传**
```http
POST /api/fyfc/oss/upload
Content-Type: multipart/form-data

Parameters:
- file: File (必需)
- evaluationId: number (必需)
- uploadBy: string (必需)
- bucketName?: string (可选)
```

#### **批量上传**
```http
POST /api/fyfc/oss/upload/batch
Content-Type: multipart/form-data

Parameters:
- files: File[] (必需)
- evaluationId: number (必需)
- uploadBy: string (必需)
- bucketName?: string (可选)
```

### **2. 文件管理**

#### **获取附件列表**
```http
GET /api/fyfc/oss/attachments/{evaluationId}?bucketName={bucketName}
```

#### **删除文件**
```http
DELETE /api/fyfc/oss/delete
Content-Type: application/json

{
  "fileKey": "fyfc/evaluation/6/2025/06/06/file.docx",
  "evaluationId": 6,
  "operatorName": "张三",
  "bucketName": "fyfc"
}
```

#### **获取下载URL**
```http
GET /api/fyfc/oss/url?fileKey={fileKey}&expireSeconds={seconds}&bucketName={bucketName}
```

#### **文件预览**
```http
GET /api/fyfc/oss/preview?fileKey={fileKey}&bucketName={bucketName}
```

---

## 📝 **数据类型定义**

### **评价相关类型**

```typescript
interface FyfcEvaluation {
  id: number;
  name: string;
  department: string;
  position?: string;
  status: EvaluationStatus;
  reviewDate: number;      // 时间戳
  createdAt: number;       // 时间戳
  updatedAt?: number;      // 时间戳
  score?: number;
  attachments?: string;    // JSON字符串，后端使用
}

interface FyfcEvaluationDto {
  id: number;
  name: string;
  department: string;
  position?: string;
  status: string;
  reviewDate: number;
  createdAt: number;
  score?: number;
  attachments: FyfcAttachment[];  // 对象数组，前端使用
  scores?: FyfcEvaluationScore[];
}

interface FyfcEvaluationDetailDto extends FyfcEvaluationDto {
  updatedAt?: number;
  statusHistory?: FyfcEvaluationStatusHistory[];
}

type EvaluationStatus = 'PENDING' | 'SELF' | 'COLLEAGUE' | 'MANAGER' | 'COMPLETED';
type EvaluatorType = 'EMPLOYEE' | 'COLLEAGUE' | 'MANAGER';
```

### **评分相关类型**

```typescript
interface FyfcEvaluationScore {
  id: number;
  evaluationId: number;
  evaluatorUsername: string;
  evaluatorName: string;
  evaluatorType: EvaluatorType;
  score: number;
  comment?: string;
  submittedAt: number;
}

interface FyfcEvaluationScoreSubmitDto {
  evaluationId: number;
  evaluatorUsername: string;
  evaluatorType: EvaluatorType;
  score: number;
  comment?: string;
}
```

### **附件相关类型**

```typescript
interface FyfcAttachment {
  id: string;
  fileName: string;
  fileKey: string;
  bucketName?: string;
  fileSize: number;
  fileType: string;
  uploadTime: number;
  uploadBy: string;
  fileUrl?: string;
}

interface FyfcOssDeleteDto {
  fileKey: string;
  evaluationId: number;
  operatorName: string;
  bucketName?: string;
}
```

### **查询和更新类型**

```typescript
interface FyfcEvaluationQueryDto {
  name?: string;
  department?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  size?: number;
}

interface FyfcEvaluationUpdateDto {
  id: number;
  name?: string;
  department?: string;
  position?: string;
  reviewDate?: number;
  score?: number;
  operatorName: string;
}

interface FyfcEvaluationStatusDto {
  evaluationId: number;
  status: EvaluationStatus;
  operatorName: string;
}
```

---

## ⚠️ **错误处理**

### **常见错误码**

| 错误码 | 说明 | 示例 |
|--------|------|------|
| 400 | 请求参数错误 | 缺少必需参数 |
| 401 | 未授权 | 登录过期 |
| 403 | 权限不足 | 无操作权限 |
| 404 | 资源不存在 | 评价不存在 |
| 409 | 状态冲突 | 评价已完成 |
| 413 | 文件过大 | 超过上传限制 |
| 415 | 文件类型不支持 | 不允许的文件格式 |
| 500 | 服务器内部错误 | 系统异常 |

### **错误响应格式**

```json
{
  "success": false,
  "code": 400,
  "message": "评价ID不能为空",
  "timestamp": 1749188994768
}
```

### **前端错误处理示例**

```typescript
async function handleApiCall<T>(apiCall: Promise<FyfcApiResponseDto<T>>): Promise<T> {
  try {
    const response = await apiCall;
    
    if (response.success) {
      return response.data!;
    } else {
      throw new Error(response.message);
    }
  } catch (error) {
    if (error.code === 401) {
      // 重定向到登录页
      window.location.href = '/login';
    } else if (error.code === 403) {
      showMessage('权限不足', 'error');
    } else {
      showMessage(error.message || '操作失败', 'error');
    }
    throw error;
  }
}
```

---

## 🎯 **最佳实践**

### **1. API 调用**

```typescript
// 推荐的 API 调用方式
class FyfcEvaluationService {
  private baseUrl = '/fyschedule2/api/fyfc/evaluation';
  
  async searchEvaluations(query: FyfcEvaluationQueryDto): Promise<FyfcPaginatedResponseDto<FyfcEvaluationDto>> {
    const params = new URLSearchParams();
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, String(value));
      }
    });
    
    const response = await fetch(`${this.baseUrl}/admin/search?${params}`);
    const result = await response.json();
    
    if (!result.success) {
      throw new Error(result.message);
    }
    
    return result.data;
  }
  
  async submitEvaluation(submitDto: FyfcEvaluationScoreSubmitDto): Promise<string> {
    const response = await fetch(`${this.baseUrl}/staff/submit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(submitDto),
    });
    
    const result = await response.json();
    
    if (!result.success) {
      throw new Error(result.message);
    }
    
    return result.data;
  }
}
```

### **2. 文件上传**

```typescript
class FyfcOssService {
  private baseUrl = '/fyschedule2/api/fyfc/oss';
  
  async uploadFile(file: File, evaluationId: number, uploadBy: string): Promise<FyfcAttachment> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('evaluationId', evaluationId.toString());
    formData.append('uploadBy', uploadBy);
    
    const response = await fetch(`${this.baseUrl}/upload`, {
      method: 'POST',
      body: formData,
    });
    
    const result = await response.json();
    
    if (!result.success) {
      throw new Error(result.message);
    }
    
    return result.data;
  }
  
  async uploadFiles(files: File[], evaluationId: number, uploadBy: string): Promise<FyfcAttachment[]> {
    const formData = new FormData();
    files.forEach(file => formData.append('files', file));
    formData.append('evaluationId', evaluationId.toString());
    formData.append('uploadBy', uploadBy);
    
    const response = await fetch(`${this.baseUrl}/upload/batch`, {
      method: 'POST',
      body: formData,
    });
    
    const result = await response.json();
    
    if (!result.success) {
      throw new Error(result.message);
    }
    
    return result.data;
  }
}
```

### **3. 状态管理**

```typescript
// 评价状态流转管理
class EvaluationStatusManager {
  private statusFlow: Record<EvaluationStatus, EvaluationStatus[]> = {
    'PENDING': ['SELF'],
    'SELF': ['COLLEAGUE'],
    'COLLEAGUE': ['MANAGER'],
    'MANAGER': ['COMPLETED'],
    'COMPLETED': []
  };
  
  canTransitionTo(currentStatus: EvaluationStatus, targetStatus: EvaluationStatus): boolean {
    return this.statusFlow[currentStatus]?.includes(targetStatus) || false;
  }
  
  getNextStatus(currentStatus: EvaluationStatus): EvaluationStatus | null {
    const nextStatuses = this.statusFlow[currentStatus];
    return nextStatuses?.length > 0 ? nextStatuses[0] : null;
  }
  
  getStatusLabel(status: EvaluationStatus): string {
    const labels: Record<EvaluationStatus, string> = {
      'PENDING': '待开始',
      'SELF': '自评中',
      'COLLEAGUE': '同事评价中',
      'MANAGER': '经理评价中',
      'COMPLETED': '已完成'
    };
    return labels[status] || status;
  }
}
```

### **4. 数据验证**

```typescript
// 前端数据验证
class FyfcValidator {
  static validateEvaluationScore(score: number): string | null {
    if (score < 0 || score > 100) {
      return '评分必须在0-100之间';
    }
    if (!/^\d+(\.\d{1,2})?$/.test(score.toString())) {
      return '评分最多保留两位小数';
    }
    return null;
  }
  
  static validateFile(file: File): string | null {
    const maxSize = 50 * 1024 * 1024; // 50MB
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'image/jpeg',
      'image/png'
    ];
    
    if (file.size > maxSize) {
      return '文件大小不能超过50MB';
    }
    
    if (!allowedTypes.includes(file.type)) {
      return '不支持的文件类型';
    }
    
    return null;
  }
}
```

---

## 🎉 **总结**

FYFC 评价系统 API 提供了完整的评价管理功能：

1. ✅ **完整的评价流程**：从创建到完成的全流程支持
2. ✅ **灵活的文件管理**：支持多种文件类型的上传和管理
3. ✅ **统一的响应格式**：便于前端统一处理
4. ✅ **完善的错误处理**：提供详细的错误信息和处理建议
5. ✅ **类型安全**：完整的 TypeScript 类型定义

通过这套 API，可以构建功能完整、用户体验良好的评价系统前端应用！🚀
