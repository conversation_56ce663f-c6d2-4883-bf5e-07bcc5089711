package cn.fyg.schedule.pojo.project.management;

import cn.fyg.schedule.pojo.dto.project.management.FProjectDto;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Setter
@Getter
@ToString
@Table(name = "f_project")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class FProject {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    private String name;
    private String creator;
    private String lastEditor;
    @Column(name = "create_date")
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @Column(name = "update_date")
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;
    private Integer dataStatus;
    private String remarks;
    private String easKey;
    private String easNumber;
    private String inManager;

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "parent_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    @ToString.Exclude
    List<FProjectStaff> staffs = new ArrayList<>();

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "parent_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    @ToString.Exclude
    List<FProjectProgress> progresses = new ArrayList<>();

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "parent_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    @ToString.Exclude
    List<FProjectObjective> objectives = new ArrayList<>();

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "parent_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    @ToString.Exclude
    List<FProjectItem> items = new ArrayList<>();

    public static FProject initialize(FProjectDto dto) {
        FProject data = new FProject();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        data.setCreator(dto.getCreator());
        if (dto.getCreateDate() != null) {
            data.setCreateDate(DateUtil.date(dto.getCreateDate()));
        }
        data.setDataStatus(dto.getDataStatus());
        data.setLastEditor(dto.getLastEditor());
        data.setName(dto.getName());
        data.setRemarks(dto.getRemarks());
        data.setEasKey(dto.getEasKey());
        data.setEasNumber(dto.getEasNumber());
        data.setInManager(dto.getInManager());
        return data;
    }
}
