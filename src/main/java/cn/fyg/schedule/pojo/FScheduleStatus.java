package cn.fyg.schedule.pojo;

import cn.fyg.schedule.enums.ScheduleStatusEnum;
import cn.fyg.schedule.pojo.dto.FScheduleStatusDto;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "fschedulestatus")
public class FScheduleStatus {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Column(name = "schedule_id")
    private Integer scheduleId;
    @Column(name = "name")
    private String name;
    @Column(name = "number")
    private String number;
    @Column(name = "create_date")
    @CreationTimestamp
    @Temporal(TemporalType.DATE)
    private Date createDate;

    public static FScheduleStatus initialize(FScheduleStatusDto dto) {
        FScheduleStatus fScheduleStatus = new FScheduleStatus();
        if (dto.getId() != null) {
            fScheduleStatus.setId(dto.getId());
        }
        fScheduleStatus.setScheduleId(dto.getScheduleId());
        fScheduleStatus.setName(ScheduleStatusEnum.valueOf(dto.getValue()).getName());
        fScheduleStatus.setNumber(ScheduleStatusEnum.valueOf(dto.getValue()).getNumber());
        return fScheduleStatus;
    }
}
