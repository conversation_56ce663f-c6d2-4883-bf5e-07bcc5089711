package cn.fyg.schedule.specification.construction;

import cn.fyg.schedule.pojo.construction.FyConstructionLicense;
import cn.fyg.schedule.utils.SpecificationUtil;
import org.springframework.data.jpa.domain.Specification;

import java.util.Date;

public class FyConstructionLicenseSpecification {
    public static Specification<FyConstructionLicense> ifContain(String column, String expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.like(root.get(column), SpecificationUtil.contains(expression)));
    }

    public static Specification<FyConstructionLicense> ifDateEq(String column, Date expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }

    public static Specification<FyConstructionLicense> ifDateBetween(String column, Date startDate, Date endDate) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.between(root.get(column), startDate, endDate));
    }

    public static Specification<FyConstructionLicense> ifDateOverThan(String column, Date date) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.greaterThan(root.get(column), date));
    }

    public static Specification<FyConstructionLicense> ifDateLessThan(String column, Date date) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.lessThan(root.get(column), date));
    }

    public static Specification<FyConstructionLicense> ifDateOverThanOrEqualTo(String column, Date date) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.greaterThanOrEqualTo(root.get(column), date));
    }

    public static Specification<FyConstructionLicense> ifDateLessThanOrEqualTo(String column, Date date) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.lessThanOrEqualTo(root.get(column), date));
    }

    public static Specification<FyConstructionLicense> ifEq(String column, String expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }

    public static Specification<FyConstructionLicense> ifEq(String column, Integer expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }

    public static Specification<FyConstructionLicense> ifNumberBetween(String column, Long min, Long max) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.between(root.get(column), min, max));
    }

    public static Specification<FyConstructionLicense> ifNumberOverThanOrEqualTo(String column, Long num) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.greaterThanOrEqualTo(root.get(column), num));
    }

    public static Specification<FyConstructionLicense> ifNumberLessThanOrEqualTo(String column, Long num) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.lessThan(root.get(column), num));
    }

    public static Specification<FyConstructionLicense> ifNumberOverThan(String column, Long num) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.greaterThanOrEqualTo(root.get(column), num));
    }

    public static Specification<FyConstructionLicense> ifNumberLessThan(String column, Long num) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.lessThan(root.get(column), num));
    }
    public static Specification<FyConstructionLicense> ifNumberEqualTo(String column, Long num) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.lessThan(root.get(column), num));
    }

}
