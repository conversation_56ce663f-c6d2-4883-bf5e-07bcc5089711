package cn.fyg.schedule.service.fyg.accounting.impl;

import cn.fyg.schedule.dao.fyg.accounting.FygProjectsRepository;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.dto.IntegerOptionObj;
import cn.fyg.schedule.pojo.dto.fyg.accounting.projects.ProjectDTO;
import cn.fyg.schedule.pojo.dto.fyg.accounting.projects.ProjectQuery;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.pojo.fyg.accounting.FygProjects;
import cn.fyg.schedule.reponse.BaseResponse;
import cn.fyg.schedule.service.fyg.accounting.FygProjectsService;
import cn.fyg.schedule.specification.BaseSpecification;
import cn.fyg.schedule.utils.SpecificationUtil;
import cn.fyg.schedule.utils.ToEntityUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;

@Service
@Slf4j
public class FygProjectsServiceImpl implements FygProjectsService {
    private final FygProjectsRepository r;

    public FygProjectsServiceImpl(FygProjectsRepository r) {
        this.r = r;
    }

    @Override
    public BaseResponse<ProjectDTO> findById(Integer id) {
        BaseResponse<ProjectDTO> resp = new BaseResponse<>();
        try {
            FygProjects data = getById(id);
            resp.initialize(200, "查询成功", toDto(data));
        } catch (MyException e) {
            resp.initialize(e.getCode(), e.getErrMsg(), null);
        }
        return resp;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse<ProjectDTO> save(ProjectDTO value) {
        BaseResponse<ProjectDTO> resp = BaseResponse.initialized();
        try {
            FygProjects entity = toEntity(value);
            if (entity == null) {
                resp.setCode(5001);
                resp.setMessage("数据转换失败");
            } else {
                try {
                    FygProjects data = save(entity);
                    resp.initialize(200, "保存成功", toDto(data));
                } catch (MyException e) {
                    resp.initialize(e.getCode(), e.getErrMsg(), null);
                }
            }
        } catch (Exception e) {
            resp.initialize(5001, "保存失败", null);
        }

        return resp;
    }

    @Override
    public BaseResponse<Page<ProjectDTO>> filter(ProjectQuery query, Pagination pagination) {
        BaseResponse<Page<ProjectDTO>> resp = new BaseResponse<>();
        try {
            Sort sort = getSort(pagination);
            Pageable pageable = PageRequest.of(pagination.getCurrentPage(), pagination.getPageSize(), sort);
            Specification<FygProjects> specification = getSpecification(query);
            Page<FygProjects> all = getData(specification, pageable);
            Page<ProjectDTO> result = all.map(this::toDto);
            resp.initialize(200, "查询成功", result);
        } catch (MyException e) {
            resp.initialize(e.getCode(), e.getErrMsg(), null);
        }
        return resp;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse<ProjectDTO> save(ProjectDTO value, Integer id) {
        BaseResponse<ProjectDTO> resp = BaseResponse.initialized();
        try {
            FygProjects data = update(value, id);
            resp.initialize(200, "更新成功", toDto(data));
        } catch (MyException e) {
            resp.initialize(e.getCode(), e.getErrMsg(), null);
        }
        return resp;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse<ProjectDTO> setDataStatus(Integer id, Integer dataStatus, String updatedBy) {
        BaseResponse<ProjectDTO> resp = BaseResponse.initialized();
        try {
            int i = r.updateDataStatus(id, dataStatus, updatedBy);
            if (i > 0) {
                resp.initialize(200, "删除成功", null);
            } else {
                resp.initialize(3001, "删除失败", null);
            }
        } catch (JpaSystemException e) {
            resp.initialize(3001, "删除失败", null);
        }
        return resp;
    }

    private FygProjects toEntity(ProjectDTO dto) throws MyException {
        FygProjects entity = new FygProjects();
        return ToEntityUtil.DTOtoEntity(dto, entity);
    }

    /**
     * 将FygProjects实体对象转换为ProjectDTO数据传输对象。
     *
     * @param entity FygProjects实体对象，包含项目的详细信息。
     * @return 转换后的ProjectDTO对象，包含与entity相同的数据但结构不同的DTO对象。
     */
    private ProjectDTO toDto(FygProjects entity) {
        ProjectDTO dto = new ProjectDTO();
        return ToEntityUtil.entityToDTO(entity, dto);
    }

    private FygProjects save(FygProjects entity) throws MyException {
        try {
            return r.save(entity);
        } catch (JpaSystemException e) {
            log.error("保存失败", e);
            throw new MyException(3001, "保存失败");
        }
    }

    private Sort getSort(Pagination pagination) {
        if (StrUtil.isEmpty(pagination.getColumnKey()) || "false".equals(pagination.getOrder())) {
            return Sort.unsorted();
        }
        Sort.Direction direction = "ascend".equalsIgnoreCase(pagination.getOrder()) ? Sort.Direction.ASC : Sort.Direction.DESC;
        return Sort.by(direction, pagination.getColumnKey());
    }

    private Specification<FygProjects> getSpecification(ProjectQuery query) {
        Specification<FygProjects> specification = Specification.where(null);
        specification = BaseSpecification.addSpecification(specification, "dataStatus", query.getDataStatus(), BaseSpecification::equals);
        specification = BaseSpecification.addSpecification(specification, "accountant", query.getAccountant(), BaseSpecification::equals);
        specification = BaseSpecification.addSpecification(specification, "company", query.getCompany(), BaseSpecification::like);
        specification = BaseSpecification.addSpecification(specification, "projectName", query.getProjectName(), BaseSpecification::like);
        specification = BaseSpecification.addSpecification(
                specification, "projectLocation", query.getProjectLocation(), BaseSpecification::like);
        specification = BaseSpecification.addSpecificationFromJson(specification, "projectType", query.getProjectTypeJsonStr(), SpecificationUtil::isInFromJson);
        specification = BaseSpecification.addSpecificationFromJson(
                specification, "landAcquisitionDate", query.getLandAcquisitionDateJsonStr(), SpecificationUtil::timestampRangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(specification, "openingDate", query.getOpeningDateJsonStr(), SpecificationUtil::timestampRangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(
                specification, "completionPreparationDate", query.getCompletionPreparationDateJsonStr(), SpecificationUtil::timestampRangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(
                specification, "handoverDate", query.getHandoverDateJsonStr(), SpecificationUtil::timestampRangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(
                specification, "landTransferFee", query.getLandTransferFeeJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(
                specification, "landArea", query.getLandAreaJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(
                specification, "totalConstructionArea", query.getTotalConstructionAreaJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(
                specification, "totalInvestment", query.getTotalInvestmentJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecification(specification, "fundsInvested", query.getFundsInvestedJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(
                specification, "projectLoanBalance", query.getProjectLoanBalanceJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(
                specification, "soldArea", query.getSoldAreaJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(
                specification, "sellableArea", query.getSellableAreaJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(specification, "estimatedTotalValue", query.getEstimatedTotalValueJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(
                specification, "salesAmount", query.getSalesAmountJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(specification, "buybackAmount", query.getBuybackAmountJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(specification, "fundsRecovered", query.getFundsRecoveredJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecification(specification, "projectProgress", query.getProjectProgress(), BaseSpecification::like);
        specification = BaseSpecification.addSpecificationFromJson(specification, "recoverableFunds", query.getRecoverableFundsJsonStr(), SpecificationUtil::rangeFromJson);
        specification = BaseSpecification.addSpecificationFromJson(specification, "fangyuanShareRatio", query.getFangyuanShareRatioJsonStr(), SpecificationUtil::rangeFromJson);
        return specification;
    }

    private Page<FygProjects> getData(Specification<FygProjects> specification, Pageable pageable) throws MyException {
        try {
            return r.findAll(specification, pageable);
        } catch (JpaSystemException e) {
            throw new MyException(5001, "查询失败");
        }
    }

    private FygProjects update(ProjectDTO value, Integer id) throws MyException {
        try {
            FygProjects source = toEntity(value);
            FygProjects target = getById(id);
            BeanUtils.copyProperties(source, target, "id", "createdAt", "createdBy");
            return r.save(target);
        } catch (JpaSystemException e) {
            log.error("更新项目失败", e);
            throw new MyException(3001, "更新项目失败");
        }
    }

    private FygProjects getById(Integer id) throws MyException {
        try {
            return r.findById(id).orElseThrow(() -> new MyException(5001, "未找到该项目"));
        } catch (JpaSystemException e) {
            throw new MyException(5001, "id不存在");
        }
    }

    @Override
    public BaseResponse<List<IntegerOptionObj>> listOptions(Integer dataStatus) {
        BaseResponse<List<IntegerOptionObj>> resp = new BaseResponse<>();
        try {
            List<IntegerOptionObj> data = r.getOptionObj(dataStatus);
            resp.initialize(200, "查询成功", data);
        } catch (JpaSystemException e) {
            log.error("查询失败", e);
            resp.initialize(3001, "查询失败", null);
        }
        return resp;
    }
}
