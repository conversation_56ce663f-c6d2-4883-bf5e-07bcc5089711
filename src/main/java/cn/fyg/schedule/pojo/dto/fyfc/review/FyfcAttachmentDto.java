package cn.fyg.schedule.pojo.dto.fyfc.review;

import lombok.Data;

/**
 * FYFC 附件 DTO
 */
@Data
public class FyfcAttachmentDto {
    
    /**
     * 附件ID（用于前端标识）
     */
    private String id;
    
    /**
     * 原始文件名
     */
    private String fileName;
    
    /**
     * OSS 存储的文件键
     */
    private String fileKey;

    /**
     * OSS Bucket 名称
     */
    private String bucketName;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;
    
    /**
     * 文件类型/MIME类型
     */
    private String fileType;
    
    /**
     * 文件URL（用于下载/预览）
     */
    private String fileUrl;
    
    /**
     * 上传时间戳
     */
    private Long uploadTime;
    
    /**
     * 上传人
     */
    private String uploadBy;
}
