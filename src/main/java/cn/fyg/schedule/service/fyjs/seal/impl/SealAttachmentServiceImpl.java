package cn.fyg.schedule.service.fyjs.seal.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.fyjs.sealManagement.FJsSealAttachmentDao;
import cn.fyg.schedule.pojo.project.fyjs.sealManagement.FJsSealAttachment;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.fyjs.seal.SealAttachmentService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SealAttachmentServiceImpl implements SealAttachmentService {
    private final FJsSealAttachmentDao r;

    public SealAttachmentServiceImpl(FJsSealAttachmentDao r) {
        this.r = r;
    }

    @Override
    public BaseResponse save(FJsSealAttachment data) {
        return BaseService.save(data, r, FJsSealAttachment.class);
    }

    @Override
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findByParentId(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FJsSealAttachment> list = r.findBySealIdOrderByCreateDate(id);
            baseResponse.setResult(list);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg("查询出错");
        }
        return baseResponse;
    }
}
