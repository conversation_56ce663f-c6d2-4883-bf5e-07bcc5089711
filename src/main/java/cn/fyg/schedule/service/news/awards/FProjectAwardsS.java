package cn.fyg.schedule.service.news.awards;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.news.awads.FProjectAwards;

import java.util.Date;
import java.util.List;

public interface FProjectAwardsS {
    BaseResponse save(FProjectAwards data);
    BaseResponse findById(Integer id);
    BaseResponse delete(Integer id);
    BaseResponse getCompanyHistoryList();
    BaseResponse listAll();
    BaseResponse listByAwardDate(Date begin, Date end);
    BaseResponse listCompanyIn(List<String> companies);
}
