package cn.fyg.schedule.pojo.project.management;

import cn.fyg.schedule.pojo.dto.project.management.FProjectObjectiveDto;
import cn.hutool.core.date.DateUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.util.Date;

@Entity
@Setter
@Getter
@ToString
@Table(name = "f_project_objective")
public class FProjectObjective {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Column(name = "parent_id")
    private Integer parentId;
    private String type;
    private String name;
    @Column(name = "achieve_date")
    @Temporal(TemporalType.DATE)
    private Date achieveDate;
    @Column(name = "create_date")
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;

    public static FProjectObjective initialized(FProjectObjectiveDto dto) {
        FProjectObjective data = new FProjectObjective();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        if (dto.getCreateDate() != null) {
            data.setCreateDate(DateUtil.date(dto.getCreateDate()));
        }
        if (dto.getAchieveDate() != null) {
            data.setAchieveDate((DateUtil.date(dto.getAchieveDate())));
        }
        data.setParentId(dto.getParentId());
        data.setType(dto.getType());
        data.setName(dto.getName());
        return data;
    }
}
