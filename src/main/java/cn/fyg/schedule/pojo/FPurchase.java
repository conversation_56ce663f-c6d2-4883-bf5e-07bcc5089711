package cn.fyg.schedule.pojo;

import cn.fyg.schedule.pojo.dto.FPurchaseDto;
import cn.hutool.core.date.DateUtil;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "fpurchase")
public class FPurchase {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Column(name = "number")
    private String number;
    @Column(name = "creator")
    private String creator;
    @Column(name = "department_name")
    private String departmentName;
    @Column(name = "project_belong")
    private String projectBelong;
    @Column(name = "apply_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date applyDate;
    @Column(name = "store_place")
    private String storePlace;
    @Column(name = "fee_type")
    private String feeType;
    @Column(name = "purpose_expain")
    private String purposeExpain;
    @Column(name = "create_date")
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @Column(name = "update_date")
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;

    public static FPurchase initialize(FPurchaseDto dto) {
        FPurchase fPurchase = new FPurchase();
        if (dto.getId() != null) {
            fPurchase.setId(dto.getId());
        }
        fPurchase.setCreator(dto.getCreator());
        fPurchase.setNumber(dto.getNumber());
        fPurchase.setDepartmentName(dto.getDepartmentName());
        fPurchase.setProjectBelong(dto.getProjectBelong());
        fPurchase.setApplyDate(DateUtil.parse(dto.getApplyDate()));
        fPurchase.setStorePlace(dto.getStorePlace());
        fPurchase.setFeeType(dto.getFeeType());
        fPurchase.setPurposeExpain(dto.getPurposeExpain());
        if (dto.getCreateDate() != null) {
            fPurchase.setCreateDate(DateUtil.parse(dto.getCreateDate()));
        }
        return fPurchase;
    }
}
