package cn.fyg.schedule.pojo.fyjs.social.insurance;

import cn.fyg.schedule.pojo.dto.fyjs.social.insurance.FyjsSocialInsuranceDto;
import cn.fyg.schedule.pojo.fyjs.staff.FyjsStaffBaseInfo;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.Filter;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Setter
@Getter
@ToString
@Table(name = "fyjs_social_insurance")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class FyjsSocialInsurance {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Column(name = "staff_name")
    private String staffName;
    @Column(name = "staff_id_card")
    private String staffIdCard;
    @Column(name = "initial_enrollment")
    @Temporal(TemporalType.DATE)
    private Date initialEnrollment;
    @Column(name = "insured_company")
    private String insuredCompany;
    @Column(name = "insurance_type")
    private String insuranceType;
    @Column(name = "insurance_end")
    @Temporal(TemporalType.DATE)
    private Date insuranceEnd;
    @Column(name = "contact_person")
    private String contactPerson;
    @Column(name = "contact_person_phone")
    private String contactPersonPhone;
    @Column(name = "in_full_or_normal")
    private Integer inFullOrNormal; //全额/正常 0 全额 1 正常
    @Temporal(TemporalType.DATE)
    @Column(name = "payment_dead_line")
    private Date paymentDeadLine;
    @Column(name = "payment_base")
    private BigDecimal paymentBase;
    @Column(name = "endowment")
    private BigDecimal endowment; //养老
    @Column(name = "medicare")
    private BigDecimal medicare;
    @Column(name = "unemployment")
    private BigDecimal unemployment;
    @Column(name = "employment_injury")
    private BigDecimal employmentInjury;
    @Column(name = "temporary_participation")
    private String temporaryParticipation; //临时参保
    @Column(name = "balance")
    private BigDecimal balance;
    @Column(name = "reserved_fund")
    private BigDecimal reservedFund;
    @Column(name = "re_fund_start_date")
    @Temporal(TemporalType.DATE)
    private Date reFundStartDate;
    @Column(name = "re_fund_end_date")
    @Temporal(TemporalType.DATE)
    private Date reFundEndDate;
    @Column(name = "labour_union_fees")
    private BigDecimal labourUnionFees;
    @Column(name = "account_receivable")
    private BigDecimal accountReceivable;
    private String remarks;
    private Integer gender; //0 女 1 男 2 其他
    private Integer age;
    @Column(name = "birth")
    @Temporal(TemporalType.DATE)
    private Date birth;
    @Column(name = "household_registration")
    private String householdRegistration;
    @Column(name = "household_registration_type")
    private String householdRegistrationType;
    @Column(name = "academic_career")
    private String academicCareer;
    @Column(name = "politics_status")
    private String politicsStatus;
    @Column(name = "phone_number")
    private String phoneNumber;
    @Column(name = "entry_date")
    @Temporal(TemporalType.DATE)
    private Date entryDate;
    @Column(name = "date_become_regular")
    @Temporal(TemporalType.DATE)
    private Date dateBecomeRegular;
    @Column(name = "belong_to")
    private String belongTo;
    @Column(name = "age_in_fy")
    private Integer ageInFy;
    @Column(name = "staff_property")
    private String staffProperty;
    @Column(name = "is_on_post")
    private Integer isOnPost; // 0 否 1 是
    @Column(name = "staff_remarks")
    private String staffRemarks;
    @Column(name = "creator")
    private String creator;
    @Column(name = "creator_name")
    private String creatorName;
    private String lastEditor;
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;
    private Integer dataStatus;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "staff_id_card", referencedColumnName = "staff_id_card", nullable = false, insertable = false, updatable = false)
    @Filter(name="data_status", condition="0")
    @ToString.Exclude
    FyjsStaffBaseInfo staffInfo;

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "parent_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
//    @JsonIgnore
    @ToString.Exclude
    List<FyjsSocialInsuranceItem> items = new ArrayList<>();

    public static FyjsSocialInsurance initialized(FyjsSocialInsuranceDto dto) {
        FyjsSocialInsurance data = new FyjsSocialInsurance();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        data.setCreator(dto.getCreator());
        data.setCreatorName(dto.getCreatorName());
        if (dto.getCreateDate() != null) {
            data.setCreateDate(DateUtil.date(data.createDate));
        }
        data.setDataStatus(dto.getDataStatus());
        data.setLastEditor(dto.getLastEditor());
        data.setStaffName(dto.getStaffName());
        data.setStaffIdCard(dto.getStaffIdCard());
        if (dto.getInitialEnrollment() != null) {
            data.setInitialEnrollment(DateUtil.date(dto.getInitialEnrollment()));
        }
        data.setInsuredCompany(dto.getInsuredCompany());
        data.setInsuranceType(dto.getInsuranceType());
        if (dto.getInsuranceEnd() != null) {
            data.setInsuranceEnd(DateUtil.date(dto.getInsuranceEnd()));
        }
        data.setContactPerson(dto.getContactPerson());
        data.setContactPersonPhone(dto.getContactPersonPhone());
        data.setInFullOrNormal(dto.getInFullOrNormal());
        if (dto.getPaymentDeadLine() != null) {
            data.setPaymentDeadLine(DateUtil.date(dto.getPaymentDeadLine()));
        }
        data.setPaymentBase(dto.getPaymentBase());
        data.setEndowment(dto.getEndowment());
        data.setMedicare(dto.getMedicare());
        data.setUnemployment(dto.getUnemployment());
        data.setEmploymentInjury(dto.getEmploymentInjury());
        data.setTemporaryParticipation(dto.getTemporaryParticipation());
        data.setBalance(dto.getBalance());
        data.setReservedFund(dto.getReservedFund());
        if (dto.getReFundStartDate() != null) {
            data.setReFundStartDate(DateUtil.date(dto.getReFundStartDate()));
        }
        if (dto.getReFundEndDate() != null) {
            data.setReFundEndDate(DateUtil.date(dto.getReFundEndDate()));
        }
        data.setLabourUnionFees(dto.getLabourUnionFees());
        data.setAccountReceivable(dto.getAccountReceivable());
        data.setRemarks(dto.getRemarks());
        data.setGender(dto.getGender());
        data.setAge(dto.getAge());
        if (dto.getBirth() != null) {
            data.setBirth(DateUtil.date(dto.getBirth()));
        }
        data.setHouseholdRegistration(dto.getHouseholdRegistration());
        data.setHouseholdRegistrationType(dto.getHouseholdRegistrationType());
        data.setAcademicCareer(dto.getAcademicCareer());
        data.setPoliticsStatus(dto.getPoliticsStatus());
        data.setPhoneNumber(dto.getPhoneNumber());
        if (dto.getEntryDate() != null) {
            data.setEntryDate(DateUtil.date(dto.getEntryDate()));
        }
        if (dto.getDateBecomeRegular() != null) {
            data.setDateBecomeRegular(DateUtil.date(dto.getDateBecomeRegular()));
        }
        data.setBelongTo(dto.getBelongTo());
        data.setAgeInFy(dto.getAgeInFy());
        data.setStaffProperty(dto.getStaffProperty());
        data.setIsOnPost(dto.getIsOnPost());
        data.setStaffRemarks(dto.getStaffRemarks());
        return data;
    }
}
