package cn.fyg.schedule.service.fyfc.review;

import cn.fyg.schedule.pojo.dto.fyfc.review.*;

import java.util.List;

/**
 * FYFC 评价通用服务接口
 * 提供各个模块共用的基础功能
 */
public interface IFyfcEvaluationCommonService {

    /**
     * 根据ID获取评价基本信息
     * @param evaluationId 评价ID
     * @return 统一API响应格式的评价DTO
     */
    FyfcApiResponseDto<FyfcEvaluationDto> getEvaluationById(Integer evaluationId);

    /**
     * 获取评价的所有得分记录
     * @param evaluationId 评价ID
     * @return 统一API响应格式的得分记录列表
     */
    FyfcApiResponseDto<List<FyfcEvaluationScoreDto>> getEvaluationScores(Integer evaluationId);

    /**
     * 获取评价的状态变更历史
     * @param evaluationId 评价ID
     * @return 统一API响应格式的状态历史列表
     */
    FyfcApiResponseDto<List<FyfcEvaluationStatusHistoryDto>> getEvaluationStatusHistory(Integer evaluationId);

    /**
     * 获取部门列表
     * @return 统一API响应格式的部门列表
     */
    FyfcApiResponseDto<List<String>> getDepartmentList();

    /**
     * 获取评价状态选项
     * @return 统一API响应格式的状态选项列表
     */
    FyfcApiResponseDto<List<FyfcEvaluationStatusDto>> getStatusOptions();

    /**
     * 获取用户角色选项
     * @return 统一API响应格式的角色选项列表
     */
    FyfcApiResponseDto<List<FyfcUserRoleDto>> getUserRoleOptions();

    /**
     * 验证评价数据完整性
     * @param evaluationId 评价ID
     * @return 统一API响应格式的验证结果
     */
    FyfcApiResponseDto<Boolean> validateEvaluationData(Integer evaluationId);

    /**
     * 计算评价总分
     * @param evaluationId 评价ID
     * @return 统一API响应格式的总分
     */
    FyfcApiResponseDto<Object> calculateTotalScore(Integer evaluationId);

    /**
     * 获取评价流程状态
     * @param evaluationId 评价ID
     * @return 统一API响应格式的流程状态
     */
    FyfcApiResponseDto<Object> getEvaluationProcess(Integer evaluationId);

    /**
     * 检查评价是否可以编辑
     * @param evaluationId 评价ID
     * @param userName 用户名
     * @return 统一API响应格式的检查结果
     */
    FyfcApiResponseDto<Boolean> canEditEvaluation(Integer evaluationId, String userName);

    /**
     * 获取评价相关的用户列表
     * @param evaluationId 评价ID
     * @return 统一API响应格式的用户列表
     */
    FyfcApiResponseDto<Object> getEvaluationRelatedUsers(Integer evaluationId);

    /**
     * 发送评价通知
     * @param evaluationId 评价ID
     * @param notificationType 通知类型
     * @return 统一API响应格式
     */
    FyfcApiResponseDto<Boolean> sendEvaluationNotification(Integer evaluationId, String notificationType);
}
