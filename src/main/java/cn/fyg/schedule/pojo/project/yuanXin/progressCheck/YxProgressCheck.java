package cn.fyg.schedule.pojo.project.yuanXin.progressCheck;

import cn.fyg.schedule.pojo.dto.project.yx.progressCheck.YxProgressCheckDto;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Setter
@Getter
@ToString
@Table(name = "yx_progress_check")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class YxProgressCheck {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    private String creator;
    private String creatorName;
    @Column(name = "yx_project_name")
    private String yxProjectName;
    @Column(name = "yx_time_limit")
    private Integer yxTimeLimit;
    @Column(name = "yx_problem_description")
    private String yxProblemDescription;
    @Column(name = "create_date")
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @Column(name = "update_date")
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;
    @Column(name = "inspection_date")
    @Temporal(TemporalType.DATE)
    private Date inspectionDate;
    @Column(name = "work_date")
    @Temporal(TemporalType.DATE)
    private Date workDate;
    private String workName;
    private Integer needSign;
    private String lastEditor;
    private Integer dataStatus;
    @Column(name = "event_status")
    private Integer eventStatus;
    private String organization;
    @Column(name = "inspection_department")
    private String inspectionDepartment;

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "parent_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    @JsonIgnore
    @ToString.Exclude
    List<YxProgressCheckItem> items = new ArrayList<>();

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "parent_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    @JsonIgnore
    @ToString.Exclude
    List<YxProgressCheckSign> signatures = new ArrayList<>();

    public static YxProgressCheck initialize(YxProgressCheckDto dto) {
        YxProgressCheck data = new YxProgressCheck();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        data.setCreator(dto.getCreator());
        data.setCreatorName(dto.getCreatorName());
        data.setYxProjectName(dto.getYxProjectName());
        data.setYxTimeLimit(dto.getYxTimeLimit());
        data.setYxProblemDescription(dto.getYxProblemDescription());
        if (dto.getCreateDate() != null) {
            data.setCreateDate(DateUtil.date(dto.getCreateDate()));
        }
        if (dto.getInspectionDate() != null) {
            data.setInspectionDate(DateUtil.date(dto.getInspectionDate()));
        }
        if (dto.getWorkDate() != null) {
            data.setWorkDate(DateUtil.date(dto.getWorkDate()));
        }
        data.setWorkName(dto.getWorkName());
        data.setNeedSign(dto.getNeedSign());
        data.setDataStatus(dto.getDataStatus());
        data.setLastEditor(dto.getLastEditor());
        data.setEventStatus(dto.getEventStatus());
        data.setOrganization(dto.getOrganization());
        data.setInspectionDepartment(dto.getInspectionDepartment());
        return data;
    }
}
