package cn.fyg.schedule.controller.fyfc.review;

import cn.fyg.schedule.config.AliyunOssProperties;
import cn.fyg.schedule.config.FyfcOssUploadProperties;
import cn.fyg.schedule.pojo.dto.fyfc.review.FyfcApiResponseDto;
import cn.fyg.schedule.service.fyfc.review.IFyfcOssService;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * FYFC OSS 诊断控制器
 * 用于诊断 OSS 服务问题
 */
@Slf4j
@RestController
@RequestMapping("/api/fyfc/oss/diagnostic")
public class FyfcOssDiagnosticController {

    private final AliyunOssProperties ossProperties;
    private final FyfcOssUploadProperties uploadProperties;
    private final IFyfcOssService ossService;

    public FyfcOssDiagnosticController(AliyunOssProperties ossProperties,
                                      FyfcOssUploadProperties uploadProperties,
                                      IFyfcOssService ossService) {
        this.ossProperties = ossProperties;
        this.uploadProperties = uploadProperties;
        this.ossService = ossService;
    }

    /**
     * 系统诊断
     */
    @GetMapping("/system")
    public FyfcApiResponseDto<Map<String, Object>> systemDiagnostic() {
        log.info("执行系统诊断...");
        
        Map<String, Object> diagnostic = new HashMap<>();
        
        try {
            // 检查 OSS 配置
            diagnostic.put("ossPropertiesInjected", true);
            Map<String, Object> ossConfig = new HashMap<>();
            ossConfig.put("endpoint", ossProperties.getEndpoint());
            ossConfig.put("bucketName", ossProperties.getBucketName());
            ossConfig.put("accessKeyIdPresent", ossProperties.getAccessKeyId() != null);
            ossConfig.put("accessKeySecretPresent", ossProperties.getAccessKeySecret() != null);
            diagnostic.put("ossProperties", ossConfig);

            // 检查 OSS 服务
            diagnostic.put("ossServiceInjected", true);
            
            // 检查 Spring 环境
            diagnostic.put("springProfileActive", System.getProperty("spring.profiles.active"));
            diagnostic.put("javaVersion", System.getProperty("java.version"));
            diagnostic.put("osName", System.getProperty("os.name"));
            
            return FyfcApiResponseDto.success(diagnostic, "系统诊断完成");
            
        } catch (Exception e) {
            log.error("系统诊断失败", e);
            diagnostic.put("error", e.getMessage());
            diagnostic.put("errorClass", e.getClass().getSimpleName());
            return FyfcApiResponseDto.error("系统诊断失败: " + e.getMessage());
        }
    }

    /**
     * 配置检查
     */
    @GetMapping("/config")
    public FyfcApiResponseDto<Map<String, Object>> configCheck() {
        log.info("执行配置检查...");
        
        Map<String, Object> config = new HashMap<>();
        
        try {
            
            config.put("endpoint", ossProperties.getEndpoint());
            config.put("bucketName", ossProperties.getBucketName());
            config.put("accessKeyId", maskString(ossProperties.getAccessKeyId()));
            config.put("accessKeySecret", maskString(ossProperties.getAccessKeySecret()));
            
            // 验证配置完整性
            boolean configValid = true;
            if (ossProperties.getEndpoint() == null || ossProperties.getEndpoint().trim().isEmpty()) {
                config.put("endpointError", "Endpoint 未配置");
                configValid = false;
            }
            if (ossProperties.getBucketName() == null || ossProperties.getBucketName().trim().isEmpty()) {
                config.put("bucketNameError", "Bucket 名称未配置");
                configValid = false;
            }
            if (ossProperties.getAccessKeyId() == null || ossProperties.getAccessKeyId().trim().isEmpty()) {
                config.put("accessKeyIdError", "Access Key ID 未配置");
                configValid = false;
            }
            if (ossProperties.getAccessKeySecret() == null || ossProperties.getAccessKeySecret().trim().isEmpty()) {
                config.put("accessKeySecretError", "Access Key Secret 未配置");
                configValid = false;
            }
            
            config.put("configValid", configValid);
            
            return FyfcApiResponseDto.success(config, configValid ? "配置检查通过" : "配置检查发现问题");
            
        } catch (Exception e) {
            log.error("配置检查失败", e);
            return FyfcApiResponseDto.error("配置检查失败: " + e.getMessage());
        }
    }

    /**
     * 服务状态检查
     */
    @GetMapping("/service")
    public FyfcApiResponseDto<Map<String, Object>> serviceCheck() {
        log.info("执行服务状态检查...");
        
        Map<String, Object> service = new HashMap<>();
        
        try {
            service.put("ossServiceAvailable", true);
            
            // 尝试调用服务方法
            try {
                // 测试获取不存在的附件（应该返回错误但不抛异常）
                var result = ossService.getEvaluationAttachments(999999);
                service.put("serviceCallable", true);
                service.put("testCallResult", result.getSuccess() ? "成功" : result.getMessage());
            } catch (Exception e) {
                service.put("serviceCallable", false);
                service.put("serviceCallError", e.getMessage());
            }
            
            return FyfcApiResponseDto.success(service, "服务状态检查完成");
            
        } catch (Exception e) {
            log.error("服务状态检查失败", e);
            return FyfcApiResponseDto.error("服务状态检查失败: " + e.getMessage());
        }
    }

    /**
     * 上传配置检查
     */
    @GetMapping("/upload-config")
    public FyfcApiResponseDto<Map<String, Object>> uploadConfigCheck() {
        log.info("执行上传配置检查...");

        Map<String, Object> config = new HashMap<>();

        try {

            config.put("maxFileSize", uploadProperties.getMaxFileSize());
            config.put("formattedMaxFileSize", uploadProperties.getFormattedMaxFileSize());
            config.put("maxBatchTotalSize", uploadProperties.getMaxBatchTotalSize());
            config.put("formattedMaxBatchTotalSize", uploadProperties.getFormattedMaxBatchTotalSize());
            config.put("maxFileCount", uploadProperties.getMaxFileCount());
            config.put("enableTypeCheck", uploadProperties.getEnableTypeCheck());
            config.put("enableSizeCheck", uploadProperties.getEnableSizeCheck());
            config.put("allowedMimeTypes", uploadProperties.getAllowedMimeTypes());
            config.put("allowedExtensions", uploadProperties.getAllowedExtensions());
            config.put("forbiddenMimeTypes", uploadProperties.getForbiddenMimeTypes());
            config.put("forbiddenExtensions", uploadProperties.getForbiddenExtensions());

            return FyfcApiResponseDto.success(config, "上传配置检查完成");

        } catch (Exception e) {
            log.error("上传配置检查失败", e);
            return FyfcApiResponseDto.error("上传配置检查失败: " + e.getMessage());
        }
    }

    /**
     * 简单健康检查
     */
    @GetMapping("/health")
    public FyfcApiResponseDto<String> simpleHealthCheck() {
        log.info("执行简单健康检查...");

        try {
            return FyfcApiResponseDto.success("健康", "OSS 诊断服务运行正常");

        } catch (Exception e) {
            log.error("健康检查失败", e);
            return FyfcApiResponseDto.error("健康检查失败: " + e.getMessage());
        }
    }

    /**
     * 掩码字符串（用于安全显示敏感信息）
     */
    private String maskString(String str) {
        if (str == null || str.length() <= 4) {
            return "****";
        }
        return str.substring(0, 4) + "****" + str.substring(str.length() - 4);
    }
}
