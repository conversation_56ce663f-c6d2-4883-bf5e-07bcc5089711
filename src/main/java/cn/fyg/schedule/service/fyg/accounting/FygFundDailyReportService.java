package cn.fyg.schedule.service.fyg.accounting;

import cn.fyg.schedule.pojo.dto.fyg.accounting.report.DailyReportDTO;
import cn.fyg.schedule.pojo.dto.fyg.accounting.report.EquityStructureDTO;
import cn.fyg.schedule.pojo.dto.fyg.accounting.report.FundSummaryWithNoteDTO;
import cn.fyg.schedule.reponse.BaseResponse;

import java.util.List;

public interface FygFundDailyReportService {
    BaseResponse<List<FundSummaryWithNoteDTO>> getFundSummaryWithNote(Integer projectId, Long transactionDate);
    BaseResponse<DailyReportDTO> save(DailyReportDTO dto);
    BaseResponse<List<EquityStructureDTO>> getEquityStructure(Integer projectId);
}
