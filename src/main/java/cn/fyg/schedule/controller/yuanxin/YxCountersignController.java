package cn.fyg.schedule.controller.yuanxin;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.pojo.dto.yx.countersign.YxCountersignDetailDto;
import cn.fyg.schedule.pojo.dto.yx.countersign.YxCountersignInfoDto;
import cn.fyg.schedule.pojo.yx.countersign.YxCountersignDetail;
import cn.fyg.schedule.service.yx.couthersign.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("yx/countersign")
@Slf4j
public class YxCountersignController {
    private final YxFunctionalDepartmentService yxFunctionalDepartmentService;
    private final YxDepartmentFunctionService yxDepartmentFunctionService;
    private final YxCountersignDetailService yxCountersignDetailService;
    private final YxCounterSignInfoService yxCounterSignInfoService;

    public YxCountersignController(YxFunctionalDepartmentService yxFunctionalDepartmentService, YxAuditMatterService yxAuditMatterService, YxDepartmentFunctionService yxDepartmentFunctionService, YxCountersignDetailService yxCountersignDetailService, YxCounterSignInfoService yxCounterSignInfoService) {
        this.yxFunctionalDepartmentService = yxFunctionalDepartmentService;
        this.yxDepartmentFunctionService = yxDepartmentFunctionService;
        this.yxCountersignDetailService = yxCountersignDetailService;
        this.yxCounterSignInfoService = yxCounterSignInfoService;
    }

    @PostMapping("test")
    public BaseResponse test(Integer parentId, Integer departmentId) {
        BaseResponse response = BaseResponse.initialize();
        try {
            List<Integer> ids = yxDepartmentFunctionService.listDepartmentFunctionId(departmentId);
            Integer count = yxCountersignDetailService.updateAuditor("153", parentId, ids);
            response.setResult(count);
        } catch (MyException e) {
            log.error(e.getErrMsg());
        }
        return response;
    }

    @PostMapping("info/save")
    public BaseResponse saveInfo(YxCountersignInfoDto dto) {
        return yxCounterSignInfoService.save(dto);
    }
    @PostMapping("info/update/sign")
    public BaseResponse updateSign(String sign, Integer id) {
        return yxCounterSignInfoService.update(sign, id);
    }
    @PostMapping("info/update/sign/comments")
    public BaseResponse updateSign(String sign, String comments, Integer id) {
        return yxCounterSignInfoService.update(sign, comments, id);
    }
    @PostMapping("info/delete/id")
    public BaseResponse deleteInfoById(Integer id) {
        return yxCounterSignInfoService.delete(id);
    }
    @PostMapping("info/filter")
    public BaseResponse filterInfo(Pagination pagination, YxCountersignInfoDto dto) {
        return yxCounterSignInfoService.filter(pagination, dto);
    }
    @PostMapping("info/find/id")
    public BaseResponse findInfo(Integer id) {
        return yxCounterSignInfoService.findById(id);
    }
    @PostMapping("detail/save")
    public BaseResponse saveDetail(YxCountersignDetailDto dto) {
        return yxCountersignDetailService.save(dto);
    }
    @PostMapping("detail/update/audit")
    public BaseResponse update(String auditData) {
        return yxCountersignDetailService.updateDetail(auditData);
    }
    @PostMapping("detail/delete/id")
    public BaseResponse deleteDetailById(Integer id) {
        return yxCountersignDetailService.delete(id);
    }

    /**
     * delete countersign detail by countersign id
     * @param id
     * @return
     */
    @PostMapping("detail/delete/parent")
    public BaseResponse deleteDetailByParent(Integer id) {
        return yxCountersignDetailService.deleteByParentId(id);
    }
    @PostMapping("detail/list/parent")
    public BaseResponse listDetailByParentId(Integer id) {
        return yxCountersignDetailService.listByParentId(id);
    }

    @PostMapping("detail/list/auditor/parent")
    public BaseResponse listAuditor(Integer parentId) {
        return yxCountersignDetailService.listAuditorByParentId(parentId);
    }
    @PostMapping("department/function/list/department")
    public BaseResponse listDepartmentFunctionByDepartmentId(Integer id) {
        return yxDepartmentFunctionService.listByDepartmentId(id);
    }

    @PostMapping("functional/department/list")
    public BaseResponse listFunctionalDepartment() {
        return yxFunctionalDepartmentService.listAll();
    }

}
