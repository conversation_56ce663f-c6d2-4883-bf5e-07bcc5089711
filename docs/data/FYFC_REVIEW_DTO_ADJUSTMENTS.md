# FYFC Review DTO 调整说明

## 📋 调整概述

根据 TypeScript 类型定义，已对所有 DTO 进行调整，确保前后端数据结构完全一致。主要调整包括时间字段类型、新增业务相关的 DTO 类，以及完善 API 响应结构。

## 🔄 主要变更

### **时间字段调整**
- 所有时间相关字段从 `Date` 改回 `Long` (时间戳)
- 与 TypeScript 中的 `number` 类型保持一致
- 便于前端处理和格式化

### **核心 DTO 调整**

#### **1. FyfcEvaluationDto.java**
```java
// 新增字段
private List<FyfcEvaluationScoreDto> scores;  // 评价得分列表
private Long reviewDate;                      // 时间戳格式
private Long createdAt;                       // 时间戳格式

// 字段重新排序，匹配 TypeScript 接口
```

#### **2. FyfcEvaluationScoreDto.java**
```java
// 移除时间字段，简化结构
// 保留核心评分字段
private String type;  // 对应 UserRole 枚举
```

#### **3. FyfcEvaluationSummaryDto.java**
```java
// 时间字段调整为时间戳
private Long reviewDate;
private Long createdAt;
// 移除 updatedAt，简化结构
```

#### **4. FyfcEvaluationStatusHistoryDto.java**
```java
// 时间字段调整
private Long changedAt;  // 时间戳格式
// 移除额外的显示字段，保持简洁
```

### **新增 DTO 类**

#### **1. FyfcEvaluationFormDto.java**
- 对应 TypeScript 的 `EvaluationForm` 类型
- 用于创建评价的表单数据
- 包含基础评价信息字段

#### **2. FyfcScoreFormDto.java**
- 对应 TypeScript 的 `ScoreForm` 类型
- 用于提交评分的表单数据
- 包含各维度得分字段

#### **3. FyfcUserRoleDto.java**
- 对应 TypeScript 的 `UserRole` 枚举
- 定义用户角色类型
- 包含预定义角色常量

#### **4. FyfcEvaluationStatusDto.java**
- 对应 TypeScript 的 `EvaluationStatus` 枚举
- 定义评价状态类型
- 包含状态流转顺序

#### **5. FyfcApiResponseDto.java**
- 对应 TypeScript 的 `ApiResponse<T>` 类型
- 统一 API 响应格式
- 包含成功/失败响应构造方法

#### **6. FyfcPaginatedResponseDto.java**
- 对应 TypeScript 的 `PaginatedResponse<T>` 类型
- 分页数据响应格式
- 包含分页状态计算

#### **7. FyfcEvaluationQueryDto.java**
- 对应 TypeScript 的 `EvaluationQuery` 类型
- 查询条件封装
- 支持多种查询场景

## ✅ **调整后的优势**

### **1. 类型一致性**
- 前后端时间字段统一使用时间戳
- 枚举类型与 TypeScript 完全对应
- 数据结构完全匹配

### **2. 业务完整性**
- 覆盖所有业务场景的 DTO
- 表单提交、查询、响应分离
- 支持分页和统计功能

### **3. 开发效率**
- 减少前后端联调问题
- 类型安全，减少运行时错误
- 清晰的数据结构定义

### **4. 可维护性**
- 每个 DTO 职责单一
- 预定义常量减少硬编码
- 完善的注释说明

## 📁 **文件清单**

### **调整的文件**
- `FyfcEvaluationDto.java` - 主评价 DTO
- `FyfcEvaluationScoreDto.java` - 评分 DTO
- `FyfcEvaluationSummaryDto.java` - 汇总 DTO
- `FyfcEvaluationStatusHistoryDto.java` - 状态历史 DTO

### **新增的文件**
- `FyfcEvaluationFormDto.java` - 评价表单 DTO
- `FyfcScoreFormDto.java` - 评分表单 DTO
- `FyfcUserRoleDto.java` - 用户角色 DTO
- `FyfcEvaluationStatusDto.java` - 评价状态 DTO
- `FyfcApiResponseDto.java` - API 响应 DTO
- `FyfcPaginatedResponseDto.java` - 分页响应 DTO
- `FyfcEvaluationQueryDto.java` - 查询条件 DTO

## 🎯 **使用建议**

### **1. API 设计**
```java
// 统一使用 FyfcApiResponseDto 包装响应
@PostMapping("/evaluations")
public FyfcApiResponseDto<FyfcEvaluationDto> createEvaluation(@RequestBody FyfcEvaluationFormDto form) {
    // 业务逻辑
    return FyfcApiResponseDto.success(result);
}

// 分页查询使用 FyfcPaginatedResponseDto
@PostMapping("/evaluations/search")
public FyfcApiResponseDto<FyfcPaginatedResponseDto<FyfcEvaluationDto>> searchEvaluations(@RequestBody FyfcEvaluationQueryDto query) {
    // 查询逻辑
    return FyfcApiResponseDto.success(paginatedResult);
}
```

### **2. 时间处理**
```java
// 前端传递时间戳，后端转换为 Date
Date reviewDate = new Date(dto.getReviewDate());

// 后端返回时间戳给前端
dto.setCreatedAt(entity.getCreatedAt().getTime());
```

### **3. 枚举使用**
```java
// 使用预定义常量
FyfcUserRoleDto.EMPLOYEE
FyfcEvaluationStatusDto.COMPLETED
```

所有 DTO 现在完全匹配 TypeScript 类型定义，确保前后端数据交互的一致性和类型安全。
