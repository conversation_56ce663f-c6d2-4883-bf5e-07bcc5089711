package cn.fyg.schedule.specification.xincai;

import cn.fyg.schedule.pojo.project.xincai.progressCheck.XcProgressCheck;
import cn.fyg.schedule.utils.SpecificationUtil;
import org.springframework.data.jpa.domain.Specification;

import java.util.Date;

public class XcProgressCheckSpecification {
    public static Specification<XcProgressCheck> ifContain(String column, String expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.like(root.get(column), SpecificationUtil.contains(expression)));
    }

    public static Specification<XcProgressCheck> ifEq(String column, String expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }

    public static Specification<XcProgressCheck> ifEq(String column, Integer expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }
    public static Specification<XcProgressCheck> ifDateEq(String column, Date expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }
    public static Specification<XcProgressCheck> ifDateBetween(String column, Date begin, Date end) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.between(root.get(column), begin, end));
    }
}
