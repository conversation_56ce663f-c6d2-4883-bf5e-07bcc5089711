package cn.fyg.schedule.service.sheets.yx.progressCheck.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.yuanxin.progressCheck.YxProgressCheckRecordRepository;
import cn.fyg.schedule.pojo.dto.project.yx.progressCheck.YxProgressCheckRecordDto;
import cn.fyg.schedule.pojo.project.yuanXin.progressCheck.YxProgressCheckRecord;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.sheets.yx.progressCheck.YxProgressCheckRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class YxProgressCheckRecordServiceImpl implements YxProgressCheckRecordService {
    private final YxProgressCheckRecordRepository r;

    public YxProgressCheckRecordServiceImpl(YxProgressCheckRecordRepository r) {
        this.r = r;
    }


    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse save(YxProgressCheckRecordDto dto) {
        BaseResponse response = BaseResponse.initialize();
        try {
            YxProgressCheckRecord data = YxProgressCheckRecord.initialize(dto);
            // 在事务内保存并立即预加载
            YxProgressCheckRecord savedData = r.save(data);
            preloadLazyCollections(savedData);
            response.setResult(savedData);
        } catch (Exception e) {
            log.error(e.getMessage());
            response.setCode(1);
            response.setMsg("保存失败");
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        YxProgressCheckRecord data = getDataById(id);
        if (data != null) {
            response.setResult(data);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    @Override
    public BaseResponse findByParentId(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        List<YxProgressCheckRecord> data = getItems(id, 1);
        if (data != null) {
            response.setResult(data);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    @Override
    public List<YxProgressCheckRecord> listRecordByParentId(Integer id, Integer mark) {
        return getItems(id, mark);
    }

    @Override
    public List<Integer> listParentIdByCreatorPhone(String creatorPhone) {
        try {
            List<Integer> list = r.listParentIdByCreatorPhone(creatorPhone);
            if (list.size() > 0) {
                return list;
            }
        } catch (Exception e) {
            log.error(e.getLocalizedMessage());
        }
        return null;
    }

    private YxProgressCheckRecord getDataById(Integer id) {
        try {
            Optional<YxProgressCheckRecord> optional = r.findById(id);
            if (optional.isPresent()) {
                return optional.get();
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    /**
     *
     * @param id
     * @param mark 0 sort by asc; 1 sort by desc
     * @return List<YxProgressCheckRecord>
     */
    private List<YxProgressCheckRecord> getItems(Integer id, Integer mark) {
        try {
            List<YxProgressCheckRecord> items = mark == 0 ? r.findByParentIdOrderByCreateDateAsc(id) : r.findByParentIdOrderByCreateDateDesc(id);
            if (!items.isEmpty()) {
                return items;
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }
}
