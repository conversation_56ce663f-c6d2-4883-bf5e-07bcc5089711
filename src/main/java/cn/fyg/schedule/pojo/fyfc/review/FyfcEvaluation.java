package cn.fyg.schedule.pojo.fyfc.review;

import cn.fyg.schedule.enums.fyfc.review.EvaluationStatus;
import cn.fyg.schedule.enums.fyfc.review.EvaluationStatusConverter;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 绩效评价主表实体
 * 对应数据库表: fyfc_evaluations
 */
@Entity
@Data
@Getter
@Setter
@ToString
@Table(name = "fyfc_evaluations")
public class FyfcEvaluation {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /**
     * 部门
     */
    @Column(name = "department", length = 100)
    private String department;

    /**
     * 被评价人姓名
     */
    @Column(name = "name", length = 50)
    private String name;

    /**
     * 评价日期
     */
    @Column(name = "review_date")
    @Temporal(TemporalType.DATE)
    private Date reviewDate;

    /**
     * 被邀同事姓名
     */
    @Column(name = "colleague_name", length = 50)
    private String colleagueName;

    /**
     * 主管上级姓名
     */
    @Column(name = "manager_name", length = 50)
    private String managerName;

    /**
     * 线上转发得分(0-10分)
     */
    @Column(name = "additional_score", precision = 5, scale = 2)
    private BigDecimal additionalScore;

    /**
     * 最终得分
     */
    @Column(name = "score", precision = 6, scale = 2)
    private BigDecimal score;

    /**
     * 说明备注
     */
    @Column(name = "comment", columnDefinition = "TEXT")
    private String comment;

    /**
     * 评价状态
     */
    @Convert(converter = EvaluationStatusConverter.class)
    @Column(name = "status")
    private EvaluationStatus status = EvaluationStatus.SELF;

    /**
     * 附件列表（JSON格式存储）
     */
    @Column(name = "attachments", columnDefinition = "TEXT")
    private String attachments;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;

    /**
     * 创建人
     */
    @Column(name = "created_by", length = 50, nullable = false)
    private String createdBy;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at")
    private Date updatedAt;

    /**
     * 更新人
     */
    @Column(name = "updated_by", length = 50)
    private String updatedBy;
}
