# BaseSpecification 错误修复说明

## 🐛 发现的问题

在 BaseSpecification 新增的代码中发现了以下错误：

### **1. 字符串格式化错误**
```java
// 错误的代码
String pattern = StrUtil.format("%{}%", value.toLowerCase());

// 修复后的代码
String pattern = "%" + value.toLowerCase() + "%";
```
**问题**：`StrUtil.format()` 的占位符语法错误，应该使用简单的字符串拼接。

### **2. 数值范围查询类型转换问题**
```java
// 错误的代码
if (min instanceof BigDecimal) {
    predicates.add(cb.greaterThanOrEqualTo(root.get(attribute), (BigDecimal) min));
} else if (NumberUtil.isNumber(min.toString())) {
    predicates.add(cb.greaterThanOrEqualTo(root.get(attribute), min));
}

// 修复后的代码
predicates.add(cb.ge(root.get(attribute), min));
```
**问题**：复杂的类型判断和转换，使用 `cb.ge()` 和 `cb.le()` 更简洁且类型安全。

### **3. 空条件处理问题**
```java
// 错误的代码
return null; // 可能导致 NullPointerException

// 修复后的代码
return cb.conjunction(); // 返回 true 条件，不影响查询
```
**问题**：返回 `null` 可能导致空指针异常，应该返回 `cb.conjunction()` 或 `cb.disjunction()`。

### **4. 空指针异常风险**
```java
// 错误的代码
spec = spec.and(specFunction.apply(attribute, value));

// 修复后的代码
Specification<T> newSpec = specFunction.apply(attribute, value);
if (ObjectUtil.isNotNull(newSpec)) {
    spec = ObjectUtil.isNull(spec) ? newSpec : spec.and(newSpec);
}
```
**问题**：没有检查 `spec` 是否为 `null`，可能导致空指针异常。

## ✅ 修复内容

### **1. likeIgnoreCase 方法修复**
```java
public static <T> Specification<T> likeIgnoreCase(String attribute, String value) {
    return (Root<T> root, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
        if (StrUtil.isBlank(value)) {
            return cb.conjunction(); // 返回 true 条件，不影响查询
        }
        String pattern = "%" + value.toLowerCase() + "%";
        return cb.like(cb.lower(root.get(attribute)), pattern);
    };
}
```

### **2. numberRange 方法修复**
```java
public static <T> Specification<T> numberRange(String attribute, Number min, Number max) {
    return (root, query, cb) -> {
        List<Predicate> predicates = new ArrayList<>();
        if (ObjectUtil.isNotNull(min)) {
            predicates.add(cb.ge(root.get(attribute), min));
        }
        if (ObjectUtil.isNotNull(max)) {
            predicates.add(cb.le(root.get(attribute), max));
        }
        if (predicates.isEmpty()) {
            return cb.conjunction(); // 返回 true 条件
        }
        return cb.and(predicates.toArray(new Predicate[0]));
    };
}
```

### **3. dateRange 方法修复**
```java
public static <T> Specification<T> dateRange(String attribute, Date startDate, Date endDate) {
    return (root, query, cb) -> {
        List<Predicate> predicates = new ArrayList<>();
        if (ObjectUtil.isNotNull(startDate)) {
            predicates.add(cb.greaterThanOrEqualTo(root.get(attribute), startDate));
        }
        if (ObjectUtil.isNotNull(endDate)) {
            Date endOfDay = DateUtil.endOfDay(endDate);
            predicates.add(cb.lessThanOrEqualTo(root.get(attribute), endOfDay));
        }
        if (predicates.isEmpty()) {
            return cb.conjunction(); // 返回 true 条件
        }
        return cb.and(predicates.toArray(new Predicate[0]));
    };
}
```

### **4. addSpecificationIfNotEmpty 方法修复**
```java
public static <T, V> Specification<T> addSpecificationIfNotEmpty(
        Specification<T> spec,
        String attribute,
        V value,
        BiFunction<String, V, Specification<T>> specFunction) {
    if (ObjectUtil.isNotNull(value)) {
        if (value instanceof String && StrUtil.isNotBlank((String) value)) {
            Specification<T> newSpec = specFunction.apply(attribute, value);
            if (ObjectUtil.isNotNull(newSpec)) {
                spec = ObjectUtil.isNull(spec) ? newSpec : spec.and(newSpec);
            }
        } else if (!(value instanceof String)) {
            Specification<T> newSpec = specFunction.apply(attribute, value);
            if (ObjectUtil.isNotNull(newSpec)) {
                spec = ObjectUtil.isNull(spec) ? newSpec : spec.and(newSpec);
            }
        }
    }
    return spec;
}
```

### **5. 集合条件添加方法修复**
```java
public static <T> Specification<T> addSpecificationIfNotEmpty(
        Specification<T> spec,
        String attribute,
        Collection<?> values) {
    if (CollUtil.isNotEmpty(values)) {
        Specification<T> newSpec = inCollection(attribute, values);
        if (ObjectUtil.isNotNull(newSpec)) {
            spec = ObjectUtil.isNull(spec) ? newSpec : spec.and(newSpec);
        }
    }
    return spec;
}
```

## 🎯 修复原则

### **1. 空值安全**
- 使用 `cb.conjunction()` 代替 `null` 返回值
- 在组合 Specification 前检查是否为 `null`
- 使用 Hutool 的空值检查方法

### **2. 类型安全**
- 使用 JPA 标准的比较方法 `cb.ge()` 和 `cb.le()`
- 避免复杂的类型转换和判断
- 让 JPA 处理类型转换

### **3. 异常处理**
- 避免可能的空指针异常
- 提供合理的默认行为
- 使用 Hutool 的安全方法

### **4. 代码简洁性**
- 移除不必要的复杂逻辑
- 使用更直接的 API
- 保持方法的单一职责

## 🚀 使用建议

### **1. 条件构建**
```java
// 推荐的使用方式
Specification<Entity> spec = Specification.where(null);
spec = BaseSpecification.addSpecificationIfNotEmpty(spec, "name", value, BaseSpecification::likeIgnoreCase);
```

### **2. 范围查询**
```java
// 数值范围
Specification<Entity> spec = BaseSpecification.numberRange("score", minScore, maxScore);

// 日期范围
Specification<Entity> spec = BaseSpecification.dateRange("createdAt", startDate, endDate);
```

### **3. 集合查询**
```java
// IN 查询
Specification<Entity> spec = BaseSpecification.inCollection("status", statusList);
```

## ✅ 验证

修复后的代码已经：
- ✅ 消除了编译错误
- ✅ 避免了运行时异常
- ✅ 提供了更好的类型安全
- ✅ 简化了代码逻辑
- ✅ 保持了 Hutool 工具包的优势

现在 BaseSpecification 可以安全地用于各种查询场景，并且与 Hutool 工具包完美集成。
