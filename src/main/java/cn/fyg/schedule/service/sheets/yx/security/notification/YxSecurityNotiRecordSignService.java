package cn.fyg.schedule.service.sheets.yx.security.notification;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.project.yuanXin.security.notification.YxSecurityNotiRecordSign;

public interface YxSecurityNotiRecordSignService {
    BaseResponse save(YxSecurityNotiRecordSign data);
    BaseResponse delete(Integer id);
    BaseResponse findByParentId(Integer id);
    BaseResponse deleteByParentId(Integer id);
}
