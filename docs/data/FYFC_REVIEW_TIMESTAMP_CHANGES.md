# FYFC Review 时间字段调整说明

## 📋 调整概述

根据您对SQL文件的调整，已将所有时间相关字段从 `bigint` (时间戳) 改为 `timestamp` 和 `date` 类型，相应地更新了Java代码中的数据类型。

## 🔄 主要变更

### **数据库字段变更**
1. **`fyfc_evaluations` 表**:
   - `review_date`: `bigint` → `date`
   - `created_at`: `bigint` → `timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP`
   - `updated_at`: `bigint` → `timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP`

2. **`fyfc_evaluation_scores` 表**:
   - `created_at`: `bigint` → `timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP`
   - `updated_at`: `bigint` → `timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP`

3. **`fyfc_evaluation_status_history` 表**:
   - `changed_at`: `bigint` → `timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP`

### **Java代码调整**

#### **实体类 (Entity Classes)**

1. **`FyfcEvaluation.java`**:
   ```java
   // 之前
   private Long reviewDate;
   private Long createdAt;
   private Long updatedAt;
   
   // 调整后
   @Temporal(TemporalType.DATE)
   private Date reviewDate;
   
   @CreationTimestamp
   @Temporal(TemporalType.TIMESTAMP)
   private Date createdAt;
   
   @UpdateTimestamp
   @Temporal(TemporalType.TIMESTAMP)
   private Date updatedAt;
   ```

2. **`FyfcEvaluationScore.java`**:
   ```java
   // 之前
   private Long createdAt = 0L;
   private Long updatedAt;
   
   // 调整后
   @CreationTimestamp
   @Temporal(TemporalType.TIMESTAMP)
   private Date createdAt;
   
   @UpdateTimestamp
   @Temporal(TemporalType.TIMESTAMP)
   private Date updatedAt;
   ```

3. **`FyfcEvaluationStatusHistory.java`**:
   ```java
   // 之前
   private Long changedAt;
   
   // 调整后
   @CreationTimestamp
   @Temporal(TemporalType.TIMESTAMP)
   private Date changedAt;
   ```

#### **Repository接口调整**

1. **`FyfcEvaluationRepository.java`**:
   ```java
   // 方法参数类型调整
   List<FyfcEvaluation> findByReviewDateBetween(Date startDate, Date endDate);
   ```

2. **`FyfcEvaluationStatusHistoryRepository.java`**:
   ```java
   // 方法参数类型调整
   List<FyfcEvaluationStatusHistory> findByChangedAtBetween(Date startTime, Date endTime);
   List<Object[]> getStatusChangeTrend(Date startTime, Date endTime);
   
   // 原生SQL查询调整
   "SELECT DATE(changed_at) as change_date, new_status, COUNT(*) " +
   "FROM fyfc_evaluation_status_history " +
   "WHERE changed_at BETWEEN :startTime AND :endTime " +
   "GROUP BY DATE(changed_at), new_status " +
   "ORDER BY change_date DESC"
   ```

#### **DTO类调整**

所有DTO类中的时间字段都从 `Long` 改为 `Date`:
- `FyfcEvaluationDto.java`
- `FyfcEvaluationScoreDto.java`
- `FyfcEvaluationSummaryDto.java`
- `FyfcEvaluationStatusHistoryDto.java`

## ✅ **新特性优势**

1. **自动时间管理**: 使用 `@CreationTimestamp` 和 `@UpdateTimestamp` 注解，Hibernate自动管理时间字段
2. **数据库默认值**: 利用数据库的 `DEFAULT CURRENT_TIMESTAMP` 和 `ON UPDATE CURRENT_TIMESTAMP`
3. **时区支持**: `timestamp` 类型自动处理时区转换
4. **更好的可读性**: Date类型比Long时间戳更直观
5. **类型安全**: 避免了手动时间戳转换的错误

## 🔧 **使用建议**

1. **时区设置**: 确保应用和数据库使用相同的时区设置
2. **日期查询**: 可以直接使用Date对象进行范围查询
3. **格式化**: 在前端显示时使用适当的日期格式化
4. **兼容性**: 如果需要与现有的时间戳API兼容，可以在Service层进行转换

## 📝 **注意事项**

- 移除了手动的 `@PrePersist` 和 `@PreUpdate` 方法
- 数据库会自动处理时间字段的设置和更新
- 查询方法的参数类型已相应调整
- DTO类型也已同步更新

所有调整已完成，代码现在完全匹配调整后的数据库结构。
