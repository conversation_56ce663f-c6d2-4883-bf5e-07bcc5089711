package cn.fyg.schedule.service.fyapp.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.fyapp.FAppListRepository;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.dto.fyapp.AppDTO;
import cn.fyg.schedule.pojo.fyapp.FAppList;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.fyapp.FAppListS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class FAppListSI implements FAppListS {
    private final FAppListRepository r;
    public FAppListSI(FAppListRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FAppList data) {
        return BaseService.save(data, r, FAppList.class);
    }

    @Override
    public BaseResponse listAll() {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FAppList> list = r.findAllByOrderByName();
            baseResponse.setResult(list);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Optional<FAppList> data = r.findById(id);
            data.ifPresent(baseResponse::setResult);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findByInNotIn(List<Integer> ids) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FAppList> list = !ids.isEmpty() ?r.findByIdNotIn(ids):r.findAllByOrderByName();
            baseResponse.setResult(list);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse findByInIn(List<Integer> ids) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FAppList> list = r.findByIdIn(ids);
            baseResponse.setResult(list);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse listByGroupId(Integer groupId) {
        BaseResponse response = BaseResponse.initialize();
        try {
            response.setResult(getByGroupId(groupId));
        } catch (MyException e) {
            response.setMsg(e.getErrMsg());
            response.setCode(e.getCode());
        }
        return response;
    }

    private List<AppDTO> getByGroupId(Integer groupId) throws MyException {
        try {
            return r.getAppByGroupId(groupId);
        } catch (JpaSystemException e) {
            log.error(e.getMessage());
            throw new MyException(500, "查询失败");
        }
    }
}
