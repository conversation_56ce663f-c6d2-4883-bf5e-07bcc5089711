package cn.fyg.schedule.controller.mini.app;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.FAssignment;
import cn.fyg.schedule.pojo.dto.FAssignmentDto;
import cn.fyg.schedule.pojo.dto.query.FAssignmentQuery;
import cn.fyg.schedule.service.FAssignmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("assignment")
public class FAssignmentController {
    @Autowired
    FAssignmentService ser;
    @GetMapping("list")
    public BaseResponse findAll(@RequestParam(value = "page", defaultValue = "1") Integer page,
                                @RequestParam(value = "size", defaultValue = "20") Integer size,
                                @RequestParam(value = "creator", defaultValue = "xlh") String creator) {

        return ser.findByCreator(creator, page, size);
    }

    @PostMapping("list")
    public BaseResponse getList(FAssignmentQuery query) {
        return ser.findByCreator(query.getCreator(), query.getPage(), query.getSize());
    }

    @PostMapping("save")
    public BaseResponse save(FAssignmentDto dto) {
        FAssignment fAssignment = FAssignment.initialize(dto);
        return ser.save(fAssignment);
    }

    @PostMapping("update")
    public BaseResponse update(FAssignmentDto dto) {
        FAssignment fAssignment = (FAssignment) ser.findById(dto.getId()).getResult();
        FAssignment tmp = FAssignment.initialize(dto);
        tmp.setScheduleId(fAssignment.getScheduleId());
        tmp.setCreateDate(fAssignment.getCreateDate());
        return ser.save(tmp);
    }

    @PostMapping("delete")
    public BaseResponse delete(Integer id) {
        return ser.delete(id);
    }

    @PostMapping("detail")
    public BaseResponse getDetail(Integer id) {
        return ser.findById(id);
    }

    @PostMapping("find")
    public BaseResponse findAssignment(FAssignmentQuery query) {
        return ser.findByScheduleIdAndCreator(query.getScheduleId(), query.getCreator());
    }

    @PostMapping("findbyvoucher")
    public BaseResponse findByVoucher(Integer voucherId, Integer voucherType) {
        return ser.findAssignmentByVoucher(voucherId, voucherType);
    }
}
