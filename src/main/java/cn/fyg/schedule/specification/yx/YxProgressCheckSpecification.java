package cn.fyg.schedule.specification.yx;

import cn.fyg.schedule.pojo.project.yuanXin.progressCheck.YxProgressCheck;
import cn.fyg.schedule.utils.SpecificationUtil;
import org.springframework.data.jpa.domain.Specification;

import java.util.Date;

public class YxProgressCheckSpecification {
    public static Specification<YxProgressCheck> ifContain(String column, String expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.like(root.get(column), SpecificationUtil.contains(expression)));
    }

    public static Specification<YxProgressCheck> ifEq(String column, String expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }

    public static Specification<YxProgressCheck> ifEq(String column, Integer expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }

    public static Specification<YxProgressCheck> ifGreaterThan(String column, Integer expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.greaterThan(root.get(column), expression));
    }

    public static Specification<YxProgressCheck> ifGreaterThanOrEqualTo(String column, Integer expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.greaterThanOrEqualTo(root.get(column), expression));
    }

    public static Specification<YxProgressCheck> ifLessThan(String column, Integer expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.lessThan(root.get(column), expression));
    }

    public static Specification<YxProgressCheck> ifLessThanOrEqualTo(String column, Integer expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.lessThanOrEqualTo(root.get(column), expression));
    }

    public static Specification<YxProgressCheck> ifBetween(String column, Integer begin, Integer end) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.between(root.get(column), begin, end));
    }
    public static Specification<YxProgressCheck> ifDateEq(String column, Date expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }
    public static Specification<YxProgressCheck> ifDateBetween(String column, Date begin, Date end) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.between(root.get(column), begin, end));
    }

    public static Specification<YxProgressCheck> ifDateGreaterThan(String column, Date expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.greaterThan(root.get(column), expression));
    }

    public static Specification<YxProgressCheck> ifDateGreaterThanOrEqualTo(String column, Date expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.greaterThanOrEqualTo(root.get(column), expression));
    }

    public static Specification<YxProgressCheck> ifDateLessThan(String column, Date expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.lessThan(root.get(column), expression));
    }
    public static Specification<YxProgressCheck> ifDateLessThanOrEqualTo(String column, Date expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.lessThanOrEqualTo(root.get(column), expression));
    }
}
