package cn.fyg.schedule.service.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.assignment.FAssignmentRepository;
import cn.fyg.schedule.pojo.FAssignment;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.FAssignmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class FAssignmentServiceImpl implements FAssignmentService {
    final
    FAssignmentRepository repository;

    public FAssignmentServiceImpl(FAssignmentRepository repository) {
        this.repository = repository;
    }

    @Override
    public BaseResponse findAllByIdIsNotNull(Integer page, Integer size) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Sort sort = Sort.by(Sort.Direction.DESC, "createDate");
            Pageable pageable = PageRequest.of(page - 1, size, sort);
            Page<FAssignment> result = repository.findAllByIdIsNotNullOrderByCreateDateDesc(pageable);
            baseResponse.setTotalCount(result.getTotalElements());
            baseResponse.setResult(result.getContent());
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Optional<FAssignment> result = repository.findById(id);
            result.ifPresent(baseResponse::setResult);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse findByCreator(String creator, Integer page, Integer size) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Sort sort = Sort.by(Sort.Direction.DESC, "createDate");
            Pageable pageable = PageRequest.of(page - 1, size, sort);
            Page<FAssignment> result = repository.findByCreatorOrderByCreateDateDesc(creator, pageable);
            baseResponse.setTotalCount(result.getTotalElements());
            baseResponse.setResult(result.getContent());
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse findByScheduleIdAndCreator(Integer id, String creator) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {

            List<FAssignment> result = repository.findByScheduleIdAndCreatorOrderByCreateDateDesc(id, creator);
            baseResponse.setResult(result);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public List<FAssignment> listBySecheduleIdAndCreator(Integer id, String creator) {
        List<FAssignment> result = null;
        try {
            result = repository.findByScheduleIdAndCreatorOrderByCreateDateDesc(id, creator);
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return result;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FAssignment assignment) {
        return BaseService.save(assignment, repository, FAssignment.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, repository);
    }

    @Override
    public BaseResponse findAssignmentByVoucher(Integer voucherId, Integer voucherType) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Optional<FAssignment> result = repository.findTopByVoucherIdAndVoucherTypeOrderByCreateDateDesc(voucherId, voucherType);
            result.ifPresent(baseResponse::setResult);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }
}
