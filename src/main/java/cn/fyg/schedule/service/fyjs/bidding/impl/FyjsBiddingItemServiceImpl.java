package cn.fyg.schedule.service.fyjs.bidding.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.fyjs.bidding.FyjsBiddingItemRepository;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.fyjs.bidding.FyjsBiddingItem;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.fyjs.bidding.FyjsBiddingItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class FyjsBiddingItemServiceImpl implements FyjsBiddingItemService {
    private final FyjsBiddingItemRepository r;

    public FyjsBiddingItemServiceImpl(FyjsBiddingItemRepository r) {
        this.r = r;
    }

    @Override
    public BaseResponse save(FyjsBiddingItem data) {
        return BaseService.save(data, r, FyjsBiddingItem.class);
    }

    @Override
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findByParentId(Integer parentId) {
        BaseResponse response = BaseResponse.initialize();
        try {
            response.setResult(getByParentId(parentId));
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }

    private List<FyjsBiddingItem> getByParentId(Integer parentId) throws MyException {
        try {
            return r.findByParentId(parentId);
        } catch (JpaSystemException e) {
            log.info(e.getMessage());
            throw new MyException(500, "查询失败");
        }
    }
}
