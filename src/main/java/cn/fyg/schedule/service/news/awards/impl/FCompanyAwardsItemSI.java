package cn.fyg.schedule.service.news.awards.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.news.awards.FCompanyAwardsItemR;
import cn.fyg.schedule.pojo.news.awads.FCompanyAwardsItem;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.news.awards.FCompanyAwardsItemS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class FCompanyAwardsItemSI implements FCompanyAwardsItemS {
    private final FCompanyAwardsItemR r;
    public FCompanyAwardsItemSI(FCompanyAwardsItemR r) {
        this.r = r;
    }
    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FCompanyAwardsItem data) {
        return BaseService.save(data, r, FCompanyAwardsItem.class);
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Optional<FCompanyAwardsItem> data = r.findById(id);
            if (data.isPresent()) {
                baseResponse.setResult(data.get());
            }
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse listByAwardId(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FCompanyAwardsItem> data = r.findByAwardId(id);
            baseResponse.setResult(data);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }
}
