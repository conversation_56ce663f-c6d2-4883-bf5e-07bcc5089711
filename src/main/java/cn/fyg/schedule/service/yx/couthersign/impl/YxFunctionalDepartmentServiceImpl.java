package cn.fyg.schedule.service.yx.couthersign.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.yx.countersign.YxFunctionalDepartmentRepository;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.yx.countersign.YxFunctionalDepartment;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.yx.couthersign.YxFunctionalDepartmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;

@Service
@Slf4j
public class YxFunctionalDepartmentServiceImpl implements YxFunctionalDepartmentService {
    private final YxFunctionalDepartmentRepository r;

    public YxFunctionalDepartmentServiceImpl(YxFunctionalDepartmentRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(YxFunctionalDepartment data) {
        BaseResponse response = BaseResponse.initialize();
        try {
            response.setResult(BaseService.saveWithR(data, r, YxFunctionalDepartment.class));
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        try {
            BaseService.deleteWithR(id, r);
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }

    @Override
    public BaseResponse listAll() {
        BaseResponse response = BaseResponse.initialize();
        try {
            response.setResult(findAll());
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }
    @Override
    public List<YxFunctionalDepartment> findAll() throws MyException {
        try {
            return (List<YxFunctionalDepartment>) r.findAll();
        } catch (JpaSystemException e) {
            log.error(e.getMessage());
            throw new MyException(500, "查询失败");
        }
    }
}
