package cn.fyg.schedule.controller.functions;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.fyapp.FAppListDto;
import cn.fyg.schedule.pojo.dto.fyapp.FAppRegisterDto;
import cn.fyg.schedule.pojo.dto.fyapp.FAppUserSettingDto;
import cn.fyg.schedule.pojo.dto.fyapp.FRegister;
import cn.fyg.schedule.pojo.fyapp.FAppList;
import cn.fyg.schedule.pojo.fyapp.FAppRegister;
import cn.fyg.schedule.service.fyapp.FAppListS;
import cn.fyg.schedule.service.fyapp.FAppRegisterS;
import cn.fyg.schedule.service.fyapp.FAppUserSettingService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("app")
public class FAppController {
    private final FAppRegisterS fAppRegisterS;
    private final FAppListS fAppListS;

    private final FAppUserSettingService appUserSettingService;
    public FAppController(FAppRegisterS fAppRegisterS, FAppListS fAppListS, FAppUserSettingService appUserSettingService) {
        this.fAppListS = fAppListS;
        this.fAppRegisterS = fAppRegisterS;
        this.appUserSettingService = appUserSettingService;
    }

    @PostMapping("save/app")
    BaseResponse saveApp(FAppListDto dto) {
        FAppList data = FAppList.initialize(dto);
        return fAppListS.save(data);
    }

    @PostMapping("find/id")
    BaseResponse findById(Integer id) {
        return fAppListS.findById(id);
    }

    @PostMapping("delete/app")
    BaseResponse deleteApp(Integer id) {
        return fAppListS.delete(id);
    }

    @PostMapping("list/group/id")
    BaseResponse listByGroupId(Integer id) {
        return fAppListS.listByGroupId(id);
    }
    @PostMapping("list/all")
    BaseResponse listAll() {
        return fAppListS.listAll();
    }

    @PostMapping("list/in")
    BaseResponse listIn(Integer id) {
        return fAppListS.findByInIn(fAppRegisterS.getAppIdsByGroupId(id));
    }

    @PostMapping("list/notin")
    BaseResponse listNotIn(Integer id) {
        return fAppListS.findByInNotIn(fAppRegisterS.getAppIdsByGroupId(id));
    }

    @PostMapping("save/registers")
    BaseResponse saveRegisters(FRegister apps) {
        BaseResponse response = BaseResponse.initialize();
        int i = 0;
        int sort = 0;
        for (Integer appid: apps.getAppIds()) {
            FAppRegisterDto dto = new FAppRegisterDto();
            dto.setGroupId(apps.getGroupId());
            dto.setAppSort(sort);
            dto.setAppId(appid);
            FAppRegister data = FAppRegister.initialize(dto);
            if (fAppRegisterS.save(data).getCode() == 1) {
                i++;
            } else {
                sort++;
            }
        }
        response.setMsg("保存结束。应用注册失败数：" + i);
        return response;
    }

    @PostMapping("save/register")
    BaseResponse saveRegister(FAppRegisterDto dto) {
        FAppRegister data = FAppRegister.initialize(dto);
        return fAppRegisterS.save(data);
    }

    @PostMapping("delete/register")
    BaseResponse deleteRegister(Integer groupId) {
        return fAppRegisterS.deleteByGroupId(groupId);
    }

    @PostMapping("user/setting/save")
    BaseResponse saveUserSetting(FAppUserSettingDto dto) {
        return appUserSettingService.save(dto);
    }

    @PostMapping("user/setting/find")
    BaseResponse findUserSetting(FAppUserSettingDto dto) {
        return appUserSettingService.ifHas(dto);
    }
}
