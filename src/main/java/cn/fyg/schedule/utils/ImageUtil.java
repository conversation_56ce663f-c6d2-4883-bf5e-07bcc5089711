package cn.fyg.schedule.utils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.net.URL;
import java.net.URLConnection;

public class ImageUtil {
    public static BufferedImage initImage(String imageUrl) {
        try {
            URL url = new URL(imageUrl);
            URLConnection connection = null;
            connection = url.openConnection();
            connection.setDoOutput(true);
            return ImageIO.read(connection.getInputStream());
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }
}
