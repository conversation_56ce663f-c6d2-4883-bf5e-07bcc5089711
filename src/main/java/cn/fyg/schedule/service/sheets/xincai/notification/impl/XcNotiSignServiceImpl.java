package cn.fyg.schedule.service.sheets.xincai.notification.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.xincai.notification.XcNotiSignRepository;
import cn.fyg.schedule.pojo.project.xincai.noti.XcNotiSign;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.sheets.xincai.notification.XcNotiSignService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class XcNotiSignServiceImpl implements XcNotiSignService {
    private final XcNotiSignRepository r;

    public XcNotiSignServiceImpl(XcNotiSignRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(XcNotiSign data) {
        return BaseService.save(data, r, XcNotiSign.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        XcNotiSign data = getDataById(id);
        if (data != null) {
            response.setResult(data);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    @Override
    public BaseResponse findByParentId(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        List<XcNotiSign> data = getItems(id);
        if (data != null) {
            response.setResult(data);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    private XcNotiSign getDataById(Integer id) {
        try {
            Optional<XcNotiSign> optional = r.findById(id);
            if (optional.isPresent()) {
                return optional.get();
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    private List<XcNotiSign> getItems(Integer id) {
        try {
            List<XcNotiSign> items = r.findByParentIdOrderByCreateDateDesc(id);
            if (!items.isEmpty()) {
                return items;
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }
}
