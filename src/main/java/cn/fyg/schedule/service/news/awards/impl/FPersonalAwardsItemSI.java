package cn.fyg.schedule.service.news.awards.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.news.awards.FPersonalAwardsItemR;
import cn.fyg.schedule.pojo.news.awads.FPersonalAwardsItem;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.news.awards.FPersonalAwardsItemS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class FPersonalAwardsItemSI implements FPersonalAwardsItemS {
    private final FPersonalAwardsItemR r;
    public FPersonalAwardsItemSI(FPersonalAwardsItemR r) {
        this.r = r;
    }
    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FPersonalAwardsItem data) {
        return BaseService.save(data, r, FPersonalAwardsItem.class);
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Optional<FPersonalAwardsItem> data = r.findById(id);
            if (data.isPresent()) {
                baseResponse.setResult(data.get());
            }
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse listByAwardId(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FPersonalAwardsItem> data = r.findByAwardId(id);
            baseResponse.setResult(data);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }
}
