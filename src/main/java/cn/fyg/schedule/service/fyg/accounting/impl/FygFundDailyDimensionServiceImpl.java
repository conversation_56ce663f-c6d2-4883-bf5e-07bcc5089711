package cn.fyg.schedule.service.fyg.accounting.impl;

import cn.fyg.schedule.dao.fyg.accounting.FygFundDailyDimensionRepository;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.fyg.accounting.FygFundDailyDimension;
import cn.fyg.schedule.service.fyg.accounting.FygFundDailyDimensionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class FygFundDailyDimensionServiceImpl implements FygFundDailyDimensionService {
    private final FygFundDailyDimensionRepository r;

    public FygFundDailyDimensionServiceImpl(FygFundDailyDimensionRepository r) {
        this.r = r;
    }

    @Override
    public List<FygFundDailyDimension> getFygFundDailyDimension() {
        try {
            return listAll();
        } catch (MyException e) {
            log.error("{}", e.getErrMsg());
        }
        return Collections.emptyList();
    }

    private List<FygFundDailyDimension> listAll() throws MyException {
        try {
            return r.findByOrderByIdAsc();
        } catch (JpaSystemException e) {
            throw new MyException(3001, "查询失败");
        }
    }
}
