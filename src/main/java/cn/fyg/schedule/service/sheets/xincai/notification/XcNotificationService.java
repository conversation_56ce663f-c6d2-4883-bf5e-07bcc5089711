package cn.fyg.schedule.service.sheets.xincai.notification;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.project.yx.security.notification.YxSecurityNotificationDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.pojo.project.xincai.noti.XcNotification;

public interface XcNotificationService {
    BaseResponse save(YxSecurityNotificationDto dto);
    BaseResponse delete(Integer id);
    BaseResponse findById(Integer id);
    BaseResponse filter(YxSecurityNotificationDto dto, Pagination pagination);
    BaseResponse getEventNameList(Integer type);
    XcNotification getById(Integer id);
}
