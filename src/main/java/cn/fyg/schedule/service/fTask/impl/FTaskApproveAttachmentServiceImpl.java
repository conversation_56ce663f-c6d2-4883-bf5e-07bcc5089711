package cn.fyg.schedule.service.fTask.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.ftask.FTaskApproveAttachmentRepository;
import cn.fyg.schedule.pojo.ftask.FTaskApproveAttachment;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.fTask.FTaskApproveAttachmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;

@Service
@Slf4j
public class FTaskApproveAttachmentServiceImpl implements FTaskApproveAttachmentService {
    private final FTaskApproveAttachmentRepository r;
    public FTaskApproveAttachmentServiceImpl(FTaskApproveAttachmentRepository r) {
        this.r = r;
    }
    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FTaskApproveAttachment data) {
        return BaseService.save(data, r, FTaskApproveAttachment.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }
}
