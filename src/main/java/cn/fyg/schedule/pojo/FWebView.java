package cn.fyg.schedule.pojo;

import cn.hutool.core.date.DateUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

@Entity
@Setter
@Getter
@ToString
@Table(name = "fwebview")
public class FWebView {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    private String name;
    private String content;
    private String image;
    private String creator;
    @Temporal(TemporalType.TIMESTAMP)
    private Date datetime;
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdate;
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedate;

    public static FWebView initialize(FWebViewDto dto) {
        FWebView data = new FWebView();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        data.setName(dto.getName());
        data.setContent(dto.getContent());
        data.setCreator(dto.getCreator());
        data.setImage(dto.getImage());
        data.setDatetime(DateUtil.date(dto.getDatetime()));
        if (dto.getCreatedate() != null) {
            data.setCreatedate(DateUtil.parse(dto.getCreatedate()));
        }
        return data;
    }
}
