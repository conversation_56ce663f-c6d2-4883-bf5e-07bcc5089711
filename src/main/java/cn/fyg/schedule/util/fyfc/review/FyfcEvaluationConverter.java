package cn.fyg.schedule.util.fyfc.review;

import cn.fyg.schedule.pojo.dto.fyfc.review.*;
import cn.fyg.schedule.pojo.fyfc.review.FyfcEvaluation;
import cn.fyg.schedule.service.fyfc.review.IFyfcEvaluationScoreService;
import cn.fyg.schedule.service.fyfc.review.IFyfcEvaluationCommonService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * FYFC 评价转换工具类
 * 提供统一的实体到DTO转换方法，避免代码重复
 */
@Slf4j
@Component
public class FyfcEvaluationConverter {

    private final IFyfcEvaluationScoreService scoreService;
    private final IFyfcEvaluationCommonService commonService;

    public FyfcEvaluationConverter(IFyfcEvaluationScoreService scoreService,
                                  IFyfcEvaluationCommonService commonService) {
        this.scoreService = scoreService;
        this.commonService = commonService;
    }

    /**
     * 转换为评价DTO
     */
    public FyfcEvaluationDto convertToEvaluationDto(FyfcEvaluation evaluation) {
        FyfcEvaluationDto dto = new FyfcEvaluationDto();
        // 排除 attachments 字段，避免类型转换异常
        BeanUtil.copyProperties(evaluation, dto, "attachments");

        // 处理时间戳转换
        convertTimestamps(evaluation, dto);

        // 设置状态字符串
        if (ObjectUtil.isNotNull(evaluation.getStatus())) {
            dto.setStatus(evaluation.getStatus().getValue());
        }

        // 处理附件数据
        convertAttachments(evaluation, dto);

        return dto;
    }

    /**
     * 转换为评价详情DTO
     */
    public FyfcEvaluationDetailDto convertToEvaluationDetailDto(FyfcEvaluation evaluation) {
        FyfcEvaluationDetailDto dto = new FyfcEvaluationDetailDto();
        // 排除 attachments 字段，避免类型转换异常
        BeanUtil.copyProperties(evaluation, dto, "attachments");

        // 处理时间戳转换
        convertTimestampsForDetail(evaluation, dto);

        // 设置状态字符串
        if (ObjectUtil.isNotNull(evaluation.getStatus())) {
            dto.setStatus(evaluation.getStatus().getValue());
        }

        // 获取评分记录
        loadScores(evaluation, dto);

        // 获取状态历史
        loadStatusHistory(evaluation, dto);

        // 处理附件数据
        convertAttachmentsForDetail(evaluation, dto);

        return dto;
    }

    /**
     * 转换为评价DTO（带评分数据）
     */
    public FyfcEvaluationDto convertToEvaluationDtoWithScores(FyfcEvaluation evaluation) {
        FyfcEvaluationDto dto = convertToEvaluationDto(evaluation);

        // 加载评分数据
        loadScores(evaluation, dto);

        return dto;
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 转换时间戳（基础版本）
     */
    private void convertTimestamps(FyfcEvaluation evaluation, FyfcEvaluationDto dto) {
        if (ObjectUtil.isNotNull(evaluation.getReviewDate())) {
            dto.setReviewDate(evaluation.getReviewDate().getTime());
        }
        if (ObjectUtil.isNotNull(evaluation.getCreatedAt())) {
            dto.setCreatedAt(evaluation.getCreatedAt().getTime());
        }
    }

    /**
     * 转换时间戳（详情版本，包含更新时间）
     */
    private void convertTimestampsForDetail(FyfcEvaluation evaluation, FyfcEvaluationDetailDto dto) {
        if (ObjectUtil.isNotNull(evaluation.getReviewDate())) {
            dto.setReviewDate(evaluation.getReviewDate().getTime());
        }
        if (ObjectUtil.isNotNull(evaluation.getCreatedAt())) {
            dto.setCreatedAt(evaluation.getCreatedAt().getTime());
        }
        if (ObjectUtil.isNotNull(evaluation.getUpdatedAt())) {
            dto.setUpdatedAt(evaluation.getUpdatedAt().getTime());
        }
    }

    /**
     * 转换附件数据（基础版本）
     */
    private void convertAttachments(FyfcEvaluation evaluation, FyfcEvaluationDto dto) {
        try {
            List<FyfcAttachmentDto> attachments = parseAttachmentsFromJson(evaluation.getAttachments());
            dto.setAttachments(attachments);
        } catch (Exception e) {
            log.warn("解析评价附件数据失败: evaluationId={}", evaluation.getId(), e);
            dto.setAttachments(new ArrayList<>());
        }
    }

    /**
     * 转换附件数据（详情版本）
     */
    private void convertAttachmentsForDetail(FyfcEvaluation evaluation, FyfcEvaluationDetailDto dto) {
        try {
            List<FyfcAttachmentDto> attachments = parseAttachmentsFromJson(evaluation.getAttachments());
            dto.setAttachments(attachments);
        } catch (Exception e) {
            log.warn("解析评价详情附件数据失败: evaluationId={}", evaluation.getId(), e);
            dto.setAttachments(new ArrayList<>());
        }
    }

    /**
     * 加载评分数据
     */
    private void loadScores(FyfcEvaluation evaluation, Object dto) {
        try {
            FyfcApiResponseDto<List<FyfcEvaluationScoreDto>> scoresResponse =
                scoreService.getEvaluationScores(evaluation.getId());
            if (scoresResponse.getSuccess() && ObjectUtil.isNotNull(scoresResponse.getData())) {
                if (dto instanceof FyfcEvaluationDto) {
                    ((FyfcEvaluationDto) dto).setScores(scoresResponse.getData());
                } else if (dto instanceof FyfcEvaluationDetailDto) {
                    ((FyfcEvaluationDetailDto) dto).setScores(scoresResponse.getData());
                }
            }
        } catch (Exception e) {
            log.warn("加载评价评分数据失败: evaluationId={}", evaluation.getId(), e);
            // 设置空列表，避免前端出错
            List<FyfcEvaluationScoreDto> emptyScores = new ArrayList<>();
            if (dto instanceof FyfcEvaluationDto) {
                ((FyfcEvaluationDto) dto).setScores(emptyScores);
            } else if (dto instanceof FyfcEvaluationDetailDto) {
                ((FyfcEvaluationDetailDto) dto).setScores(emptyScores);
            }
        }
    }

    /**
     * 加载状态历史
     */
    private void loadStatusHistory(FyfcEvaluation evaluation, FyfcEvaluationDetailDto dto) {
        try {
            FyfcApiResponseDto<List<FyfcEvaluationStatusHistoryDto>> historyResponse =
                commonService.getEvaluationStatusHistory(evaluation.getId());
            if (historyResponse.getSuccess()) {
                dto.setStatusHistory(historyResponse.getData());
            }
        } catch (Exception e) {
            log.warn("加载评价状态历史失败: evaluationId={}", evaluation.getId(), e);
            dto.setStatusHistory(new ArrayList<>());
        }
    }

    /**
     * 从JSON字符串解析附件列表
     */
    private List<FyfcAttachmentDto> parseAttachmentsFromJson(String attachmentsJson) {
        if (StrUtil.isBlank(attachmentsJson)) {
            return new ArrayList<>();
        }

        try {
            return JSONUtil.toList(attachmentsJson, FyfcAttachmentDto.class);
        } catch (Exception e) {
            log.warn("解析附件JSON失败: {}", attachmentsJson, e);
            return new ArrayList<>();
        }
    }
}
