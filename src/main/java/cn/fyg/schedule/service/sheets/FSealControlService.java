package cn.fyg.schedule.service.sheets;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.project.yuanXin.FSealControl;
import cn.fyg.schedule.pojo.project.yuanXin.FSealControlItem;
import cn.fyg.schedule.pojo.project.yuanXin.FSealControlSignature;

public interface FSealControlService {
    BaseResponse save(FSealControl data);
    BaseResponse delete(Integer id);
    BaseResponse findById(Integer id);
    BaseResponse findAll(Integer page, Integer size);
    BaseResponse findAll();

    BaseResponse getDepartmentNameHistoryList();

    BaseResponse getSealTypeHistoryList();

    BaseResponse saveItem(FSealControlItem data);
    BaseResponse deleteItem(Integer id);

    BaseResponse findItems(Integer sealId);

    BaseResponse saveSignature(FSealControlSignature data);
    BaseResponse deleteSignature(Integer id);

    BaseResponse findSignatureList(Integer sealId);
}
