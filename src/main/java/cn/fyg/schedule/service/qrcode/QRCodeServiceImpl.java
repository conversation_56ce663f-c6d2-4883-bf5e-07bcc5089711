package cn.fyg.schedule.service.qrcode;

import cn.fyg.schedule.base.BaseResponse;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class QRCodeServiceImpl implements QRCodeService {
    @Override
    public BaseResponse generateQR(String url) {
        BaseResponse response = BaseResponse.initialize();
        QrConfig config = new QrConfig(300, 300);
        // 设置边距，既二维码和背景之间的边距
        response.setResult(ImgUtil.toBase64DataUri(QrCodeUtil.generate(url, config), "jpg"));
        return response;
    }
}
