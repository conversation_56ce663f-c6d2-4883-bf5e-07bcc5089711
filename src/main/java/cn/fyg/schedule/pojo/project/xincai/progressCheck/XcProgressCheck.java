package cn.fyg.schedule.pojo.project.xincai.progressCheck;

import cn.fyg.schedule.pojo.dto.project.yx.progressCheck.YxProgressCheckDto;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Setter
@Getter
@ToString
@Table(name = "xc_progress_check")
public class XcProgressCheck {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    private String creator;
    private String creatorName;
    @Column(name = "yx_project_name")
    private String yxProjectName;
    @Column(name = "yx_time_limit")
    private Integer yxTimeLimit;
    @Column(name = "yx_problem_description")
    private String yxProblemDescription;
    @Column(name = "create_date")
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @Column(name = "update_date")
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;
    @Column(name = "inspection_date")
    @Temporal(TemporalType.DATE)
    private Date inspectionDate;
    @Column(name = "work_date")
    @Temporal(TemporalType.DATE)
    private Date workDate;
    private String workName;
    private Integer needSign;
    private String lastEditor;
    private Integer dataStatus;
    @Column(name = "event_status")
    private Integer eventStatus;
    private String organization;
    @Column(name = "inspection_department")
    private String inspectionDepartment;

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "parent_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    @JsonIgnore
    @ToString.Exclude
    List<XcProgressCheckItem> items = new ArrayList<>();

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "parent_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    @JsonIgnore
    @ToString.Exclude
    List<XcProgressCheckSign> signatures = new ArrayList<>();

    public static XcProgressCheck initialize(YxProgressCheckDto dto) {
        XcProgressCheck data = new XcProgressCheck();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        data.setCreator(dto.getCreator());
        data.setCreatorName(dto.getCreatorName());
        data.setYxProjectName(dto.getYxProjectName());
        data.setYxTimeLimit(dto.getYxTimeLimit());
        data.setYxProblemDescription(dto.getYxProblemDescription());
        if (dto.getCreateDate() != null) {
            data.setCreateDate(DateUtil.date(data.createDate));
        }
        if (dto.getInspectionDate() != null) {
            data.setInspectionDate(DateUtil.date(dto.getInspectionDate()));
        }
        if (dto.getWorkDate() != null) {
            data.setWorkDate(DateUtil.date(dto.getWorkDate()));
        }
        data.setWorkName(dto.getWorkName());
        data.setNeedSign(dto.getNeedSign());
        data.setDataStatus(dto.getDataStatus());
        data.setLastEditor(dto.getLastEditor());
        data.setEventStatus(dto.getEventStatus());
        data.setOrganization(dto.getOrganization());
        data.setInspectionDepartment(dto.getInspectionDepartment());
        return data;
    }
}
