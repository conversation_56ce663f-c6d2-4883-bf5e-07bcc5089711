package cn.fyg.schedule.service.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.schedule.FScheduleRepository;
import cn.fyg.schedule.pojo.FSchedule;
import cn.fyg.schedule.pojo.dto.FScheduleCountGroupByStartDate;
import cn.fyg.schedule.pojo.dto.FScheduleDto;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.FScheduleService;
import cn.fyg.schedule.specification.FScheduleSpecification;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.transaction.Transactional;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FScheduleServiceImpl implements FScheduleService {
    final private FScheduleRepository r;
    @Qualifier("entityManagerPrimary")
    final private EntityManager entityManager;

    public FScheduleServiceImpl(FScheduleRepository r, EntityManager entityManager) {
        this.r = r;
        this.entityManager = entityManager;
    }
    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FSchedule fSchedule) {
        return BaseService.save(fSchedule, r, FSchedule.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findByCreator(String creator, Integer page, Integer size) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Sort sort = Sort.by(Sort.Direction.DESC, "createDate");
            Pageable pageable = PageRequest.of(page - 1, size, sort);
            Page<FSchedule> result = r.findByCreatorOrderByCreateDateDesc(creator, pageable);
            baseResponse.setTotalCount(result.getTotalElements());
            baseResponse.setResult(result.getContent());
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Optional<FSchedule> result = r.findById(id);
            result.ifPresent(baseResponse::setResult);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse findByCreatorAndStartDate(String creator, String startDate, Integer page, Integer size) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Sort sort = Sort.by(Sort.Direction.DESC, "createDate");
            Pageable pageable = PageRequest.of(page - 1, size, sort);
            Page<FSchedule> result = r.findByCreatorAndStartDateOrderByCreateDateDesc(creator, DateUtil.parseDate(startDate), pageable);
            baseResponse.setTotalCount(result.getTotalElements());
            baseResponse.setResult(result.getContent());
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    private Date getEndDate(String start, String duration) {
        Date end = DateUtil.parseDate(start);
        switch (duration) {
            case "week": {
                return DateUtil.offset(end, DateField.DAY_OF_MONTH, -7);
            }
            case "month": {
                return DateUtil.offset(end, DateField.MONTH, -1);
            }
            case "year": {
                return DateUtil.offset(end, DateField.YEAR, -1);
            }
            default: {
                return end;
            }
        }
    }

    @Override
    public BaseResponse findByCreatorAndDuration(String creator, String start, String duration, Integer page, Integer size) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Sort sort = Sort.by(Sort.Direction.DESC, "createDate");
            Pageable pageable = PageRequest.of(page - 1, size, sort);
            Page<FSchedule> result = r.findByCreatorAndStickyTopicAndStartDateBetweenOrderByCreateDateDesc(creator, 0, this.getEndDate(start, duration), DateUtil.parseDate(start), pageable);
            List<FSchedule> stickyTopics = r.findByCreatorAndStickyTopicOrderByCreateDateDesc(creator, 1);
            stickyTopics.addAll(result.getContent());
            baseResponse.setTotalCount((long) stickyTopics.size());
            baseResponse.setResult(stickyTopics);

        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse findByCreatorAndDurationAndCurrentStatus(String creator, String start, String duration, String currentStatus, Integer page, Integer size) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Sort sort = Sort.by(Sort.Direction.DESC, "createDate");
            Pageable pageable = PageRequest.of(page - 1, size, sort);
            Page<FSchedule> result = r.findByCreatorAndCurrentStatusAndStartDateBetweenOrderByStickyTopicDescCreateDateDesc(creator, currentStatus, this.getEndDate(start, duration), DateUtil.parseDate(start), pageable);
            List<FSchedule> stickyTopics = r.findByCreatorAndStickyTopicAndCurrentStatusOrderByCreateDateDesc(creator, 1, currentStatus);
            stickyTopics.addAll(result.getContent());
            baseResponse.setTotalCount((long) stickyTopics.size());
            baseResponse.setResult(stickyTopics);

        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse search(String creator, String start, String duration, String searchText, Integer page, Integer size) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Sort sort = Sort.by(Sort.Direction.DESC, "createDate");
            Pageable pageable = PageRequest.of(page - 1, size, sort);
            Page<FSchedule> result = r.findByCreatorAndStartDateBetweenAndTitleContainsOrderByCreateDateDesc(creator, this.getEndDate(start, duration), DateUtil.parseDate(start), searchText, pageable);
            baseResponse.setTotalCount(result.getTotalElements());
            baseResponse.setResult(result.getContent());
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse findMarkedDate(String creator) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<String> list = r.findByCreatorGroupByStartDate(creator);
            baseResponse.setResult(list);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse getMarkedSchedule(String creator) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FScheduleCountGroupByStartDate> list = r.getMarkedSchedule(creator);
            baseResponse.setResult(list);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse findAllColumns(String table) {
        BaseResponse baseResponse = BaseResponse.initialize();
        Query query = entityManager.createNativeQuery("DESCRIBE " + table);
        List<Object[]> list = query.getResultList();
        List<String> collect = list.stream().map( arr -> String.valueOf(arr[0])).collect(Collectors.toList());
        baseResponse.setResult(collect);
        return baseResponse;
    }

    @Override
    public BaseResponse listByFilter(FScheduleDto dto) {
        BaseResponse baseResponse = BaseResponse.initialize();
        Specification<FSchedule> specification = Specification.where(dto.getId() == null ? null : FScheduleSpecification.ifContentEq("id", dto.getId()))
                .and(dto.getCreator() == null || dto.getCreator().equals("") ? null : FScheduleSpecification.ifContentEq("creator", dto.getCreator()))
                .and(dto.getVoucherId() == null ? null : FScheduleSpecification.ifContentEq("voucherId", dto.getVoucherId()));
        try {
            List<FSchedule> data = r.findAll(specification);
            baseResponse.setResult(data);
        } catch (Exception e) {
            log.info(e.getMessage());
            baseResponse.setCode(1);
            baseResponse.setMsg("查询出错");
        }
        return baseResponse;
    }

    @Override
    public List<FSchedule> findByVoucherTypeAndVoucherId(Integer voucherId, Integer voucherType) {
        try {
            List<FSchedule> list = r.findByVoucherIdAndVoucherType(voucherId, voucherType);
            if (list.size() > 0) {
                return list;
            }
        } catch (Exception e) {
            log.error(e.getLocalizedMessage());
        }
        return null;
    }
}
