package cn.fyg.schedule.controller.functions;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.service.core.FImportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


@RestController
@RequestMapping("import")
@Slf4j
public class DataImportController {
    private final FImportService service;
    public DataImportController(FImportService service) {
        this.service = service;
    }

    @PostMapping("excel")
    public BaseResponse uploadExcel(@RequestParam("file") MultipartFile file, String scheme, String creator, Integer start, Integer headerIndex) {

        return service.excelToMap(file, scheme, creator, start, headerIndex);
    }

    public static Class<?> getClazz(String clazzName){
        try {
            return Class.forName(clazzName);
        } catch (ClassNotFoundException e) {
            log.error(e.getMessage());
            return null;
        }
    }
}
