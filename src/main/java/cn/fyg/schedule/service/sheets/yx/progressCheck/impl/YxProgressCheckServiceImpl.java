package cn.fyg.schedule.service.sheets.yx.progressCheck.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.yuanxin.progressCheck.YxProgressCheckRepository;
import cn.fyg.schedule.pojo.core.ScopeQueryDate;
import cn.fyg.schedule.pojo.core.ScopeQueryInteger;
import cn.fyg.schedule.pojo.dto.project.yx.progressCheck.YxProgressCheckDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.pojo.project.yuanXin.progressCheck.YxProgressCheck;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.sheets.yx.progressCheck.YxProgressCheckService;
import cn.fyg.schedule.specification.yx.YxProgressCheckSpecification;
import cn.fyg.schedule.utils.DatePeriodUtil;
import cn.fyg.schedule.utils.GsonUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class YxProgressCheckServiceImpl implements YxProgressCheckService {
    private final YxProgressCheckRepository r;

    public YxProgressCheckServiceImpl(YxProgressCheckRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse save(YxProgressCheckDto dto) {
        BaseResponse response = BaseResponse.initialize();
        try {
            YxProgressCheck data = YxProgressCheck.initialize(dto);
            // 在事务内保存并立即预加载
            YxProgressCheck savedData = r.save(data);
            preloadLazyCollections(savedData);
            response.setResult(savedData);
        } catch (Exception e) {
            log.error(e.getMessage());
            response.setCode(1);
            response.setMsg("保存失败");
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse findById(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        try {
            // 直接在事务内查询和预加载
            Optional<YxProgressCheck> optional = r.findById(id);
            if (optional.isPresent()) {
                YxProgressCheck data = optional.get();
                // 立即在事务内预加载懒加载的集合
                preloadLazyCollections(data);
                response.setResult(data);
            } else {
                response.setCode(1);
                response.setMsg("数据不存在");
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse filter(YxProgressCheckDto dto, Pagination pagination) {
        BaseResponse response = BaseResponse.initialize();
        try {
            Sort sort = Sort.unsorted();
            if (!StrUtil.isEmpty(pagination.getColumnKey())) {
                if (!pagination.getOrder().equals("false")) {
                    sort = Sort.by(pagination.getOrder().equals("ascend") ? Sort.Direction.ASC : Sort.Direction.DESC, pagination.getColumnKey());
                }
            }
            Pageable pageable = PageRequest.of(pagination.getCurrentPage(), pagination.getPageSize(), sort);
            Specification<YxProgressCheck> specification = getSpecification(dto);
            Page<YxProgressCheck> data = r.findAll(specification, pageable);
            // 立即在事务内预加载懒加载的集合
            for (YxProgressCheck check : data.getContent()) {
                preloadLazyCollections(check);
            }
            response.setResult(data);
        } catch (Exception e) {
            log.error(e.getMessage());
            response.setCode(1);
            response.setMsg("failed");
        }
        return response;
    }

    private Specification<YxProgressCheck> getSpecification(YxProgressCheckDto dto) {
        return Specification.where(dto.getEventStatus() == null ? null : YxProgressCheckSpecification.ifEq("eventStatus", dto.getEventStatus()))
                .and(dto.getDataStatus() == null ? null : YxProgressCheckSpecification.ifEq("dataStatus", dto.getDataStatus()))
                .and(StrUtil.isEmpty(dto.getInspectionDepartment()) ? null : YxProgressCheckSpecification.ifContain("inspectionDepartment", dto.getInspectionDepartment()))
                .and(StrUtil.isEmpty(dto.getCreatorName()) ? null : YxProgressCheckSpecification.ifContain("creatorName", dto.getCreatorName()))
                .and(StrUtil.isEmpty(dto.getYxProjectName()) ? null : YxProgressCheckSpecification.ifContain("yxProjectName", dto.getYxProjectName()))
                .and(StrUtil.isEmpty(dto.getYxProblemDescription()) ? null : YxProgressCheckSpecification.ifContain("yxProblemDescription", dto.getYxProblemDescription()))
                .and(dto.getInspectionDate() == null ? null : YxProgressCheckSpecification.ifDateEq("inspectionDate", DateUtil.date(dto.getInspectionDate())))
                .and(StrUtil.isEmpty(dto.getInspectionDateQuery()) ? null : getScopeQuery("inspectionDate", dto.getInspectionDateQuery(), 0))
                .and(StrUtil.isEmpty(dto.getWorkDateQuery()) ? null : getScopeQuery("workData", dto.getWorkDateQuery(), 0))
                .and(StrUtil.isEmpty(dto.getYxTimeLimitQuery()) ? null : getScopeQuery("yxTimeLimit", dto.getYxTimeLimitQuery(), 1));
    }

    private Specification<YxProgressCheck> getScopeQuery(String column, String jsonStr) {
        List<ScopeQueryDate> list = GsonUtil.fromJsonList(jsonStr, ScopeQueryDate.class);
        if (list.size() > 1) {
            Date dateStart = DatePeriodUtil.getDate(list.get(0).getValue());
            Date dateStop = DatePeriodUtil.getDate(list.get(1).getValue());
            return dateStart.compareTo(dateStop) > 0 ? YxProgressCheckSpecification.ifDateBetween(column, dateStop, dateStart) : YxProgressCheckSpecification.ifDateBetween(column, dateStart, dateStop);
        } else {
            return getScopeQuery(column, list.get(0));
        }
    }

    private Specification<YxProgressCheck> getScopeQuery(String column, ScopeQueryDate query) {
        Date date = DatePeriodUtil.getDate(query.getValue());
        switch (query.getSymbol()) {
            case "<":
                return YxProgressCheckSpecification.ifDateLessThan(column, date);
            case "<=":
                return YxProgressCheckSpecification.ifDateLessThanOrEqualTo(column, date);
            case ">":
                return YxProgressCheckSpecification.ifDateGreaterThan(column, date);
            case ">=":
                return YxProgressCheckSpecification.ifDateGreaterThanOrEqualTo(column, date);
            default:
                return YxProgressCheckSpecification.ifDateEq(column, date);
        }
    }

    private Specification<YxProgressCheck> getScopeQueryNumber(String column, String jsonStr) {
        Specification<YxProgressCheck> data = null;
        List<ScopeQueryInteger> list = GsonUtil.fromJsonList(jsonStr, ScopeQueryInteger.class);
        if (!ObjectUtil.isEmpty(list)) {
            if (list.size() > 1) {
                if (list.get(0).getValue().compareTo(list.get(1).getValue()) > 0) {
                    data = YxProgressCheckSpecification.ifBetween(column, list.get(1).getValue(), list.get(0).getValue());
                } else {
                    data = YxProgressCheckSpecification.ifBetween(column, list.get(0).getValue(), list.get(1).getValue());
                }
            } else {
                data = getScopeQuery(column, list.get(0));
            }
        }
        return data;
    }

    private Specification<YxProgressCheck> getScopeQuery(String column, ScopeQueryInteger query) {
        switch (query.getSymbol()) {
            case "<":
                return YxProgressCheckSpecification.ifLessThan(column, query.getValue());
            case "<=":
                return YxProgressCheckSpecification.ifLessThanOrEqualTo(column, query.getValue());
            case ">":
                return YxProgressCheckSpecification.ifGreaterThan(column, query.getValue());
            case ">=":
                return YxProgressCheckSpecification.ifGreaterThanOrEqualTo(column, query.getValue());
            default:
                return YxProgressCheckSpecification.ifEq(column, query.getValue());
        }
    }

    /**
     *
     * @param column
     * @param jsonStr
     * @param type 0: date 1: number
     * @return
     */
    private Specification<YxProgressCheck> getScopeQuery(String column, String jsonStr, Integer type) {
        Specification<YxProgressCheck> data = null;
        if (type == 0) {
            List<ScopeQueryDate> list = GsonUtil.fromJsonList(jsonStr, ScopeQueryDate.class);
            for (ScopeQueryDate tmp : list) {
                data = data != null ? data.and(getScopeQuery(column, tmp)) : getScopeQuery(column, tmp);
            }
        } else if (type == 1) {
            List<ScopeQueryInteger> list = GsonUtil.fromJsonList(jsonStr, ScopeQueryInteger.class);
            for (ScopeQueryInteger tmp : list) {
                data = data != null ? data.and(getScopeQuery(column, tmp)) : getScopeQuery(column, tmp);
            }
        }
        return data;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public YxProgressCheck getById(Integer id) {
        try {
            Optional<YxProgressCheck> optional = r.findById(id);
            if (optional.isPresent()) {
                YxProgressCheck data = optional.get();
                // 预加载懒加载的集合，避免LazyInitializationException
                preloadLazyCollections(data);
                return data;
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    @Override
    public BaseResponse listInspectionDepartmentHistory() {
        BaseResponse response = BaseResponse.initialize();
        try {
            List<String> list = r.getInspectionDepartmentHistory();
            response.setResult(list);
        } catch (Exception e) {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse findByIdIn(List<Integer> ids) {
        BaseResponse response = BaseResponse.initialize();
        try {
            // 在事务内完成查询和预加载
            List<YxProgressCheck> list = r.findByDataStatusAndIdIn(0, ids);
            if (!list.isEmpty()) {
                // 立即在事务内预加载懒加载的集合
                for (YxProgressCheck data : list) {
                    preloadLazyCollections(data);
                }
                response.setResult(list);
            } else {
                response.setCode(1);
                response.setMsg("未查到数据");
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }



    /**
     * 预加载懒加载的集合，避免LazyInitializationException
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    private void preloadLazyCollections(YxProgressCheck data) {
        if (data != null) {
            // 触发懒加载 - 我们只需要触发加载，不需要使用返回值
            if (data.getItems() != null) {
                data.getItems().size(); // 触发懒加载
            }
            if (data.getSignatures() != null) {
                data.getSignatures().size(); // 触发懒加载
            }
        }
    }
}
