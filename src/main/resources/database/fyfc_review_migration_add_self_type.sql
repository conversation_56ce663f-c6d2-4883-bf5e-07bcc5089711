-- =====================================================
-- FYFC Review 系统数据库迁移脚本
-- 添加 SELF 评价人类型支持
-- =====================================================

-- 1. 修改 fyfc_evaluation_scores 表的 type 字段，添加 'self' 选项
ALTER TABLE `fyfc_evaluation_scores` 
MODIFY COLUMN `type` enum('unknown','self','employee','colleague','manager','admin') NOT NULL COMMENT '评价人角色类型';

-- 2. 将现有的 'employee' 类型数据迁移为 'self' 类型
-- 这是为了保持语义一致性：employee 类型实际上表示的是自评
UPDATE `fyfc_evaluation_scores` 
SET `type` = 'self' 
WHERE `type` = 'employee';

-- 3. 验证迁移结果
-- 查看迁移后的数据分布
SELECT 
    `type`,
    COUNT(*) as count,
    CONCAT(ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM `fyfc_evaluation_scores`), 2), '%') as percentage
FROM `fyfc_evaluation_scores` 
GROUP BY `type`
ORDER BY count DESC;

-- 4. 检查是否还有 'employee' 类型的记录（应该为 0）
SELECT COUNT(*) as remaining_employee_records
FROM `fyfc_evaluation_scores` 
WHERE `type` = 'employee';

-- 5. 显示迁移完成信息
SELECT 
    'Migration completed successfully!' as status,
    NOW() as migration_time,
    (SELECT COUNT(*) FROM `fyfc_evaluation_scores` WHERE `type` = 'self') as self_records,
    (SELECT COUNT(*) FROM `fyfc_evaluation_scores` WHERE `type` = 'colleague') as colleague_records,
    (SELECT COUNT(*) FROM `fyfc_evaluation_scores` WHERE `type` = 'manager') as manager_records;
