package cn.fyg.schedule.service.sheets.yx.security.notification;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.project.yuanXin.security.notification.YxSecurityNotiSign;

import java.util.List;

public interface YxSecurityNotiSignService {
    BaseResponse save(YxSecurityNotiSign data);
    BaseResponse delete(Integer id);
    BaseResponse findById(Integer id);
    BaseResponse findByParentId(Integer id);
}
