package cn.fyg.schedule.pojo.fyg.accounting;

import cn.fyg.schedule.enums.fyg.accounting.CounterpartyAccountType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

@Entity
@Setter
@Getter
@ToString
@Table(name = "fyg_counterparty_account_project_types")
public class FygCounterpartyAccountProjectTypes {

    // 主键id，自动生成
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    // 项目ID，关联项目表
    @Column(name = "project_id", nullable = false)
    private Integer projectId;

    // 对方户名ID，关联对方账户表
    @Column(name = "counterparty_account_id")
    private Integer counterpartyAccountId;

    // 对方户名类型，枚举类型
    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false)
    private CounterpartyAccountType type;

    // 数据状态，1表示正常，0表示删除
    @Column(name = "data_status", columnDefinition = "tinyint(4) default 1")
    private Integer dataStatus;

    // 创建时间，自动生成
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;

    // 更新时间，自动更新
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at", nullable = false)
    private Date updatedAt;

    // 创建者
    @Column(name = "created_by", length = 50)
    private String createdBy;

    // 最后更新者
    @Column(name = "updated_by", length = 50)
    private String updatedBy;

    // 外键关联注解
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", referencedColumnName = "id", insertable = false, updatable = false)
    private FygProjects project;

}

