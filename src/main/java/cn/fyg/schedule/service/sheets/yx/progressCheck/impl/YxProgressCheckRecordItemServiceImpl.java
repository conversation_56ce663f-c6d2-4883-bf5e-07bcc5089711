package cn.fyg.schedule.service.sheets.yx.progressCheck.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.yuanxin.progressCheck.YxProgressCheckRecordItemRepository;
import cn.fyg.schedule.pojo.project.yuanXin.progressCheck.YxProgressCheckRecordItem;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.sheets.yx.progressCheck.YxProgressCheckRecordItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class YxProgressCheckRecordItemServiceImpl implements YxProgressCheckRecordItemService {
    private final YxProgressCheckRecordItemRepository r;

    public YxProgressCheckRecordItemServiceImpl(YxProgressCheckRecordItemRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(YxProgressCheckRecordItem data) {
        return BaseService.save(data, r, YxProgressCheckRecordItem.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        YxProgressCheckRecordItem data = getDataById(id);
        if (data != null) {
            response.setResult(data);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    private YxProgressCheckRecordItem getDataById(Integer id) {
        try {
            Optional<YxProgressCheckRecordItem> optional = r.findById(id);
            if (optional.isPresent()) {
                return optional.get();
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    @Override
    public BaseResponse findByParentId(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        List<YxProgressCheckRecordItem> data = getItems(id);
        if (data != null) {
            response.setResult(data);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    private List<YxProgressCheckRecordItem> getItems(Integer id) {
        try {
            List<YxProgressCheckRecordItem> items = r.findByParentIdOrderByCreateDateDesc(id);
            if (!items.isEmpty()) {
                return items;
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }
}
