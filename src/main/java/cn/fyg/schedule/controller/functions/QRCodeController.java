package cn.fyg.schedule.controller.functions;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.service.qrcode.QRCodeService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("qr")
public class QRCodeController {
    private final QRCodeService service;
    public QRCodeController(QRCodeService service) {
        this.service = service;
    }

    @PostMapping("generate")
    public BaseResponse generateQR(String url) {
        return service.generateQR(url);
    }
}
