package cn.fyg.schedule.service.statement.daily.report.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.statement.daily.report.FDailySellingReportRepository;
import cn.fyg.schedule.pojo.dto.statement.daily.report.AnnualSellingReport;
import cn.fyg.schedule.pojo.dto.statement.daily.report.DailySellingReport;
import cn.fyg.schedule.pojo.statement.daily.report.FDailySellingReport;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.statement.daily.report.FDailySellingReportService;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class FDailySellingReportServiceImpl implements FDailySellingReportService {
    private final FDailySellingReportRepository r;

    public FDailySellingReportServiceImpl(FDailySellingReportRepository r) {
        this.r = r;
    }

    @Override
    public BaseResponse listProjects() {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<String> list = r.listProjects();
            baseResponse.setResult(list);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse listDaiySellingReportGroupByProject() {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<DailySellingReport> list = r.listNewestData();
            baseResponse.setResult(list);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Optional<FDailySellingReport> result = r.findById(id);
            result.ifPresent(baseResponse::setResult);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FDailySellingReport data) {
        return BaseService.save(data, r, FDailySellingReport.class);
    }

    @Override
    public BaseResponse findTopByCreatorAndFName(String creator, String name) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Optional<FDailySellingReport> result = r.findTopByCreatorAndFnameOrderByDataDateDesc(creator, name);
            result.ifPresent(baseResponse::setResult);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse annualSellingReport(String creator, String name) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Integer currentYear = DateUtil.year(new Date());
            Date begin = DateUtil.parse(currentYear + "-01-01"),
                    end = DateUtil.parse(currentYear + "-12-31");
            List<FDailySellingReport> list = r.findByCreatorAndFnameAndDataDateBetween(creator, name, begin, end);
            baseResponse.setResult(getAnnualSellingReport(list));
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    private AnnualSellingReport getAnnualSellingReport(List<FDailySellingReport> list) {
        AnnualSellingReport report = new AnnualSellingReport();
        Integer annualSoldNum = list.stream().map(FDailySellingReport::getJrysnum).reduce(0, Integer::sum);
        Double annualSaleAmount  = list.stream().map(e-> new BigDecimal(String.valueOf(e.getJrxsje())))
                .reduce(BigDecimal.ZERO, BigDecimal::add).doubleValue();
        Double annualReturn = list.stream().map(e-> new BigDecimal(String.valueOf(e.getJrhk())))
                .reduce(BigDecimal.ZERO, BigDecimal::add).doubleValue();
        report.setJnhk(annualReturn);
        report.setJnxsje(annualSaleAmount);
        report.setJnysnum(annualSoldNum);
        return report;
    }

    @Override
    public BaseResponse listNewestData() {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<DailySellingReport> list = r.listNewestData();
            baseResponse.setResult(list);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }
}
