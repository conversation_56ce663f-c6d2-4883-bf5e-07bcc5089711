package cn.fyg.schedule.controller.functions;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.FWebView;
import cn.fyg.schedule.pojo.FWebViewDto;
import cn.fyg.schedule.pojo.dto.query.Base;
import cn.fyg.schedule.service.FWebViewService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("webview")
public class FWebViewController {
    private final FWebViewService s;

    public FWebViewController(FWebViewService s) {
        this.s = s;
    }

    @PostMapping("save")
    public BaseResponse save(FWebViewDto dto) {
        FWebView data = FWebView.initialize(dto);
        return s.save(data);
    }

    @PostMapping("find")
    public BaseResponse find(Integer id) {
        return s.findById(id);
    }

    @PostMapping("delete")
    public BaseResponse delete(Base query) {
        return s.delete(query.getId());
    }
}
