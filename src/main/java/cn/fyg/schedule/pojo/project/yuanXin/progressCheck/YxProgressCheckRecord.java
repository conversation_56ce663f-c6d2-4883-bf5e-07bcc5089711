package cn.fyg.schedule.pojo.project.yuanXin.progressCheck;

import cn.fyg.schedule.pojo.dto.project.yx.progressCheck.YxProgressCheckRecordDto;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Setter
@Getter
@ToString
@Table(name = "yx_progress_check_record")
public class YxProgressCheckRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Column(name = "parent_id")
    private Integer parentId;
    private String creator;
    private String creatorName;
    private String creatorPhone;
    private String avatar;
    private String records;
    private Integer recordStatus;
    @Column(name = "create_date")
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @Column(name = "update_date")
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "parent_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    @ToString.Exclude
    List<YxProgressCheckRecordItem> items = new ArrayList<>();

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "parent_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    @ToString.Exclude
    List<YxProgressCheckRecordSign> signatures = new ArrayList<>();

    public static YxProgressCheckRecord initialize(YxProgressCheckRecordDto dto) {
        YxProgressCheckRecord data = new YxProgressCheckRecord();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        data.setParentId(dto.getParentId());
        data.setCreator(dto.getCreator());
        data.setCreatorName(dto.getCreatorName());
        data.setCreatorPhone(dto.getCreatorPhone());
        data.setAvatar(dto.getAvatar());
        data.setRecords(dto.getRecords());
        data.setRecordStatus(dto.getRecordStatus());
        if (dto.getCreateDate() != null) {
            data.setCreateDate(DateUtil.date(dto.getCreateDate()));
        }
        return data;
    }
}
