package cn.fyg.schedule.service.fyjs.bidding.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.fyjs.bidding.FyjsBiddingInfoRepository;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.core.ScopeQueryBigDecimal;
import cn.fyg.schedule.pojo.core.ScopeQueryDate;
import cn.fyg.schedule.pojo.core.ScopeQueryInteger;
import cn.fyg.schedule.pojo.dto.fyjs.bidding.FyjsBiddingInfoDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.pojo.fyjs.bidding.FyjsBiddingInfo;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.fyjs.bidding.FyjsBiddingInfoService;
import cn.fyg.schedule.specification.fyjs.bidding.FyjsBiddingInfoSpecification;
import cn.fyg.schedule.utils.DatePeriodUtil;
import cn.fyg.schedule.utils.GsonUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class FyjsBiddingInfoServiceImpl implements FyjsBiddingInfoService {
    private final FyjsBiddingInfoRepository r;

    public FyjsBiddingInfoServiceImpl(FyjsBiddingInfoRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse save(FyjsBiddingInfoDto dto) {
        BaseResponse response = BaseResponse.initialize();
        try {
            FyjsBiddingInfo data = FyjsBiddingInfo.initialized(dto);
            // 在事务内保存并立即预加载
            FyjsBiddingInfo savedInfo = r.save(data);
            preloadLazyCollections(savedInfo);
            response.setResult(savedInfo);
        } catch (Exception e) {
            log.error(e.getMessage());
            response.setCode(1);
            response.setMsg("保存失败");
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse filter(Pagination pagination, FyjsBiddingInfoDto dto) {
        BaseResponse response = BaseResponse.initialize();
        try {
            Sort sort = Sort.unsorted();
            if (!StrUtil.isEmpty(pagination.getColumnKey())) {
                if (!pagination.getOrder().equals("false")) {
                    sort = Sort.by(pagination.getOrder().equals("ascend") ? Sort.Direction.ASC : Sort.Direction.DESC, pagination.getColumnKey());
                }
            }
            Pageable pageable = PageRequest.of(pagination.getCurrentPage(), pagination.getPageSize(), sort);
            Specification<FyjsBiddingInfo> specification = getSpecification(dto);

            // 在事务内完成查询和预加载
            Page<FyjsBiddingInfo> data = r.findAll(specification, pageable);
            // 立即在事务内预加载懒加载的集合
            for (FyjsBiddingInfo info : data.getContent()) {
                preloadLazyCollections(info);
            }

            response.setResult(data);
        } catch (JpaSystemException e) {
            log.info(e.getMessage());
            response.setCode(1);
            response.setMsg("查询出错");
        } catch (Exception e) {
            log.error(e.getMessage());
            response.setCode(1);
            response.setMsg("查询出错");
        }
        return response;
    }



    private Specification<FyjsBiddingInfo> getSpecification(FyjsBiddingInfoDto dto) {
        return Specification.where(dto.getBidStatus() == null ? null : FyjsBiddingInfoSpecification.ifEq("bidStatus", dto.getBidStatus()))
                .and(dto.getDataStatus() == null ? null : FyjsBiddingInfoSpecification.ifEq("dataStatus", dto.getDataStatus()))
                .and(dto.getIsOpened() == null ? null : FyjsBiddingInfoSpecification.ifEq("isOpened", dto.getIsOpened()))
                .and(StrUtil.isEmpty(dto.getProjectName()) ? null : FyjsBiddingInfoSpecification.ifContain("projectName", dto.getProjectName()))
                .and(StrUtil.isEmpty(dto.getProjectCostQuery()) ? null : getScopeQuery("projectCost", dto.getProjectCostQuery(), 2))
                .and(StrUtil.isEmpty(dto.getProjectTimeLimitQuery()) ? null : getScopeQuery("projectTimeLimit", dto.getProjectTimeLimitQuery(), 1))
                .and(StrUtil.isEmpty(dto.getBidEndDateQuery()) ? null : getScopeQuery("projectCost", dto.getBidEndDateQuery(), 0))
                ;
    }

    private Specification<FyjsBiddingInfo> getScopeQuery(String column, ScopeQueryDate query) {
        Date date = DatePeriodUtil.getDate(query.getValue());
        switch (query.getSymbol()) {
            case "<":
                return FyjsBiddingInfoSpecification.ifDateLessThan(column, date);
            case "<=":
                return FyjsBiddingInfoSpecification.ifDateLessThanOrEqualTo(column, date);
            case ">":
                return FyjsBiddingInfoSpecification.ifDateOverThan(column, date);
            case ">=":
                return FyjsBiddingInfoSpecification.ifDateOverThanOrEqualTo(column, date);
            default:
                return FyjsBiddingInfoSpecification.ifDateEq(column, date);
        }
    }

    private Specification<FyjsBiddingInfo> getScopeQuery(String column, ScopeQueryInteger query) {
        switch (query.getSymbol()) {
            case "<":
                return FyjsBiddingInfoSpecification.ifNumberLessThan(column, query.getValue());
            case "<=":
                return FyjsBiddingInfoSpecification.ifNumberLessThanOrEqualTo(column, query.getValue());
            case ">":
                return FyjsBiddingInfoSpecification.ifNumberOverThan(column, query.getValue());
            case ">=":
                return FyjsBiddingInfoSpecification.ifNumberOverThanOrEqualTo(column, query.getValue());
            default:
                return FyjsBiddingInfoSpecification.ifEq(column, query.getValue());
        }
    }

    private Specification<FyjsBiddingInfo> getScopeQuery(String column, ScopeQueryBigDecimal query) {
        switch (query.getSymbol()) {
            case "<":
                return FyjsBiddingInfoSpecification.ifNumberLessThan(column, query.getValue());
            case "<=":
                return FyjsBiddingInfoSpecification.ifNumberLessThanOrEqualTo(column, query.getValue());
            case ">":
                return FyjsBiddingInfoSpecification.ifNumberOverThan(column, query.getValue());
            case ">=":
                return FyjsBiddingInfoSpecification.ifNumberOverThanOrEqualTo(column, query.getValue());
            default:
                return FyjsBiddingInfoSpecification.ifEq(column, query.getValue());
        }
    }

    /**
     *
     * @param column
     * @param jsonStr
     * @param type 0: date 1: number(Integer) : 2 number(BigDecimal)
     * @return
     */
    private Specification<FyjsBiddingInfo> getScopeQuery(String column, String jsonStr, Integer type) {
        Specification<FyjsBiddingInfo> data = null;
        if (type == 0) {
            List<ScopeQueryDate> list = GsonUtil.fromJsonList(jsonStr, ScopeQueryDate.class);
            for (ScopeQueryDate tmp : list) {
                data = data != null ? data.and(getScopeQuery(column, tmp)) : getScopeQuery(column, tmp);
            }
        } else if (type == 1) {
            List<ScopeQueryInteger> list = GsonUtil.fromJsonList(jsonStr, ScopeQueryInteger.class);
            for (ScopeQueryInteger tmp : list) {
                data = data != null ? data.and(getScopeQuery(column, tmp)) : getScopeQuery(column, tmp);
            }
        } else if (type == 2) {
            List<ScopeQueryBigDecimal> list = GsonUtil.fromJsonList(jsonStr, ScopeQueryBigDecimal.class);
            for (ScopeQueryBigDecimal tmp : list) {
                data = data != null ? data.and(getScopeQuery(column, tmp)) : getScopeQuery(column, tmp);
            }
        }
        return data;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse findById(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        try {
            // 直接在事务内查询和预加载
            Optional<FyjsBiddingInfo> optional = r.findById(id);
            if (optional.isPresent()) {
                FyjsBiddingInfo data = optional.get();
                // 立即在事务内预加载懒加载的集合
                preloadLazyCollections(data);
                response.setResult(data);
            } else {
                response.setCode(1);
                response.setMsg("数据不存在");
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    /**
     * 预加载懒加载的集合，避免LazyInitializationException
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    private void preloadLazyCollections(FyjsBiddingInfo info) {
        if (info != null) {
            try {
                // 触发第一层懒加载
                if (info.getItems() != null) {
                    info.getItems().size(); // 触发懒加载
                }
                if (info.getTenderers() != null) {
                    info.getTenderers().size(); // 触发懒加载
                    // 触发第二层懒加载：FyjsBiddingTenderer.documents
                    info.getTenderers().forEach(tenderer -> {
                        if (tenderer.getDocuments() != null) {
                            tenderer.getDocuments().size(); // 触发嵌套懒加载
                        }
                    });
                }
                if (info.getOpeners() != null) {
                    info.getOpeners().size(); // 触发懒加载
                }
            } catch (Exception e) {
                log.warn("预加载懒加载集合时出错: {}", e.getMessage());
                // 不抛出异常，允许继续执行
            }
        }
    }
}
