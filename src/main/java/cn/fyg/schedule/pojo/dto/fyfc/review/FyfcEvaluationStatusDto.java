package cn.fyg.schedule.pojo.dto.fyfc.review;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 评价状态DTO
 * 对应 TypeScript 中的 EvaluationStatus 枚举
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FyfcEvaluationStatusDto {
    
    /**
     * 状态代码
     */
    private String code;
    
    /**
     * 状态名称
     */
    private String name;
    
    /**
     * 状态描述
     */
    private String description;
    
    /**
     * 排序顺序
     */
    private Integer order;
    
    // 预定义的状态常量
    public static final FyfcEvaluationStatusDto SELF = new FyfcEvaluationStatusDto("self", "自评阶段", "等待员工完成自评", 1);
    public static final FyfcEvaluationStatusDto COLLEAGUE = new FyfcEvaluationStatusDto("colleague", "同事评价阶段", "等待同事完成评价", 2);
    public static final FyfcEvaluationStatusDto MANAGER = new FyfcEvaluationStatusDto("manager", "主管评价阶段", "等待主管完成评价", 3);
    public static final FyfcEvaluationStatusDto COMPLETED = new FyfcEvaluationStatusDto("completed", "评价完成", "所有评价已完成", 4);
}
