package cn.fyg.schedule.pojo.fyg.accounting;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;


@Entity
@Setter
@Getter
@ToString
@Table(name = "fyg_counterparty_accounts")
public class FygCounterpartyAccounts {

    // 主键ID，自动生成
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    // 对方户名
    @Column(name = "account_name", length = 255, nullable = false)
    private String accountName;

    // 数据状态，1表示正常，0表示删除
    @Column(name = "data_status", columnDefinition = "tinyint(4) default 1")
    private Integer dataStatus;

    // 创建时间，自动生成
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;

    // 更新时间，自动更新
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at", nullable = false)
    private Date updatedAt;

    // 创建者
    @Column(name = "created_by", length = 255)
    private String createdBy;

    // 最后更新者
    @Column(name = "updated_by", length = 255)
    private String updatedBy;
}

