package cn.fyg.schedule.service.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.schedule.FScheduleStatusRepository;
import cn.fyg.schedule.pojo.FScheduleStatus;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.FScheduleStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;

@Service
@Slf4j
public class FScheduleStatusServiceImpl implements FScheduleStatusService {
    @Autowired private FScheduleStatusRepository r;
    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FScheduleStatus status) {
        return BaseService.save(status, r, FScheduleStatus.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) { return BaseService.delete(id, r); }

    @Override
    public BaseResponse findByScheduleId(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FScheduleStatus> result = r.findByScheduleIdOrderByCreateDateDesc(id);
            baseResponse.setResult(result);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }
}
