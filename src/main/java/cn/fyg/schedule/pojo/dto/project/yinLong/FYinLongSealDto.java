package cn.fyg.schedule.pojo.dto.project.yinLong;

import lombok.Data;
import java.math.BigDecimal;

@Data
public class FYinLongSealDto {
    private Integer id;
    private String number;
    private String creator;
    private String departmentName;
    private String sealType;
    private String contacts;
    private String phoneNumber;
    private BigDecimal amount;
    private String auditDept;
    private String sealContent;
    private Long transactionDate;
    private Long createDate;
}
