package cn.fyg.schedule.controller.general;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.news.awards.FAwardsItemDto;
import cn.fyg.schedule.pojo.dto.news.awards.FCompanyAwardsDto;
import cn.fyg.schedule.pojo.dto.news.awards.FProjectAwardsDto;
import cn.fyg.schedule.pojo.dto.news.awards.FPersonalAwardsDto;
import cn.fyg.schedule.pojo.news.awads.*;
import cn.fyg.schedule.service.news.awards.*;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("awards")
public class FAwardsController {
    private final FCompanyAwardsS cs;
    private final FCompanyAwardsItemS cis;
    private final FPersonalAwardsS ps;
    private final FPersonalAwardsItemS pis;
    private final FProjectAwardsS pjs;
    private final FProjectAwardsItemS pjis;

    public FAwardsController(FCompanyAwardsS cs, FCompanyAwardsItemS cis,
                             FPersonalAwardsS ps, FPersonalAwardsItemS pis,
                             FProjectAwardsS pjs, FProjectAwardsItemS pjis) {
        this.cs = cs;
        this.cis = cis;
        this.ps = ps;
        this.pis = pis;
        this.pjis = pjis;
        this.pjs = pjs;
    }

    @PostMapping("p/c/list")
    public BaseResponse getPersonalCompanyList() {
        return ps.getCompanyHistoryList();
    }

    @PostMapping("p/save")
    public BaseResponse personalAwardsSave(FPersonalAwardsDto dto) {
        FPersonalAwards data = FPersonalAwards.initialize(dto);
        return ps.save(data);
    }

    @PostMapping("p/list/all")
    public BaseResponse pListAll() {
        return ps.listAll();
    }

    @PostMapping("p/list/company")
    public BaseResponse pListCompanyIn(String companies) {
        return ps.listCompanyIn(StrUtil.split(companies, ","));
    }

    @PostMapping("p/list/date")
    public BaseResponse pListAllByDate(String year) {
        return ps.listByAwardDate(getBegin(year), getEnd(year));
    }

    @PostMapping("p/i/save")
    public BaseResponse personalAwardsItemSave(FAwardsItemDto dto) {
        FPersonalAwardsItem data = FPersonalAwardsItem.initialize(dto);
        return pis.save(data);
    }

    @PostMapping("p/i/delete")
    public BaseResponse personalAwardsItemDelete(Integer id) {
        return pis.delete(id);
    }

    @PostMapping("p/i/list/id")
    public BaseResponse personalAwardsItemListByAwardId(Integer id) {
        return pis.listByAwardId(id);
    }

    @PostMapping("p/find/id")
    public BaseResponse personalAwardsfindById(Integer id) {
        return ps.findById(id);
    }

    @PostMapping("p/delete")
    public BaseResponse personalAwardsDelete(Integer id) {
        return ps.delete(id);
    }

    @PostMapping("c/c/list")
    public BaseResponse getCCList() {
        return cs.getCompanyHistoryList();
    }

    @PostMapping("c/save")
    public BaseResponse companyAwardsSave(FCompanyAwardsDto dto) {
        FCompanyAwards data = FCompanyAwards.initialize(dto);
        return cs.save(data);
    }

    @PostMapping("c/list/all")
    public BaseResponse cListAll() {
        return cs.listAll();
    }

    @PostMapping("c/list/company")
    public BaseResponse cListCompanyIn(String companies) {
        return cs.listCompanyIn(StrUtil.split(companies, ","));
    }

    @PostMapping("c/list/date")
    public BaseResponse cListAllByDate(String year) {
        return cs.listByAwardDate(getBegin(year), getEnd(year));
    }

    @PostMapping("c/i/save")
    public BaseResponse companyAwardsItemSave(FAwardsItemDto dto) {
        FCompanyAwardsItem data = FCompanyAwardsItem.initialize(dto);
        return cis.save(data);
    }

    @PostMapping("c/i/delete")
    public BaseResponse companyAwardsItemDelete(Integer id) {
        return cis.delete(id);
    }

    @PostMapping("c/i/list/id")
    public BaseResponse companyAwardsItemListByAwardId(Integer id) {
        return cis.listByAwardId(id);
    }

    @PostMapping("c/find/id")
    public BaseResponse companyAwardsFindById(Integer id) {
        return cs.findById(id);
    }

    @PostMapping("c/delete")
    public BaseResponse companyAwardsDelete(Integer id) {
        return cs.delete(id);
    }

    @PostMapping("pj/c/list")
    public BaseResponse getPjCList() {
        return pjs.getCompanyHistoryList();
    }

    @PostMapping("pj/save")
    public BaseResponse companyAwardsSave(FProjectAwardsDto dto) {
        FProjectAwards data = FProjectAwards.initialize(dto);
        return pjs.save(data);
    }

    @PostMapping("pj/list/all")
    public BaseResponse pjListAll() {
        return pjs.listAll();
    }

    @PostMapping("pj/list/company")
    public BaseResponse pjListCompanyIn(String companies) {
        return pjs.listCompanyIn(StrUtil.split(companies, ","));
    }

    @PostMapping("pj/list/date")
    public BaseResponse pjListAllByDate(String year) {
        return pjs.listByAwardDate(getBegin(year), getEnd(year));
    }

    @PostMapping("pj/i/save")
    public BaseResponse pjAwardsItemSave(FAwardsItemDto dto) {
        FProjectAwardsItem data = FProjectAwardsItem.initialize(dto);
        return pjis.save(data);
    }

    @PostMapping("pj/i/delete")
    public BaseResponse pjAwardsItemDelete(Integer id) {
        return pjis.delete(id);
    }

    @PostMapping("pj/i/list/id")
    public BaseResponse pjAwardsItemListByAwardId(Integer id) {
        return pjis.listByAwardId(id);
    }

    @PostMapping("pj/find/id")
    public BaseResponse pjAwardsFindById(Integer id) {
        return pjs.findById(id);
    }

    @PostMapping("pj/delete")
    public BaseResponse pjAwardsDelete(Integer id) {
        return pjs.delete(id);
    }

    @PostMapping("datelist")
    public BaseResponse getDateList() {
        BaseResponse baseResponse = BaseResponse.initialize();
        int year = DateUtil.parseDate(DateUtil.now()).year();
        List<Integer> years = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            years.add(year - i);
        }
        baseResponse.setResult(years);
        return baseResponse;
    }

    private Date getBegin(String year) {
        return DateUtil.parseDate(year + "-01-01");
    }

    private Date getEnd(String year) {
        return DateUtil.parseDate(year + "-12-31");
    }

}
