package cn.fyg.schedule.service.sheets.yx.security.notification.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.yuanxin.security.notification.YxSecurityNotiRecordRepository;
import cn.fyg.schedule.pojo.dto.project.yx.security.notification.YxSecurityNotiRecordDto;
import cn.fyg.schedule.pojo.project.yuanXin.security.notification.YxSecurityNotiRecord;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.sheets.yx.security.notification.YxSecurityNotiRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class YxSecurityNotiRecordServiceImpl implements YxSecurityNotiRecordService {
    private final YxSecurityNotiRecordRepository r;

    public YxSecurityNotiRecordServiceImpl(YxSecurityNotiRecordRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(YxSecurityNotiRecordDto dto) {
        YxSecurityNotiRecord data = YxSecurityNotiRecord.initialize(dto);
        return BaseService.save(data, r, YxSecurityNotiRecord.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        YxSecurityNotiRecord data = getDataById(id);
        if (data != null) {
            response.setResult(data);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    private YxSecurityNotiRecord getDataById(Integer id) {
        try {
            Optional<YxSecurityNotiRecord> optional = r.findById(id);
            if (optional.isPresent()) {
                return optional.get();
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    @Override
    public BaseResponse findByParentId(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        List<YxSecurityNotiRecord> data = getItems(id, 1);
        if (data != null) {
            response.setResult(data);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    @Override
    public List<YxSecurityNotiRecord> listRecordByParentId(Integer id, Integer mark) {
        return getItems(id, mark);
    }

    @Override
    public BaseResponse ifApproved(Integer id, Integer recordStatus) {
        BaseResponse response = BaseResponse.initialize();
        List<YxSecurityNotiRecord> list = findByParentIdAndStatus(id, recordStatus);
        if (list != null) {
            response.setResult(list);
            response.setMsg("is approved");
        } else {
            response.setCode(1);
            response.setMsg("not been approved yet");
        }
        return response;
    }

    @Override
    public List<Integer> listParentIdByCreatorPhone(String creatorPhone) {
        try {
            List<Integer> list = r.listParentIdByCreatorPhone(creatorPhone);
            if (list.size() > 0) {
                return list;
            }
        } catch (Exception e) {
            log.error(e.getLocalizedMessage());
        }
        return null;
    }

    private List<YxSecurityNotiRecord> findByParentIdAndStatus(Integer id, Integer status) {
        try {
            List<YxSecurityNotiRecord> list = r.findByParentIdAndRecordStatus(id, status);
            if (list.size() > 0) {
                return list;
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    /**
     *
     * @param id
     * @param mark 0 sort by asc; 1 sort by desc
     * @return
     */
    private List<YxSecurityNotiRecord> getItems(Integer id, Integer mark) {
        try {
            List<YxSecurityNotiRecord> items = mark == 0 ? r.findByParentIdOrderByCreateDateAsc(id) : r.findByParentIdOrderByCreateDateDesc(id);
            if (!items.isEmpty()) {
                return items;
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }
}
