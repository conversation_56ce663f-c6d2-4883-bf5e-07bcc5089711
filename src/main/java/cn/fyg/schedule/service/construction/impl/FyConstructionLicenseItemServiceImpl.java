package cn.fyg.schedule.service.construction.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.construction.FyConstructionLicenseItemRepository;
import cn.fyg.schedule.pojo.construction.FyConstructionLicenseItem;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.construction.FyConstructionLicenseItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class FyConstructionLicenseItemServiceImpl implements FyConstructionLicenseItemService {
    private final FyConstructionLicenseItemRepository r;

    public FyConstructionLicenseItemServiceImpl(FyConstructionLicenseItemRepository r) {
        this.r = r;
    }

    @Override
    public BaseResponse save(FyConstructionLicenseItem data) {
        return BaseService.save(data, r, FyConstructionLicenseItem.class);
    }

    @Override
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        FyConstructionLicenseItem data = getById(id);
        if (data != null) {
            response.setResult(data);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    private FyConstructionLicenseItem getById(Integer id) {
        try {
            Optional<FyConstructionLicenseItem> optional = r.findById(id);
            if (optional.isPresent()) {
                return optional.get();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    @Override
    public BaseResponse findByParentId(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        List<FyConstructionLicenseItem> list = getByParentId(id);
        if (list != null) {
            response.setResult(list);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    private List<FyConstructionLicenseItem> getByParentId(Integer id) {
        try {
            List<FyConstructionLicenseItem> list = r.findByParentIdOrderByCreateDateDesc(id);
            return list;
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }
}
