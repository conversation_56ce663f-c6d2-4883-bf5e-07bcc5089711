package cn.fyg.schedule.service.statement.capital;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.statement.capital.FCapitalStatement;

import java.util.Date;
import java.util.List;

public interface FCapitalStatementService {
    BaseResponse save(FCapitalStatement data);
    BaseResponse delete(Integer id);
    BaseResponse findById(Integer id);
    BaseResponse findByStatementDate(Date date);
    List<FCapitalStatement> findLastStatement(Date date);
    List<FCapitalStatement> findAll();
    FCapitalStatement findFirstStatement();
    List<FCapitalStatement> findStatementInIds(Integer[] ids);
    BaseResponse listStatement(Integer[] ids);
}
