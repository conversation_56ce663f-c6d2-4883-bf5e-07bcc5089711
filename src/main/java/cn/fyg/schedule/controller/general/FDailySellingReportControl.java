package cn.fyg.schedule.controller.general;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.statement.daily.report.FDailySellingReportDto;
import cn.fyg.schedule.pojo.statement.daily.report.FDailySellingReport;
import cn.fyg.schedule.service.statement.daily.report.FDailySellingReportService;
import cn.hutool.poi.ofd.OfdWriter;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("daily")
public class FDailySellingReportControl {
    private final FDailySellingReportService s;
    public FDailySellingReportControl(FDailySellingReportService s) {
        this.s = s;
    }
    @PostMapping("projects")
    public BaseResponse listProjects() {
        return s.listProjects();
    }

    @PostMapping("report")
    public BaseResponse listDailySellingReport() {
        return s.listDaiySellingReportGroupByProject();
    }

    @PostMapping("report/current/year")
    public BaseResponse getCurrentYearReport(String creator, String fname_l2) {
        return s.annualSellingReport(creator, fname_l2);
    }

    @PostMapping("listnewest")
    public BaseResponse listNewest() { return s.listNewestData(); }

    @PostMapping("save")
    public BaseResponse save(FDailySellingReportDto dto) {
        FDailySellingReport data = FDailySellingReport.initialize(dto);
        return s.save(data);
    }

    @PostMapping("delete")
    public BaseResponse delete(Integer id) {
        return s.delete(id);
    }

    @PostMapping("find")
    public BaseResponse findById(Integer id) {
        return s.findById(id);
    }

    @PostMapping("findlast")
    public BaseResponse findLast(String creator, String fname_l2) {
        return s.findTopByCreatorAndFName(creator, fname_l2);
    }
}
