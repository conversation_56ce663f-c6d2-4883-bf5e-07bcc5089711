package cn.fyg.schedule.service.sheets.xincai.progressCheck;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.project.xincai.progressCheck.XcProgressCheckRecordItem;

public interface XcProgressCheckRecordItemService {
    BaseResponse save(XcProgressCheckRecordItem data);
    BaseResponse delete(Integer id);
    BaseResponse findById(Integer id);
    BaseResponse findByParentId(Integer id);
}
