package cn.fyg.schedule.pojo.construction;

import cn.fyg.schedule.pojo.dto.construction.FyConstructionLicenseDto;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.poi.hpsf.Decimal;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Setter
@Getter
@ToString
@Table(name = "fy_construction_license")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class FyConstructionLicense {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    private String serialNumber;
    @Column(name = "license_issuing_date")
    @Temporal(TemporalType.DATE)
    private Date licenseIssuingDate;
    private String licenseIssuingAuthority;
    private String developmentOrganization; //建设单位
    private Integer projectId;
    private String projectName;
    private String constructionAddress;
    private String constructionScale;
    @Column(name = "contract_begin")
    @Temporal(TemporalType.DATE)
    private Date contractBegin;
    @Column(name = "contract_end")
    @Temporal(TemporalType.DATE)
    private Date contractEnd;
    private String contractSchedule; //合同工期
    private BigDecimal contractAmountNum;
    private String contractAmount; //合同价格
    private String surveyOrganization; //勘察单位
    private String surveyOrgPrincipal; //勘察单位项目负责人
    private String designOrganization; //设计单位
    private String designOrgPrincipal; //设计单位项目负责人
    private String constructionOrganization; //施工单位
    private String constructionOrgPrincipal; //施工单位项目负责人
    private String supervisingOrganization; //监理单位
    private String supervisor; //总监理工程师
    private String constructionMainOrg; //工程总承包单位
    private String constructionMainManager; //工程总承包单位项目经理
    private Integer dataStatus;
    private Integer eventStatus;
    private String creator;
    private String creatorName;
    private String lastEditor;
    private String organization;
    private String remarks;
    private String managingDirector;
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "parent_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    @ToString.Exclude
    private List<FyConstructionLicenseItem> items = new ArrayList<>();

    public static FyConstructionLicense initialize(FyConstructionLicenseDto dto) {
        FyConstructionLicense data = new FyConstructionLicense();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        data.setCreator(dto.getCreator());
        data.setCreatorName(dto.getCreatorName());
        if (dto.getLicenseIssuingDate() != null) {
            data.setLicenseIssuingDate(DateUtil.date(dto.getLicenseIssuingDate()));
        }
        if (dto.getCreateDate() != null) {
            data.setCreateDate(DateUtil.date(data.createDate));
        }
        data.setDataStatus(dto.getDataStatus());
        data.setLastEditor(dto.getLastEditor());
        data.setEventStatus(dto.getEventStatus());
        data.setOrganization(dto.getOrganization());
        data.setSerialNumber(dto.getSerialNumber());
        data.setLicenseIssuingAuthority(dto.getLicenseIssuingAuthority());
        data.setDevelopmentOrganization(dto.getDevelopmentOrganization());
        data.setProjectId(dto.getProjectId());
        data.setProjectName(dto.getProjectName());
        data.setConstructionAddress(dto.getConstructionAddress());
        data.setConstructionScale(dto.getConstructionScale());
        if (dto.getContractBegin() != null) {
            data.setContractBegin(DateUtil.date(dto.getContractBegin()));
        }
        if (dto.getContractEnd() != null) {
            data.setContractEnd(DateUtil.date(dto.getContractEnd()));
        }
        data.setContractSchedule(dto.getContractSchedule());
        data.setContractAmount(dto.getContractAmount());
        data.setContractAmountNum(dto.getContractAmountNum());
        data.setSurveyOrganization(dto.getSurveyOrganization());
        data.setSurveyOrgPrincipal(dto.getSurveyOrgPrincipal());
        data.setDesignOrganization(dto.getDesignOrganization());
        data.setDesignOrgPrincipal(dto.getDesignOrgPrincipal());
        data.setConstructionOrganization(dto.getConstructionOrganization());
        data.setConstructionOrgPrincipal(dto.getConstructionOrgPrincipal());
        data.setSupervisingOrganization(dto.getSupervisingOrganization());
        data.setSupervisor(dto.getSupervisor());
        data.setConstructionMainOrg(dto.getConstructionMainOrg());
        data.setConstructionMainManager(dto.getConstructionMainManager());
        data.setRemarks(dto.getRemarks());
        data.setManagingDirector(dto.getManagingDirector());
        return data;
    }
}
