package cn.fyg.schedule.controller.fyfc.review;

import cn.fyg.schedule.pojo.dto.fyfc.review.*;
import cn.fyg.schedule.service.fyfc.review.IFyfcEvaluationManagerService;
import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * FYFC 评价主管控制器
 * 对应 manager/dashboard 页面的 API
 */
@Slf4j
@RestController
@RequestMapping("/api/fyfc/evaluation/manager")
@RequiredArgsConstructor
public class FyfcEvaluationManagerController {

    private final IFyfcEvaluationManagerService managerService;

    /**
     * 主管查询待评价的记录
     */
    @PostMapping("/pending")
    public FyfcApiResponseDto<FyfcPaginatedResponseDto<FyfcEvaluationDto>> getPendingEvaluations(
            @RequestParam String managerName,
            @RequestBody FyfcEvaluationQueryDto queryDto) {
        log.info("主管查询待评价记录: managerName={}", managerName);
        return managerService.getPendingEvaluations(managerName, queryDto);
    }

    /**
     * 主管查询已完成的评价
     */
    @PostMapping("/completed")
    public FyfcApiResponseDto<FyfcPaginatedResponseDto<FyfcEvaluationDto>> getCompletedEvaluations(
            @RequestParam String managerName,
            @RequestBody FyfcEvaluationQueryDto queryDto) {
        log.info("主管查询已完成评价: managerName={}", managerName);
        return managerService.getCompletedEvaluations(managerName, queryDto);
    }

    /**
     * 主管提交评分
     */
    @PostMapping("/score")
    public FyfcApiResponseDto<Boolean> submitManagerScore(
            @RequestBody FyfcScoreFormDto scoreForm,
            @RequestParam String managerName) {
        log.info("主管提交评分: evaluationId={}, manager={}", scoreForm.getEvaluationId(), managerName);
        return managerService.submitManagerScore(scoreForm, managerName);
    }

    /**
     * 主管批量审核评价
     */
    @PostMapping("/batch-approve")
    public FyfcApiResponseDto<Integer> batchApproveEvaluations(
            @RequestBody List<Integer> evaluationIds,
            @RequestParam String managerName) {
        log.info("主管批量审核评价: ids={}, manager={}", evaluationIds, managerName);
        return managerService.batchApproveEvaluations(evaluationIds, managerName);
    }

    /**
     * 获取主管评价统计
     */
    @GetMapping("/stats")
    public FyfcApiResponseDto<FyfcEvaluationStatsDto> getEvaluationStats(
            @RequestParam String managerName) {
        log.info("获取主管评价统计: managerName={}", managerName);
        return managerService.getEvaluationStats(managerName);
    }

    /**
     * 获取主管负责的员工列表
     */
    @GetMapping("/employees")
    public FyfcApiResponseDto<List<String>> getManagedEmployees(
            @RequestParam String managerName) {
        log.info("获取主管负责员工列表: managerName={}", managerName);
        return managerService.getManagedEmployees(managerName);
    }

    /**
     * 主管查看评价详情
     */
    @GetMapping("/{id}")
    public FyfcApiResponseDto<FyfcEvaluationDetailDto> getEvaluationDetail(
            @PathVariable Integer id,
            @RequestParam String managerName) {
        log.info("主管查看评价详情: id={}, manager={}", id, managerName);
        return managerService.getEvaluationDetail(id, managerName);
    }

    /**
     * 主管驳回评价
     */
    @PostMapping("/{id}/reject")
    public FyfcApiResponseDto<Boolean> rejectEvaluation(
            @PathVariable Integer id,
            @RequestParam String managerName,
            @RequestParam(required = false) String reason) {
        log.info("主管驳回评价: id={}, manager={}, reason={}", id, managerName, reason);
        return managerService.rejectEvaluation(id, managerName, reason);
    }

    /**
     * 获取主管待办事项统计
     */
    @GetMapping("/tasks/stats")
    public FyfcApiResponseDto<Object> getPendingTaskStats(@RequestParam String managerName) {
        log.info("获取主管待办事项统计: managerName={}", managerName);
        return managerService.getPendingTaskStats(managerName);
    }

    /**
     * 主管导出下属评价数据
     */
    @PostMapping("/export")
    public FyfcApiResponseDto<List<FyfcEvaluationDto>> exportSubordinateEvaluations(
            @RequestParam String managerName,
            @RequestBody FyfcEvaluationQueryDto queryDto) {
        log.info("主管导出下属评价数据: manager={}", managerName);
        return managerService.exportSubordinateEvaluations(managerName, queryDto);
    }

    // ==================== 辅助 API ====================

    /**
     * 主管快速审核（一键通过）
     */
    @PostMapping("/{id}/quick-approve")
    public FyfcApiResponseDto<Boolean> quickApprove(
            @PathVariable Integer id,
            @RequestParam String managerName) {
        log.info("主管快速审核: id={}, manager={}", id, managerName);

        FyfcApiResponseDto<Integer> result = managerService.batchApproveEvaluations(CollUtil.newArrayList(id), managerName);

        if (result.getSuccess() != null && result.getSuccess()) {
            boolean approved = result.getData() != null && result.getData() > 0;
            return FyfcApiResponseDto.success(approved, "快速审核" + (approved ? "成功" : "失败"));
        } else {
            return FyfcApiResponseDto.error(result.getCode(), result.getMessage());
        }
    }

    /**
     * 获取评价进度概览
     */
    @GetMapping("/progress/overview")
    public FyfcApiResponseDto<Object> getProgressOverview(@RequestParam String managerName) {
        log.info("获取评价进度概览: managerName={}", managerName);
        // TODO: 实现进度概览功能
        return FyfcApiResponseDto.success(null, "进度概览功能开发中");
    }

    /**
     * 设置评价提醒
     */
    @PostMapping("/reminder")
    public FyfcApiResponseDto<Boolean> setEvaluationReminder(
            @RequestParam String managerName,
            @RequestParam Integer evaluationId,
            @RequestParam Long reminderTime) {
        log.info("设置评价提醒: manager={}, evaluationId={}, time={}", managerName, evaluationId, reminderTime);
        // TODO: 实现提醒功能
        return FyfcApiResponseDto.success(true, "提醒设置成功");
    }

    /**
     * 获取团队评价报告
     */
    @GetMapping("/team/report")
    public FyfcApiResponseDto<Object> getTeamReport(@RequestParam String managerName) {
        log.info("获取团队评价报告: managerName={}", managerName);
        // TODO: 实现团队报告功能
        return FyfcApiResponseDto.success(null, "团队报告功能开发中");
    }

    /**
     * 主管评价建议
     */
    @PostMapping("/{id}/suggestion")
    public FyfcApiResponseDto<Boolean> addEvaluationSuggestion(
            @PathVariable Integer id,
            @RequestParam String managerName,
            @RequestParam String suggestion) {
        log.info("主管添加评价建议: id={}, manager={}", id, managerName);
        // TODO: 实现评价建议功能
        return FyfcApiResponseDto.success(true, "建议添加成功");
    }

    /**
     * 委托评价审核
     */
    @PostMapping("/{id}/delegate")
    public FyfcApiResponseDto<Boolean> delegateEvaluation(
            @PathVariable Integer id,
            @RequestParam String managerName,
            @RequestParam String delegateTo) {
        log.info("委托评价审核: id={}, from={}, to={}", id, managerName, delegateTo);
        // TODO: 实现委托功能
        return FyfcApiResponseDto.success(true, "委托成功");
    }
}
