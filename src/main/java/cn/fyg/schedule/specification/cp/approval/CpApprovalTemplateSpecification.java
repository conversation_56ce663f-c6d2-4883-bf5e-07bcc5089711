package cn.fyg.schedule.specification.cp.approval;

import cn.fyg.schedule.pojo.cp.approval.CpApprovalTemplate;
import cn.fyg.schedule.utils.SpecificationUtil;
import org.springframework.data.jpa.domain.Specification;

public class CpApprovalTemplateSpecification {
    public static Specification<CpApprovalTemplate> ifContain(String column, String expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.like(root.get(column), SpecificationUtil.contains(expression)));
    }

    public static Specification<CpApprovalTemplate> ifEq(String column, Integer expression) {
        return ((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(column), expression));
    }
}
