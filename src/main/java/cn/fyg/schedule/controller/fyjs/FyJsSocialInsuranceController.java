package cn.fyg.schedule.controller.fyjs;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.fyjs.social.insurance.FyjsSocialInsuranceDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.pojo.fyjs.social.insurance.FyjsSocialInsuranceItem;
import cn.fyg.schedule.service.fyjs.social.insurance.FyjsSocialInsuranceItemService;
import cn.fyg.schedule.service.fyjs.social.insurance.FyjsSocialInsuranceService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("js/social/insurance")
public class FyJsSocialInsuranceController {
    private final FyjsSocialInsuranceService service;
    private final FyjsSocialInsuranceItemService itemService;

    public FyJsSocialInsuranceController(FyjsSocialInsuranceService service, FyjsSocialInsuranceItemService itemService) {
        this.service = service;
        this.itemService = itemService;
    }

    @PostMapping("save")
    public BaseResponse save(FyjsSocialInsuranceDto dto) {
        return service.save(dto);
    }

    @PostMapping("find/id")
    public BaseResponse findById(Integer id) {
        return service.findById(id);
    }

    @PostMapping("find/top")
    public BaseResponse getTop() {
        return service.findFirstTop();
    }

    @PostMapping("filter")
    public BaseResponse filter(Pagination pagination, FyjsSocialInsuranceDto dto) {
        return service.filter(pagination, dto);
    }

    @PostMapping("save/item")
    public BaseResponse saveItem(FyjsSocialInsuranceItem data) {
        return itemService.save(data);
    }

    @PostMapping("delete/item/parent")
    public BaseResponse deleteItemByParentId(Integer id) {
        return itemService.deleteByParentId(id);
    }

    @PostMapping("delete/item")
    public BaseResponse deleteItem(Integer id) {
        return itemService.delete(id);
    }

    @PostMapping("find/item/parent")
    public BaseResponse findItemByParentId(Integer id) {
        return itemService.findByParentId(id);
    }
}
