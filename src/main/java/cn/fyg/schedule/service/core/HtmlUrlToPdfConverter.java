package cn.fyg.schedule.service.core;

import cn.fyg.schedule.utils.PDFUtil;
import cn.fyg.schedule.utils.RestTemplateUtil;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletResponse;
import java.io.*;

@Component
@Slf4j
public class HtmlUrlToPdfConverter {
    private final HtmlToPdfService htmlToPdfService;

    public HtmlUrlToPdfConverter(HtmlToPdfService htmlToPdfService) {
        this.htmlToPdfService = htmlToPdfService;
    }

    public void htmlToPdf(HttpServletResponse response, String url) {
        try {
            cPDF(response, url);
        } catch (IOException e) {
            log.error("printRecord {}",e.getMessage());
        }
    }

    private String doGet(String url) {
        RestTemplate restTemplate = new RestTemplate();
        RestTemplateUtil.setRestTemplateEncode(restTemplate);
        return restTemplate.getForObject(url, String.class, "");
    }

    public ResponseEntity<ByteArrayResource> downloadPdf(String url) {
        String html = doGet(url);
        byte[] pdfBytes = htmlToPdfService.convertHtmlToPdf(html);
        ByteArrayResource resource = new ByteArrayResource(pdfBytes);

        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=notification.pdf");

        return ResponseEntity.ok()
                .headers(headers)
                .contentLength(resource.contentLength())
                .contentType(MediaType.APPLICATION_PDF)
                .body(resource);
    }

    private void cPDF(HttpServletResponse res, String url) throws FileNotFoundException, IOException {
        String html = doGet(url);
        PDFUtil pdfUtil = new PDFUtil();
        ByteArrayOutputStream stream = pdfUtil.html2Pdf(html);
        res.setHeader("Expires", "0");
        res.setHeader("Cache-Control", "must-revalidate, post-check=0, pre-check=0");
        res.setHeader("Pragma", "public");
        res.setContentType("application/pdf");
        OutputStream os = res.getOutputStream();
        stream.writeTo(os);
        os.flush();
        os.close();
    }
}
