package cn.fyg.schedule.service.statement.capital.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.statement.capital.FCapitalStatementRepository;
import cn.fyg.schedule.pojo.statement.capital.FCapitalStatement;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.statement.capital.FCapitalStatementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.Date;
import java.util.List;
import java.util.Optional;
@Service
@Slf4j
public class FCapitalStatementServiceImpl implements FCapitalStatementService {
    private final FCapitalStatementRepository r;

    @Autowired
    public FCapitalStatementServiceImpl(FCapitalStatementRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse save(FCapitalStatement data) {
        BaseResponse response = BaseResponse.initialize();
        try {
            // 在事务内保存并立即预加载
            FCapitalStatement savedStatement = r.save(data);
            preloadLazyCollections(savedStatement);
            response.setResult(savedStatement);
        } catch (Exception e) {
            log.error(e.getMessage());
            response.setCode(1);
            response.setMsg("保存失败");
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse findById(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Optional<FCapitalStatement> result = r.findById(id);
            if (result.isPresent()) {
                FCapitalStatement data = result.get();
                // 立即在事务内预加载懒加载的集合
                preloadLazyCollections(data);
                baseResponse.setResult(data);
            } else {
                baseResponse.setCode(1);
                baseResponse.setMsg("数据不存在");
            }
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.error(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse findByStatementDate(Date date) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FCapitalStatement> result = r.findByStatementDateOrderByCreateDateDesc(date);
            baseResponse.setResult(result);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public List<FCapitalStatement> findLastStatement(Date date) {
        try {
            List<FCapitalStatement> result = r.findByStatementDateOrderByCreateDateDesc(date);
            return result;
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public List<FCapitalStatement> findAll() {
        try {
            List<FCapitalStatement> result = (List<FCapitalStatement>) r.findAll();
            // 立即在事务内预加载懒加载的集合
            for (FCapitalStatement statement : result) {
                preloadLazyCollections(statement);
            }
            return result;
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    @Override
    public FCapitalStatement findFirstStatement() {
        try {
            Optional<FCapitalStatement> optional = r.findTopByOrderByCreateDate();
            if (optional.isPresent()) {
                return optional.get();
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse listStatement(Integer[] ids) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FCapitalStatement> result = r.findByIdInOrderByStatementDateDesc(ids);
            // 立即在事务内预加载懒加载的集合
            for (FCapitalStatement statement : result) {
                preloadLazyCollections(statement);
            }
            baseResponse.setResult(result);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.error(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public List<FCapitalStatement> findStatementInIds(Integer[] ids) {
        try {
            List<FCapitalStatement> list = r.findByIdInOrderByStatementDateDesc(ids);
            // 立即在事务内预加载懒加载的集合
            for (FCapitalStatement statement : list) {
                preloadLazyCollections(statement);
            }
            return list;
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    /**
     * 预加载懒加载的集合，避免LazyInitializationException
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    private void preloadLazyCollections(FCapitalStatement statement) {
        if (statement != null) {
            try {
                // 触发懒加载 - 我们只需要触发加载，不需要使用返回值
                if (statement.getIncomes() != null) {
                    statement.getIncomes().size(); // 触发懒加载
                }
                if (statement.getExpenditures() != null) {
                    statement.getExpenditures().size(); // 触发懒加载
                }
                if (statement.getBalances() != null) {
                    statement.getBalances().size(); // 触发懒加载
                }
            } catch (Exception e) {
                log.warn("预加载懒加载集合时出错: {}", e.getMessage());
                // 不抛出异常，允许继续执行
            }
        }
    }
}
