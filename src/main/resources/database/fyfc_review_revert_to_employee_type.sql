-- =====================================================
-- FYFC Review 系统数据库迁移脚本
-- 将 SELF 类型改回 EMPLOYEE 类型
-- =====================================================

-- 1. 显示当前数据分布
SELECT 
    'Migration Before' as phase,
    `type`,
    COUNT(*) as count,
    CONCAT(ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM `fyfc_evaluation_scores`), 2), '%') as percentage
FROM `fyfc_evaluation_scores` 
GROUP BY `type`
ORDER BY count DESC;

-- 2. 检查是否有 'self' 类型的数据需要迁移
SELECT 
    'self' as type_name,
    COUNT(*) as records_to_migrate,
    CASE 
        WHEN COUNT(*) > 0 THEN '需要迁移'
        ELSE '无需迁移'
    END as migration_status
FROM `fyfc_evaluation_scores` 
WHERE `type` = 'self';

-- 3. 修改表结构，添加 'employee' 选项（如果还没有的话）
ALTER TABLE `fyfc_evaluation_scores` 
MODIFY COLUMN `type` enum('unknown','self','employee','colleague','manager','admin') NOT NULL COMMENT '评价人角色类型';

-- 4. 将 'self' 数据迁移为 'employee'
UPDATE `fyfc_evaluation_scores` 
SET `type` = 'employee' 
WHERE `type` = 'self';

-- 5. 显示迁移后的数据分布
SELECT 
    'Migration After' as phase,
    `type`,
    COUNT(*) as count,
    CONCAT(ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM `fyfc_evaluation_scores`), 2), '%') as percentage
FROM `fyfc_evaluation_scores` 
GROUP BY `type`
ORDER BY count DESC;

-- 6. 验证迁移结果 - 检查是否还有 'self' 类型的记录（应该为 0）
SELECT 
    'Validation' as phase,
    COUNT(*) as remaining_self_records,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ 迁移成功，无遗留 self 记录'
        ELSE '❌ 迁移失败，仍有 self 记录'
    END as validation_result
FROM `fyfc_evaluation_scores` 
WHERE `type` = 'self';

-- 7. 修改表结构，移除 'self' 选项
ALTER TABLE `fyfc_evaluation_scores` 
MODIFY COLUMN `type` enum('unknown','employee','colleague','manager','admin') NOT NULL COMMENT '评价人角色类型';

-- 8. 验证最终表结构
SHOW COLUMNS FROM `fyfc_evaluation_scores` LIKE 'type';

-- 9. 最终数据验证
SELECT 
    'Final Validation' as phase,
    `type`,
    COUNT(*) as count,
    'Valid' as status
FROM `fyfc_evaluation_scores` 
GROUP BY `type`
UNION ALL
SELECT 
    'TOTAL' as phase,
    'ALL_TYPES' as type,
    COUNT(*) as count,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ 所有数据有效'
        ELSE '⚠️ 无数据'
    END as status
FROM `fyfc_evaluation_scores`;

-- 10. 显示迁移完成信息
SELECT 
    'Revert to EMPLOYEE completed successfully!' as status,
    NOW() as migration_time,
    'self -> employee migration completed' as action,
    (SELECT COUNT(*) FROM `fyfc_evaluation_scores` WHERE `type` = 'employee') as employee_records,
    (SELECT COUNT(*) FROM `fyfc_evaluation_scores` WHERE `type` = 'colleague') as colleague_records,
    (SELECT COUNT(*) FROM `fyfc_evaluation_scores` WHERE `type` = 'manager') as manager_records,
    (SELECT COUNT(*) FROM `fyfc_evaluation_scores` WHERE `type` = 'admin') as admin_records;
