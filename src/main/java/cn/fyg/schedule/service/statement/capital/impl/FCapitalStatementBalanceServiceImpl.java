package cn.fyg.schedule.service.statement.capital.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.statement.capital.FCapitalStatementBalanceRepository;
import cn.fyg.schedule.pojo.dto.statement.capital.FCapitalStatementAccount;
import cn.fyg.schedule.pojo.statement.capital.FCapitalStatementBalance;
import cn.fyg.schedule.pojo.statement.capital.FCapitalStatementExpenditure;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.statement.capital.FCapitalStatementBalanceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class FCapitalStatementBalanceServiceImpl implements FCapitalStatementBalanceService {
    private final FCapitalStatementBalanceRepository r;
    public FCapitalStatementBalanceServiceImpl(FCapitalStatementBalanceRepository r) {
        this.r = r;
    }
    @Override
    public BaseResponse save(FCapitalStatementBalance data) {
        return BaseService.save(data, r, FCapitalStatementBalance.class);
    }

    @Override
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse list(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FCapitalStatementBalance> result = r.findByCapitalStatementIdOrderByNumberDesc(id);
            baseResponse.setResult(result);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse findById(Integer id) {
        return null;
    }

    @Override
    public Double sumCurrentDayIncome(Integer id) {
        Double result = 0.00;
        try {
            List<FCapitalStatementBalance> list = r.findByCapitalStatementIdOrderByNumberDesc(id);
            for (FCapitalStatementBalance balance : list) {
                result += balance.getCurrentDayAmount();
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return result;
    }

    @Override
    public Double cumCurrentDayIncome(Integer id) {
        Double result = 0.00;
        try {
            List<FCapitalStatementBalance> list = r.findByCapitalStatementIdOrderByNumberDesc(id);
            for (FCapitalStatementBalance balance : list) {
                result += balance.getCurrentDayAmount() + balance.getCumulativeAmount();
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return result;
    }

    @Override
    public BaseResponse getHistoryAmount(Integer id, String account, String subject) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FCapitalStatementBalance> result = r.findByCapitalStatementIdAndAccountAndCurrencySubject(id, account, subject);
            Double sum = 0.00;
            if (result.size() > 0) {
                for (FCapitalStatementBalance balance : result) {
                    sum += balance.getCumulativeAmount();
                }
            }
            baseResponse.setResult(sum);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse listAccount(Integer[] ids) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FCapitalStatementAccount> list = r.listAccount(ids);
            baseResponse.setResult(list);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }
}
