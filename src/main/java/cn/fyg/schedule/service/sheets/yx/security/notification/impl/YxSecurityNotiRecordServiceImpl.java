package cn.fyg.schedule.service.sheets.yx.security.notification.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.yuanxin.security.notification.YxSecurityNotiRecordRepository;
import cn.fyg.schedule.pojo.dto.project.yx.security.notification.YxSecurityNotiRecordDto;
import cn.fyg.schedule.pojo.project.yuanXin.security.notification.YxSecurityNotiRecord;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.sheets.yx.security.notification.YxSecurityNotiRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class YxSecurityNotiRecordServiceImpl implements YxSecurityNotiRecordService {
    private final YxSecurityNotiRecordRepository r;

    public YxSecurityNotiRecordServiceImpl(YxSecurityNotiRecordRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse save(YxSecurityNotiRecordDto dto) {
        BaseResponse response = BaseResponse.initialize();
        try {
            YxSecurityNotiRecord data = YxSecurityNotiRecord.initialize(dto);
            // 在事务内保存并立即预加载
            YxSecurityNotiRecord savedData = r.save(data);
            preloadLazyCollections(savedData);
            response.setResult(savedData);
        } catch (Exception e) {
            log.error(e.getMessage());
            response.setCode(1);
            response.setMsg("保存失败");
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        YxSecurityNotiRecord data = getDataById(id);
        if (data != null) {
            response.setResult(data);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    private YxSecurityNotiRecord getDataById(Integer id) {
        try {
            Optional<YxSecurityNotiRecord> optional = r.findById(id);
            if (optional.isPresent()) {
                return optional.get();
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse findByParentId(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        try {
            // 在事务内完成查询和预加载
            List<YxSecurityNotiRecord> data = r.findByParentIdOrderByCreateDateDesc(id);
            if (!data.isEmpty()) {
                // 立即在事务内预加载懒加载的集合
                for (YxSecurityNotiRecord record : data) {
                    preloadLazyCollections(record);
                }
                response.setResult(data);
            } else {
                response.setCode(1);
                response.setMsg("未查到数据");
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public List<YxSecurityNotiRecord> listRecordByParentId(Integer id, Integer mark) {
        try {
            // 在事务内完成查询和预加载
            List<YxSecurityNotiRecord> data = mark == 0 ?
                r.findByParentIdOrderByCreateDateAsc(id) :
                r.findByParentIdOrderByCreateDateDesc(id);

            // 立即在事务内预加载懒加载的集合
            for (YxSecurityNotiRecord record : data) {
                preloadLazyCollections(record);
            }
            return data;
        } catch (Exception e) {
            log.error(e.getMessage());
            return new ArrayList<>();
        }
    }

    @Override
    public BaseResponse ifApproved(Integer id, Integer recordStatus) {
        BaseResponse response = BaseResponse.initialize();
        List<YxSecurityNotiRecord> list = findByParentIdAndStatus(id, recordStatus);
        if (list != null) {
            response.setResult(list);
            response.setMsg("is approved");
        } else {
            response.setCode(1);
            response.setMsg("not been approved yet");
        }
        return response;
    }

    @Override
    public List<Integer> listParentIdByCreatorPhone(String creatorPhone) {
        try {
            List<Integer> list = r.listParentIdByCreatorPhone(creatorPhone);
            if (!list.isEmpty()) {
                return list;
            }
        } catch (Exception e) {
            log.error(e.getLocalizedMessage());
        }
        return null;
    }

    private List<YxSecurityNotiRecord> findByParentIdAndStatus(Integer id, Integer status) {
        try {
            List<YxSecurityNotiRecord> list = r.findByParentIdAndRecordStatus(id, status);
            if (!list.isEmpty()) {
                return list;
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    /**
     * 预加载懒加载的集合，避免LazyInitializationException
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    private void preloadLazyCollections(YxSecurityNotiRecord record) {
        if (record != null) {
            try {
                // 触发懒加载 - 我们只需要触发加载，不需要使用返回值
                if (record.getItems() != null) {
                    record.getItems().size(); // 触发懒加载
                }
                if (record.getSignatures() != null) {
                    record.getSignatures().size(); // 触发懒加载
                }
            } catch (Exception e) {
                log.warn("预加载懒加载集合时出错: {}", e.getMessage());
                // 不抛出异常，允许继续执行
            }
        }
    }
}
