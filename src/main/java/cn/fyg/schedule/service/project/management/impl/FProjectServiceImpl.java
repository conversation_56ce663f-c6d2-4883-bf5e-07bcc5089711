package cn.fyg.schedule.service.project.management.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.projectManagement.FProjectRepository;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.dto.project.management.FProjectDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.pojo.project.management.FProject;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.project.management.FProjectService;
import cn.fyg.schedule.specification.project.FProjectSpecification;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class FProjectServiceImpl implements FProjectService {
    private final FProjectRepository r;

    public FProjectServiceImpl(FProjectRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FProjectDto dto) {
        FProject data = FProject.initialize(dto);
        BaseResponse response = BaseService.save(data, r, FProject.class);
        // 如果保存成功，预加载懒加载的集合，避免序列化时的LazyInitializationException
        if (response.getCode() == 0 && response.getResult() instanceof FProject) {
            FProject savedProject = (FProject) response.getResult();
            preloadLazyCollections(savedProject);
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse findById(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        FProject data = getById(id);
        if (data != null) {
            // 预加载懒加载的集合，避免LazyInitializationException
            preloadLazyCollections(data);
            baseResponse.setResult(data);
        } else {
            baseResponse.setMsg("查询出错");
            baseResponse.setCode(1);
        }
        return baseResponse;
    }

    private FProject getById(Integer id) {
        try {
            Optional<FProject> optional = r.findById(id);
            if (optional.isPresent()) {
                return optional.get();
            }
        } catch (Exception e) {
            log.error(e.getLocalizedMessage());
        }
        return null;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse filter(FProjectDto dto, Pagination pagination) {
        BaseResponse response = BaseResponse.initialize();
        try {
            Sort sort = Sort.unsorted();
            if (!StrUtil.isEmpty(pagination.getColumnKey())) {
                if (!pagination.getOrder().equals("false")) {
                    sort = Sort.by(pagination.getOrder().equals("ascend") ? Sort.Direction.ASC : Sort.Direction.DESC, pagination.getColumnKey());
                }
            }
            Pageable pageable = PageRequest.of(pagination.getCurrentPage(), pagination.getPageSize(), sort);
            Specification<FProject> specification = getSpecification(dto);
            Page<FProject> data = r.findAll(specification, pageable);
            // 预加载懒加载的集合，避免LazyInitializationException
            data.getContent().forEach(this::preloadLazyCollections);
            response.setResult(data);
        } catch (Exception e) {
            log.info(e.getMessage());
            response.setCode(1);
            response.setMsg("failed");
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse filter(FProjectDto dto) {
        BaseResponse response = BaseResponse.initialize();
        try {
            Specification<FProject> specification = getSpecification(dto);
            List<FProject> data = r.findAll(specification);
            // 预加载懒加载的集合，避免LazyInitializationException
            data.forEach(this::preloadLazyCollections);
            response.setResult(data);
        } catch (Exception e) {
            log.info(e.getMessage());
            response.setCode(1);
            response.setMsg("failed");
        }
        return response;
    }

    @Override
    public BaseResponse delete(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        try {
            deleteById(id);
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getMessage());
        }
        return response;
    }

    private void deleteById(Integer id) throws MyException {
        try {
            r.deleteById(id);
        } catch (JpaSystemException e) {
            log.error(e.getLocalizedMessage());
            throw new MyException(442, "删除失败");
        }
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse findOneByName(String name) {
        BaseResponse baseResponse = BaseResponse.initialize();
        FProject data = findByName(name);
        if (data != null) {
            // 预加载懒加载的集合，避免LazyInitializationException
            preloadLazyCollections(data);
            baseResponse.setResult(data);
        } else {
            baseResponse.setCode(1);
            baseResponse.setMsg("查询失败");
        }
        return baseResponse;
    }

    private FProject findByName(String name) {
        try {
            Optional<FProject> optional = r.findFirstByName(name);
            if (optional.isPresent()) {
                return optional.get();
            }
        } catch (Exception e) {
            log.error(e.getLocalizedMessage());
        }
        return null;
    }

    /**
     * 预加载懒加载的集合，避免LazyInitializationException
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    private void preloadLazyCollections(FProject project) {
        if (project != null) {
            // 触发第一层懒加载
            if (project.getStaffs() != null) {
                project.getStaffs().size();
            }
            if (project.getProgresses() != null) {
                project.getProgresses().size();
                // 触发第二层懒加载：FProjectProgress.items
                project.getProgresses().forEach(progress -> {
                    if (progress.getItems() != null) {
                        progress.getItems().size();
                    }
                });
            }
            if (project.getObjectives() != null) {
                project.getObjectives().size();
            }
            if (project.getItems() != null) {
                project.getItems().size();
            }
        }
    }

    private Specification<FProject> getSpecification(FProjectDto dto) {

        return Specification.where(StrUtil.isEmpty(dto.getName()) ? null : FProjectSpecification.ifContain("name", dto.getName()))
                .and(dto.getDataStatus() == null ? null : FProjectSpecification.ifEq("dataStatus", dto.getDataStatus()))
                .and(StrUtil.isEmpty(dto.getEasKey()) ? null : FProjectSpecification.ifEq("easKey", dto.getEasKey()));
    }
}
