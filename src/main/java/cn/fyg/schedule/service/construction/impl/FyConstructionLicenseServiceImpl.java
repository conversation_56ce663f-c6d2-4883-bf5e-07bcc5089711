package cn.fyg.schedule.service.construction.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.construction.FyConstructionLicenseRepository;
import cn.fyg.schedule.pojo.construction.FyConstructionLicense;
import cn.fyg.schedule.pojo.core.ScopeQueryDate;
import cn.fyg.schedule.pojo.core.ScopeQueryNumber;
import cn.fyg.schedule.pojo.dto.construction.FyConstructionLicenseDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.construction.FyConstructionLicenseService;
import cn.fyg.schedule.specification.construction.FyConstructionLicenseSpecification;
import cn.fyg.schedule.utils.GsonUtil;
import cn.hutool.core.date.DateException;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class FyConstructionLicenseServiceImpl implements FyConstructionLicenseService {
    final private FyConstructionLicenseRepository r;

    private static final String[] possibleDateFormats = {
            "yyyy年",
            "yyyy-MM",
            "yyyy/MM",
            "yyyy.MM",
            "yyyy年MM月"
            // 添加更多可能的格式
    };

    public FyConstructionLicenseServiceImpl(FyConstructionLicenseRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FyConstructionLicenseDto dto) {
        FyConstructionLicense data = FyConstructionLicense.initialize(dto);
        BaseResponse response = BaseService.save(data, r, FyConstructionLicense.class);
        // 如果保存成功，预加载懒加载的集合，避免序列化时的LazyInitializationException
        if (response.getCode() == 0 && response.getResult() instanceof FyConstructionLicense) {
            FyConstructionLicense savedLicense = (FyConstructionLicense) response.getResult();
            preloadLazyCollections(savedLicense);
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse findById(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        FyConstructionLicense data = getDataById(id);
        if (data != null) {
            // 预加载懒加载的items集合，避免LazyInitializationException
            preloadLazyCollections(data);
            response.setResult(data);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    private FyConstructionLicense getDataById(Integer id) {
        try {
            Optional<FyConstructionLicense> optional = r.findById(id);
            if (optional.isPresent()) {
                return optional.get();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public BaseResponse filter(FyConstructionLicenseDto dto, Pagination pagination) {
        BaseResponse response = BaseResponse.initialize();
        try {
            Sort sort = Sort.unsorted();
            if (!StrUtil.isEmpty(pagination.getColumnKey())) {
                if (!pagination.getOrder().equals("false")) {
                    sort = Sort.by(pagination.getOrder().equals("ascend") ? Sort.Direction.ASC : Sort.Direction.DESC, pagination.getColumnKey());
                }
            }
            Pageable pageable = PageRequest.of(pagination.getCurrentPage(), pagination.getPageSize(), sort);
            Specification<FyConstructionLicense> specification = getSpecification(dto);
            Page<FyConstructionLicense> data = r.findAll(specification, pageable);
            // 预加载懒加载的items集合，避免LazyInitializationException
            data.getContent().forEach(this::preloadLazyCollections);
            response.setResult(data);
        } catch (Exception e) {
            log.info(e.getMessage());
            response.setCode(1);
            response.setMsg("查询出错");
        }
        return response;
    }

    private Specification<FyConstructionLicense> getSpecification(FyConstructionLicenseDto dto) {
        return Specification.where(dto.getEventStatus() == null ? null : FyConstructionLicenseSpecification.ifEq("eventStatus", dto.getEventStatus()))
                .and(dto.getDataStatus() == null ? null : FyConstructionLicenseSpecification.ifEq("dataStatus", dto.getDataStatus()))
                .and(StrUtil.isEmpty(dto.getCreatorName()) ? null : FyConstructionLicenseSpecification.ifContain("creatorName", dto.getCreatorName()))
                .and(StrUtil.isEmpty(dto.getSerialNumber()) ? null : FyConstructionLicenseSpecification.ifContain("serialNumber", dto.getSerialNumber()))
                .and(dto.getStartDate() == null || dto.getEndDate() == null ? null : FyConstructionLicenseSpecification.ifDateBetween("licenseIssuingDate", DateUtil.date(dto.getStartDate()), DateUtil.date(dto.getEndDate())))
                .and(StrUtil.isEmpty(dto.getLicenseIssuingAuthority()) ? null : FyConstructionLicenseSpecification.ifContain("licenseIssuingAuthority", dto.getLicenseIssuingAuthority()))
                .and(StrUtil.isEmpty(dto.getDevelopmentOrganization()) ? null : FyConstructionLicenseSpecification.ifContain("developmentOrganization", dto.getDevelopmentOrganization()))
                .and(StrUtil.isEmpty(dto.getProjectName()) ? null : FyConstructionLicenseSpecification.ifContain("projectName", dto.getProjectName()))
                .and(StrUtil.isEmpty(dto.getConstructionAddress()) ? null : FyConstructionLicenseSpecification.ifContain("constructionAddress", dto.getConstructionAddress()))
                .and(StrUtil.isEmpty(dto.getConstructionScale()) ? null : FyConstructionLicenseSpecification.ifContain("constructionScale", dto.getConstructionScale()))
                .and(dto.getContractBegin() == null ? null : FyConstructionLicenseSpecification.ifDateEq("contractBegin", DateUtil.date(dto.getContractBegin())))
                .and(dto.getContractEnd() == null ? null : FyConstructionLicenseSpecification.ifDateEq("contractEnd", DateUtil.date(dto.getContractEnd())))
                .and(StrUtil.isEmpty(dto.getContractSchedule()) ? null : FyConstructionLicenseSpecification.ifContain("contractSchedule", dto.getContractSchedule()))
                .and(dto.getAmountMin() == null && dto.getAmountMax() == null ?
                        null : dto.getAmountMin() != null && dto.getAmountMax() == null ?
                        FyConstructionLicenseSpecification.ifNumberOverThan("contractAmountNum", dto.getAmountMin()) :
                        dto.getAmountMin() == null ? FyConstructionLicenseSpecification.ifNumberLessThan("contractAmountNum", dto.getAmountMax()) : null)
                .and(dto.getAmountMin() == null || dto.getAmountMax() == null ? null : FyConstructionLicenseSpecification.ifNumberBetween("contractAmountNum", dto.getAmountMin(), dto.getAmountMax()))
                .and(StrUtil.isEmpty(dto.getConstructionOrganization()) ? null : FyConstructionLicenseSpecification.ifContain("constructionOrganization", dto.getConstructionOrganization()))
                .and(StrUtil.isEmpty(dto.getConstructionOrgPrincipal()) ? null : FyConstructionLicenseSpecification.ifContain("constructionOrgPrincipal", dto.getConstructionOrgPrincipal()))
                .and(StrUtil.isEmpty(dto.getSurveyOrganization()) ? null : FyConstructionLicenseSpecification.ifContain("surveyOrganization", dto.getSurveyOrganization()))
                .and(StrUtil.isEmpty(dto.getSurveyOrgPrincipal()) ? null : FyConstructionLicenseSpecification.ifContain("surveyOrgPrincipal", dto.getSurveyOrgPrincipal()))
                .and(StrUtil.isEmpty(dto.getDesignOrganization()) ? null : FyConstructionLicenseSpecification.ifContain("designOrganization", dto.getDesignOrganization()))
                .and(StrUtil.isEmpty(dto.getDesignOrgPrincipal()) ? null : FyConstructionLicenseSpecification.ifContain("designOrgPrincipal", dto.getDesignOrgPrincipal()))
                .and(StrUtil.isEmpty(dto.getSupervisingOrganization()) ? null : FyConstructionLicenseSpecification.ifContain("supervisingOrganization", dto.getSupervisingOrganization()))
                .and(StrUtil.isEmpty(dto.getSupervisor()) ? null : FyConstructionLicenseSpecification.ifContain("supervisor", dto.getSupervisor()))
                .and(StrUtil.isEmpty(dto.getConstructionMainOrg()) ? null : FyConstructionLicenseSpecification.ifContain("constructionMainOrg", dto.getConstructionMainOrg()))
                .and(StrUtil.isEmpty(dto.getConstructionMainManager()) ? null : FyConstructionLicenseSpecification.ifContain("constructionMainManager", dto.getConstructionMainManager()))
                .and(StrUtil.isEmpty(dto.getRemarks()) ? null : FyConstructionLicenseSpecification.ifContain("remarks", dto.getRemarks()))
                .and(StrUtil.isEmpty(dto.getManagingDirector()) ? null : FyConstructionLicenseSpecification.ifContain("managingDirector", dto.getManagingDirector()))
                .and(StrUtil.isEmpty(dto.getOrganization()) ? null : FyConstructionLicenseSpecification.ifEq("organization", dto.getOrganization()))
                .and(StrUtil.isEmpty(dto.getLicenseIssuingDateQuery()) ? null : getScopeQuery("licenseIssuingDate", dto.getLicenseIssuingDateQuery()))
                .and(StrUtil.isEmpty(dto.getContractAmountNumQuery()) ? null : getScopeQueryNumber("contractAmountNum", dto.getContractAmountNumQuery()));
    }

    private Specification<FyConstructionLicense> getScopeQuery(String column, String jsonStr, String dateFormat) {
        List<ScopeQueryDate> list = GsonUtil.fromJsonList(jsonStr, ScopeQueryDate.class);
        if (list.size() > 1) {
            Date dateStart = DateUtil.parse(list.get(0).getValue(), dateFormat);
            Date dateStop = DateUtil.parse(list.get(1).getValue(), dateFormat);
            return dateStart.compareTo(dateStop) > 0 ? FyConstructionLicenseSpecification.ifDateBetween(column, dateStop, dateStart) : FyConstructionLicenseSpecification.ifDateBetween(column, dateStart, dateStop);
        } else {
            return getScopeQuery(column, list.get(0), dateFormat);
        }
    }

    private Specification<FyConstructionLicense> getScopeQuery(String column, ScopeQueryDate query, String dateFormat) {
        Date date = DateUtil.parse(query.getValue(), dateFormat);
        switch (query.getSymbol()) {
            case "<":
                return FyConstructionLicenseSpecification.ifDateLessThan(column, date);
            case "<=":
                return FyConstructionLicenseSpecification.ifDateLessThanOrEqualTo(column, date);
            case ">":
                return FyConstructionLicenseSpecification.ifDateOverThan(column, date);
            case ">=":
                return FyConstructionLicenseSpecification.ifDateOverThanOrEqualTo(column, date);
            default:
                return FyConstructionLicenseSpecification.ifDateEq(column, date);
        }
    }

    private Specification<FyConstructionLicense> getScopeQueryNumber(String column, String jsonStr) {
        List<ScopeQueryNumber> list = GsonUtil.fromJsonList(jsonStr, ScopeQueryNumber.class);
        if (list.size() > 1) {
            if (list.get(0).getValue().compareTo(list.get(1).getValue()) > 0) {
                return FyConstructionLicenseSpecification.ifNumberBetween(column, list.get(1).getValue(), list.get(0).getValue());
            } else {
                return FyConstructionLicenseSpecification.ifNumberBetween(column, list.get(0).getValue(), list.get(1).getValue());
            }
        } else {
            return getScopeQuery(column, list.get(0));
        }
    }

    private Specification<FyConstructionLicense> getScopeQuery(String column, ScopeQueryNumber query) {
        switch (query.getSymbol()) {
            case "<":
                return FyConstructionLicenseSpecification.ifNumberLessThan(column, query.getValue());
            case "<=":
                return FyConstructionLicenseSpecification.ifNumberLessThanOrEqualTo(column, query.getValue());
            case ">":
                return FyConstructionLicenseSpecification.ifNumberOverThan(column, query.getValue());
            case ">=":
                return FyConstructionLicenseSpecification.ifNumberOverThanOrEqualTo(column, query.getValue());
            default:
                return FyConstructionLicenseSpecification.ifNumberEqualTo(column, query.getValue());
        }
    }

    /**
     *
     * @param column
     * @param jsonStr
     * @return
     */
    private Specification<FyConstructionLicense> getScopeQuery(String column, String jsonStr) {
        List<ScopeQueryDate> list = GsonUtil.fromJsonList(jsonStr, ScopeQueryDate.class);
        if (list.size() > 1) {
            Date dateStart = getDate(list.get(0).getValue());
            Date dateStop = getDate(list.get(1).getValue());
            return dateStart.compareTo(dateStop) > 0 ? FyConstructionLicenseSpecification.ifDateBetween(column, dateStop, dateStart) : FyConstructionLicenseSpecification.ifDateBetween(column, dateStart, dateStop);
        } else {
            return getScopeQuery(column, list.get(0));
        }
    }

    private Specification<FyConstructionLicense> getScopeQuery(String column, ScopeQueryDate query) {
        Date date = getDate(query.getValue());
        switch (query.getSymbol()) {
            case "<":
                return FyConstructionLicenseSpecification.ifDateLessThan(column, date);
            case "<=":
                return FyConstructionLicenseSpecification.ifDateLessThanOrEqualTo(column, date);
            case ">":
                return FyConstructionLicenseSpecification.ifDateOverThan(column, date);
            case ">=":
                return FyConstructionLicenseSpecification.ifDateOverThanOrEqualTo(column, date);
            default:
                return FyConstructionLicenseSpecification.ifDateEq(column, date);
        }
    }

    private Date getDate(String dateStr) {
        Date parsedDate = null;
        try {
            parsedDate = DateUtil.parse(dateStr);
        } catch (DateException e) {
            log.error(e.getMessage());
            parsedDate = getDateWithDefaultFormat(dateStr);
        }
        return parsedDate;
    }

    private Date getDateWithDefaultFormat(String dateStr) {
        Date parsedDate = null;
        // 尝试解析日期字符串
        for (String dateFormat : possibleDateFormats) {
            try {
                parsedDate = DateUtil.parse(dateStr, dateFormat);
                log.info("成功解析日期：" + parsedDate);
                break;
            } catch (DateException e) {
                // 解析失败，尝试下一个格式
            }
        }
        return parsedDate;
    }

    /**
     * 预加载懒加载的集合，避免LazyInitializationException
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    private void preloadLazyCollections(FyConstructionLicense license) {
        if (license != null) {
            // 触发懒加载
            if (license.getItems() != null) {
                license.getItems().size();
            }
        }
    }
}
