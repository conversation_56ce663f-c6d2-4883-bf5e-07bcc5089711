package cn.fyg.schedule.service.sheets.yx.progressCheck;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.project.yuanXin.progressCheck.YxProgressCheckRecordSign;

public interface YxProgressCheckRecordSignService {
    BaseResponse save(YxProgressCheckRecordSign data);
    BaseResponse delete(Integer id);
    BaseResponse findByParentId(Integer id);
    BaseResponse deleteByParentId(Integer id);
}
