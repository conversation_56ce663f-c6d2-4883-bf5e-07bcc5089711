package cn.fyg.schedule.controller.functions;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.project.xincai.noti.XcNotiRecord;
import cn.fyg.schedule.pojo.project.xincai.noti.XcNotiRecordItem;
import cn.fyg.schedule.pojo.project.xincai.noti.XcNotification;
import cn.fyg.schedule.pojo.project.xincai.progressCheck.XcProgressCheck;
import cn.fyg.schedule.pojo.project.xincai.progressCheck.XcProgressCheckRecord;
import cn.fyg.schedule.pojo.project.xincai.progressCheck.XcProgressCheckRecordItem;
import cn.fyg.schedule.pojo.project.yuanXin.progressCheck.YxProgressCheck;
import cn.fyg.schedule.pojo.project.yuanXin.progressCheck.YxProgressCheckRecord;
import cn.fyg.schedule.pojo.project.yuanXin.progressCheck.YxProgressCheckRecordItem;
import cn.fyg.schedule.pojo.project.yuanXin.security.notification.YxSecurityNotiRecord;
import cn.fyg.schedule.pojo.project.yuanXin.security.notification.YxSecurityNotiRecordItem;
import cn.fyg.schedule.pojo.project.yuanXin.security.notification.YxSecurityNotification;
import cn.fyg.schedule.service.sheets.xincai.notification.XcNotiRecordService;
import cn.fyg.schedule.service.sheets.xincai.notification.XcNotificationService;
import cn.fyg.schedule.service.sheets.xincai.progressCheck.XcProgressCheckRecordService;
import cn.fyg.schedule.service.sheets.xincai.progressCheck.XcProgressCheckService;
import cn.fyg.schedule.service.sheets.yx.progressCheck.YxProgressCheckRecordService;
import cn.fyg.schedule.service.sheets.yx.progressCheck.YxProgressCheckService;
import cn.fyg.schedule.service.sheets.yx.security.notification.YxSecurityNotiRecordService;
import cn.fyg.schedule.service.sheets.yx.security.notification.YxSecurityNotificationService;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateUtil;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;

@Controller
@Slf4j
@RequestMapping("preview")
public class PdfPreviewController {
    private final YxSecurityNotiRecordService yxSecurityNotiRecordService;
    private final YxSecurityNotificationService yxSecurityNotificationService;
    private final YxProgressCheckRecordService yxProgressCheckRecordService;
    private final YxProgressCheckService yxProgressCheckService;

    private final XcNotificationService xcNotificationService;

    private final XcNotiRecordService xcNotiRecordService;
    private final XcProgressCheckService xcProgressCheckService;
    private final XcProgressCheckRecordService xcProgressCheckRecordService;


    public PdfPreviewController(YxSecurityNotiRecordService yxSecurityNotiRecordService, YxSecurityNotificationService yxSecurityNotificationService, YxProgressCheckRecordService yxProgressCheckRecordService, YxProgressCheckService yxProgressCheckService, XcNotificationService xcNotificationService, XcNotiRecordService xcNotiRecordService, XcProgressCheckService xcProgressCheckService, XcProgressCheckRecordService xcProgressCheckRecordService) {
        this.yxSecurityNotiRecordService = yxSecurityNotiRecordService;
        this.yxSecurityNotificationService = yxSecurityNotificationService;
        this.yxProgressCheckRecordService = yxProgressCheckRecordService;
        this.yxProgressCheckService = yxProgressCheckService;
        this.xcNotificationService = xcNotificationService;
        this.xcNotiRecordService = xcNotiRecordService;
        this.xcProgressCheckService = xcProgressCheckService;
        this.xcProgressCheckRecordService = xcProgressCheckRecordService;
    }

    @GetMapping("yx/progress/check/{id}")
    public String yxProcessCheckPdfPreview(Model model, @PathVariable Integer id) throws IOException {
        BaseResponse response = yxProgressCheckService.findById(id);
        if (response.getCode() == 0 && response.getResult() instanceof YxProgressCheck) {
            YxProgressCheck data = (YxProgressCheck) response.getResult();
            model.addAttribute("title", "进度检查单");
            model.addAttribute("attachmentTitle", "进度检查单附件：");
            model.addAttribute("recordTitle", "进度检查单回执单");
            model.addAttribute("recordAttachmentTitle", "进度检查单回执单附件：");
            model.addAttribute("inspectionDate", DateUtil.format(data.getInspectionDate(), "yyyy年MM月dd日"));
            model.addAttribute("workDate", DateUtil.format(data.getWorkDate(), "yyyy年MM月dd日"));
            model.addAttribute("data", data);
            model.addAttribute("problems", data.getYxProblemDescription());
            model.addAttribute("signList", data.getSignatures());
            model.addAttribute("items", splitList(data.getItems(), 2, false));
            model.addAttribute("signDate", DateUtil.format(data.getCreateDate(), "yyyy年MM月dd日"));
            String path = getResourcePath("quality.png", "/static/img/");
            File file = new File(path);
            InputStream in = Files.newInputStream(file.toPath());
            String base64 = Base64.encode(in);
            base64 = "data:image/png;base64," + base64;
            model.addAttribute("seal", base64);
            List<YxProgressCheckRecord> list = yxProgressCheckRecordService.listRecordByParentId(id, 0);
            model.addAttribute("records", splitList(list, 5, true));
            model.addAttribute("recordAttachments", splitList(reFormProgressCheckRecordAttachment(list), 2, false));
        }

        return "/pdf/yx_progress_check";
    }

    @GetMapping("xc/progress/check/{id}")
    public String xcProcessCheckPdfPreview(Model model, @PathVariable Integer id) throws IOException {
        BaseResponse response = xcProgressCheckService.findById(id);
        if (response.getCode() == 0 && response.getResult() instanceof XcProgressCheck) {
            XcProgressCheck data = (XcProgressCheck) response.getResult();
            model.addAttribute("title", "进度检查单");
            model.addAttribute("attachmentTitle", "进度检查单附件：");
            model.addAttribute("recordTitle", "进度检查单回执单");
            model.addAttribute("recordAttachmentTitle", "进度检查单回执单附件：");
            model.addAttribute("inspectionDate", DateUtil.format(data.getInspectionDate(), "yyyy年MM月dd日"));
            model.addAttribute("workDate", DateUtil.format(data.getWorkDate(), "yyyy年MM月dd日"));
            model.addAttribute("data", data);
            model.addAttribute("problems", data.getYxProblemDescription());
            model.addAttribute("signList", data.getSignatures());
            model.addAttribute("items", splitList(data.getItems(), 2, false));
            model.addAttribute("signDate", DateUtil.format(data.getCreateDate(), "yyyy年MM月dd日"));
            String path = getResourcePath("quality.png", "/static/img/");
            File file = new File(path);
            InputStream in = Files.newInputStream(file.toPath());
            String base64 = Base64.encode(in);
            base64 = "data:image/png;base64," + base64;
            model.addAttribute("seal", base64);
            List<XcProgressCheckRecord> list = xcProgressCheckRecordService.listRecordByParentId(id, 0);
            model.addAttribute("records", splitList(list, 5, true));
            model.addAttribute("recordAttachments", splitList(reFormProgressCheckRecordAttachment4Xc(list), 2, false));
        }

        return "/pdf/xc_progress_check";
    }

    @GetMapping("yx/notification/{id}")
    public String test(Model model, @PathVariable Integer id) throws IOException {
        BaseResponse response = yxSecurityNotificationService.findById(id);
        if (response.getCode() == 0 && response.getResult() instanceof YxSecurityNotification) {
            YxSecurityNotification data = (YxSecurityNotification) response.getResult();
            model.addAttribute("title", data.getType() == 1 ? "安全生产整改通知单" : "质量监督检查记录单");
            model.addAttribute("attachmentTitle", data.getType() == 1 ? "安全生产整改通知单附件：" : "质量监督检查记录单附件：");
            model.addAttribute("recordTitle", data.getType() == 1 ? "安全生产整改通知回执单" : "质量监督检查记录回执单");
            model.addAttribute("recordAttachmentTitle", data.getType() == 1 ? "安全生产整改通知回执单附件：" : "质量监督检查记录回执单附件：");
            model.addAttribute("inspectionDate", DateUtil.format(data.getInspectionDate(), "yyyy年MM月dd日"));
            model.addAttribute("data", data);
            model.addAttribute("problems", data.getYxProblemDescription());
            model.addAttribute("signList", data.getSignatures());
            model.addAttribute("items", splitList(data.getItems(), 2, false));
            String path = getResourcePath(data.getType() == 1 ? "security.png" : "quality.png", "/static/img/");
            File file = new File(path);
            InputStream in = Files.newInputStream(file.toPath());
            String base64 = Base64.encode(in);
            base64 = "data:image/png;base64," + base64;
            model.addAttribute("seal", base64);
            List<YxSecurityNotiRecord> list = yxSecurityNotiRecordService.listRecordByParentId(id, 0);
            model.addAttribute("records", splitList(list, 5, true));
            model.addAttribute("recordAttachments", splitList(reFormRecordAttachment(list), 2, false));
        }

        return "pdf_print";
    }

    @GetMapping("xc/notification/{id}")
    public String xcNotificationPreviewer(Model model, @PathVariable Integer id) throws IOException {
        BaseResponse response = xcNotificationService.findById(id);
        if (response.getCode() == 0 && response.getResult() instanceof XcNotification) {
            XcNotification data = (XcNotification) response.getResult();
            model.addAttribute("title", data.getType() == 1 ? "安全生产整改通知单" : "质量监督检查记录单");
            model.addAttribute("attachmentTitle", data.getType() == 1 ? "安全生产整改通知单附件：" : "质量监督检查记录单附件：");
            model.addAttribute("recordTitle", data.getType() == 1 ? "安全生产整改通知回执单" : "质量监督检查记录回执单");
            model.addAttribute("recordAttachmentTitle", data.getType() == 1 ? "安全生产整改通知回执单附件：" : "质量监督检查记录回执单附件：");
            model.addAttribute("inspectionDate", DateUtil.format(data.getInspectionDate(), "yyyy年MM月dd日"));
            model.addAttribute("data", data);
            model.addAttribute("problems", data.getYxProblemDescription());
            model.addAttribute("signList", data.getSignatures());
            model.addAttribute("items", splitList(data.getItems(), 2, false));
            String path = getResourcePath(data.getType() == 1 ? "security.png" : "quality.png", "/static/img/");
            File file = new File(path);
            InputStream in = Files.newInputStream(file.toPath());
            String base64 = Base64.encode(in);
            base64 = "data:image/png;base64," + base64;
            model.addAttribute("seal", base64);
            List<XcNotiRecord> list = xcNotiRecordService.listRecordByParentId(id, 0);
            model.addAttribute("records", splitList(list, 5, true));
            model.addAttribute("recordAttachments", splitList(reForm4XcRecordAttachment(list), 2, false));
        }

        return "/pdf/xc_notification";
    }

    private List<XcProgressCheckRecordItem> reFormProgressCheckRecordAttachment4Xc(List<XcProgressCheckRecord> list) {
        List<XcProgressCheckRecordItem> newList = new ArrayList<>();
        for (XcProgressCheckRecord data : list) {
            List<XcProgressCheckRecordItem> items = data.getItems();
            newList.addAll(items);
        }

        return newList;
    }

    private List<YxProgressCheckRecordItem> reFormProgressCheckRecordAttachment(List<YxProgressCheckRecord> list) {
        List<YxProgressCheckRecordItem> newList = new ArrayList<>();
        for (YxProgressCheckRecord data : list) {
            List<YxProgressCheckRecordItem> items = data.getItems();
            newList.addAll(items);
        }

        return newList;
    }

    private String getResourcePath(String fileName, String documentName) throws IOException {
        File directory = new File("src/main/resources");
        String reportPath = directory.getCanonicalPath();
        return reportPath + documentName + fileName;
    }

    private List<YxSecurityNotiRecordItem> reFormRecordAttachment(List<YxSecurityNotiRecord> list) {
        List<YxSecurityNotiRecordItem> newList = new ArrayList<>();
        for (YxSecurityNotiRecord data : list) {
            List<YxSecurityNotiRecordItem> items = data.getItems();
            newList.addAll(items);
        }
        return newList;
    }

    private List<XcNotiRecordItem> reForm4XcRecordAttachment(List<XcNotiRecord> list) {
        List<XcNotiRecordItem> newList = new ArrayList<>();
        for (XcNotiRecord data : list) {
            List<XcNotiRecordItem> items = data.getItems();
            newList.addAll(items);
        }
        return newList;
    }

    public static <T> List<List<T>> splitList(List<T> list, int chunkSize, boolean except) {
        List<List<T>> result = new ArrayList<>();
        int defaultSize = chunkSize;
        if (except) {
            chunkSize--;
        }
        for (int i = 0; i < list.size(); i += chunkSize) {
            if (except) {
                if (i == 0) {
                    result.add(list.subList(i, Math.min(i + chunkSize, list.size())));
                } else {
                    result.add(list.subList(i, Math.min(i + defaultSize, list.size())));
                    chunkSize = defaultSize;
                }
            } else {
                result.add(list.subList(i, Math.min(i + chunkSize, list.size())));
            }
        }
        return result;
    }
}
