package cn.fyg.schedule.service.fyg.accounting.impl;

import cn.fyg.schedule.dao.fyg.accounting.FygBankAccountsRepository;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.dto.IntegerOptionObj;
import cn.fyg.schedule.pojo.dto.fyg.accounting.bank.BankAccountDTO;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.pojo.fyg.accounting.FygBankAccounts;
import cn.fyg.schedule.reponse.BaseResponse;
import cn.fyg.schedule.service.fyg.accounting.FygBankAccountsService;
import cn.fyg.schedule.utils.ToEntityUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FygBankAccountsServiceImpl implements FygBankAccountsService {
    private final FygBankAccountsRepository r;

    public FygBankAccountsServiceImpl(FygBankAccountsRepository r) {
        this.r = r;
    }
    @Override
    public BaseResponse<List<IntegerOptionObj>> listOptions(Integer dataStatus, Integer projectId) {
        BaseResponse<List<IntegerOptionObj>> resp = new BaseResponse<>();
        try {
            List<IntegerOptionObj> data = r.getOptionObj(dataStatus, projectId);
            resp.initialize(200, "查询成功", data);
        } catch (JpaSystemException e) {
            log.error("查询失败", e);
            resp.initialize(3001, "查询失败", null);
        }
        return resp;
    }

    @Override
    public BaseResponse<List<BankAccountDTO>> listBankAccount(Integer dataStatus, Integer projectId) {
        BaseResponse<List<BankAccountDTO>> resp = new BaseResponse<>();
        try {
            List<FygBankAccounts> data = r.findByDataStatusAndProjectId(dataStatus, projectId);
            List<BankAccountDTO> result = data.stream().map(this::toDto).collect(Collectors.toList());
            resp.initialize(200, "查询成功", result);
        } catch (JpaSystemException e) {
            log.error("查询失败", e);
            resp.initialize(3001, "查询失败", null);
        }

        return resp;
    }

    @Override
    public BaseResponse<BankAccountDTO> findById(Integer id) {
        return null;
    }

    @Transactional(Transactional.TxType.SUPPORTS)
    @Override
    public BaseResponse<BankAccountDTO> save(BankAccountDTO value) {
        BaseResponse<BankAccountDTO> resp = new BaseResponse<>();
        FygBankAccounts entity = toEntity(value);
        if ( entity == null )
            resp.initialize(3001, "参数错误", null);
        else
            try {
                FygBankAccounts data = save(entity);
                resp.initialize(200, "保存成功", toDto(data));
            } catch (MyException e) {
                resp.initialize(e.getCode(), e.getMessage(), null);
            }
        return resp;
    }

    @Override
    public BaseResponse<Void> filter(BankAccountDTO query, Pagination pagination) {
        return null;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse<BankAccountDTO> setDataStatus(Integer id, Integer dataStatus, String updatedBy) {
        BaseResponse<BankAccountDTO> resp = new BaseResponse<>();
        try {
            int i = r.updateDataStatus(id, dataStatus, updatedBy);
            if (i > 0) {
                resp.initialize(200, "删除成功" + i, null);
            } else {
                resp.initialize(3001, "删除失败", null);
            }
        } catch (JpaSystemException e) {
            log.error("删除失败", e);
            resp.initialize(3001, "删除失败", null);
        }
        return resp;
    }

    @Transactional(Transactional.TxType.SUPPORTS)
    @Override
    public BaseResponse<BankAccountDTO> save(BankAccountDTO value, Integer id) {
        BaseResponse<BankAccountDTO> resp = new BaseResponse<>();
        try {
            FygBankAccounts data = update(value, id);
            resp.initialize(200, "更新成功", toDto(data));
        } catch (MyException e) {
            resp.initialize(e.getCode(), e.getMessage(), null);
        }
        return resp;
    }

    private BankAccountDTO toDto(FygBankAccounts entity) {
        BankAccountDTO dto = ToEntityUtil.entityToDTO(entity, new BankAccountDTO());
        assert dto != null;
        if (entity.getProject() != null) {
            dto.setProjectName(entity.getProject().getProjectName());
        }

        return dto;
    }

    private FygBankAccounts toEntity(BankAccountDTO dto) {
        return ToEntityUtil.DTOtoEntity(dto, new FygBankAccounts());
    }

    private FygBankAccounts save(FygBankAccounts entity) throws MyException {
        try {
            return r.save(entity);
        } catch (JpaSystemException e) {
            log.error("保存失败", e);
            throw new MyException(3001, "保存失败");
        }
    }

    private FygBankAccounts update(BankAccountDTO dto, Integer id) throws MyException {
        try {
            FygBankAccounts source = toEntity(dto);
            FygBankAccounts target = getById(id);
            BeanUtils.copyProperties(source, target, "id", "createdAt", "createdBy");
            return save(target);
        } catch (JpaSystemException e) {
            log.error("更新失败", e);
            throw new MyException(3001, "更新失败");
        }
    }

    private FygBankAccounts getById(Integer id) throws MyException {
        try {
            return r.findById(id).orElseThrow(() -> new MyException(5001, "查询失败"));
        } catch (JpaSystemException e) {
            throw new MyException(5001, "id不存在");
        }
    }


}
