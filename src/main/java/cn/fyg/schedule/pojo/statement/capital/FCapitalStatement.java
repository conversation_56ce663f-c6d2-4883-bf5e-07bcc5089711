package cn.fyg.schedule.pojo.statement.capital;

import cn.fyg.schedule.pojo.dto.statement.capital.FCapitalStatementDto;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Setter
@Getter
@ToString
@Table(name = "fcapitalstatement")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class FCapitalStatement {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Column(name = "title")
    private String title;
    @Column(name = "statement_date")
    @Temporal(TemporalType.DATE)
    private Date statementDate;
    @Column(name = "current_day_amount_income")
    private Double currentDayAmountIncome;
    @Column(name = "cumulative_amount_income")
    private Double cumulativeAmountIncome;
    @Column(name = "current_day_amount_expenditure")
    private Double currentDayAmountExpenditure;
    @Column(name = "cumulative_amount_expenditure")
    private Double cumulativeAmountExpenditure;
    @Column(name = "current_day_amount_balance")
    private Double currentDayAmountBalance;
    @Column(name = "cumulative_amount_balance")
    private Double cumulativeAmountBalance;
    @Column(name = "create_date")
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @Column(name = "update_date")
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;
    @Column(name = "creator")
    private String creator;

    @OneToMany(fetch = FetchType.LAZY,
            cascade = CascadeType.ALL)
    @JoinColumn(name = "capital_statement_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    @ToString.Exclude
    private List<FCapitalStatementIncome> incomes = new ArrayList<>();

    @OneToMany(fetch = FetchType.LAZY,
            cascade = CascadeType.ALL)
    @JoinColumn(name = "capital_statement_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    @ToString.Exclude
    private List<FCapitalStatementExpenditure> expenditures = new ArrayList<>();

    @OneToMany(fetch = FetchType.LAZY,
            cascade = CascadeType.ALL)
    @JoinColumn(name = "capital_statement_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    @ToString.Exclude
    private List<FCapitalStatementBalance> balances = new ArrayList<>();

    public static FCapitalStatement initialize(FCapitalStatementDto dto) {
        FCapitalStatement data = new FCapitalStatement();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        data.setTitle(dto.getTitle());
        data.setCreator(dto.getCreator());
        data.setStatementDate(DateUtil.parseDate(dto.getStatementDate()));
        if (dto.getCreateDate() != null) {
            data.setCreateDate(DateUtil.parse(dto.getCreateDate()));
        }
        return data;
    }
}
