package cn.fyg.schedule.service.fyjs.bidding.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.fyjs.bidding.FyjsBiddingOpenRepository;
import cn.fyg.schedule.exception.MyException;
import cn.fyg.schedule.pojo.dto.fyjs.bidding.FyjsBiddingOpenDto;
import cn.fyg.schedule.pojo.fyjs.bidding.FyjsBiddingOpen;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.fyjs.bidding.FyjsBiddingOpenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class FyjsBiddingOpenServiceImpl implements FyjsBiddingOpenService {
    private final FyjsBiddingOpenRepository r;

    public FyjsBiddingOpenServiceImpl(FyjsBiddingOpenRepository r) {
        this.r = r;
    }

    @Override
    public BaseResponse save(FyjsBiddingOpenDto dto) {
        FyjsBiddingOpen data = FyjsBiddingOpen.initialized(dto);
        return BaseService.save(data, r, FyjsBiddingOpen.class);
    }

    @Override
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findByParentId(Integer parentId) {
        BaseResponse response = BaseResponse.initialize();
        try {
            response.setResult(getByParentId(parentId));
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }

    @Override
    public BaseResponse findByParentIdAndThirdPartyId(Integer parentId, String thirdPartyId) {
        BaseResponse response = BaseResponse.initialize();
        try {
            response.setResult(getByParentIdAndThirdPartyId(parentId, thirdPartyId));
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }

    @Override
    public BaseResponse deleteByParentIdAndThirdPartyId(Integer parentId, String thirdPartyId) {
        BaseResponse response = BaseResponse.initialize();
        try {
            Integer count = deleteOpener(parentId, thirdPartyId);
            response.setResult(count);
            response.setMsg("删除" + count + "条数据。");
        } catch (MyException e) {
            response.setCode(e.getCode());
            response.setMsg(e.getErrMsg());
        }
        return response;
    }

    private Integer deleteOpener(Integer parentId, String thirdPartyId) throws MyException {
        try {
            return r.deleteByParentIdAndThirdPartyId(parentId, thirdPartyId);
        } catch (JpaSystemException e) {
            throw new MyException(500, e.getMessage());
        }
    }

    private FyjsBiddingOpen getByParentIdAndThirdPartyId(Integer parentId, String thirdPartyId) throws MyException {
        try {
            Optional<FyjsBiddingOpen> optional = r.findByParentIdAndThirdPartyId(parentId, thirdPartyId);
            return optional.orElseThrow(() -> new MyException(1, "未查到数据"));
        } catch (JpaSystemException e) {
            throw new MyException(500, "查询出错");
        }
    }

    private List<FyjsBiddingOpen> getByParentId(Integer parentId) throws MyException {
        try {
            return r.findByParentId(parentId);
        } catch (JpaSystemException e) {
            log.info(e.getMessage());
            throw new MyException(500, "查询失败");
        }
    }
}
