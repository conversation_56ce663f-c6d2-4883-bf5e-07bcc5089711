package cn.fyg.schedule.controller.fyjs;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.fyjs.staff.FyjsStaffBaseInfoDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.service.fyjs.staff.FyjsStaffBaseInfoService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("js/staff")
public class FyJsStaffController {
    private final FyjsStaffBaseInfoService service;

    public FyJsStaffController(FyjsStaffBaseInfoService service) {
        this.service = service;
    }

    @PostMapping("save/base/info")
    public BaseResponse saveBaseInfo(FyjsStaffBaseInfoDto dto) {
        return service.save(dto);
    }

    @PostMapping("delete/base/info")
    public BaseResponse deleteBaseInfo(FyjsStaffBaseInfoDto dto) {
        return service.delete(dto);
    }

    @PostMapping("filter")
    public BaseResponse filter(Pagination pagination, FyjsStaffBaseInfoDto dto) {
        return service.filter(pagination, dto);
    }

    @PostMapping("find/base/info/id")
    public BaseResponse findBaseInfoById(Integer id) {
        return service.findById(id);
    }

    @PostMapping("find/base/info/idcard/status")
    public BaseResponse findBaseInfoByIdCardAndStatus(String idCard, Integer status) {
        return service.findByIdCardAndStatus(idCard, status);
    }

    @PutMapping("update/avatar")
    public BaseResponse updateAvatar(Integer id, String avatar) {
        return service.updateAvatar(id, avatar);
    }
}
