package cn.fyg.schedule.service.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.yuanxin.FSignatureRepository;
import cn.fyg.schedule.pojo.FSignature;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.FSignatureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class FSignatureServiceImpl implements FSignatureService{
    @Autowired private FSignatureRepository r;
    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FSignature signature) {
        return BaseService.save(signature, r, FSignature.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findByAssignmentId(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            List<FSignature> result = r.findByAssignmentId(id);
            baseResponse.setResult(result);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            Optional<FSignature> result = r.findById(id);
            if (result.isPresent()) {
                baseResponse.setResult(result.get());
            }
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg(e.getMessage());
            log.info(e.getMessage());
        }
        return baseResponse;
    }

}
