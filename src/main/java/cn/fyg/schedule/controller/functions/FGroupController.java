package cn.fyg.schedule.controller.functions;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.fyapp.FGroupListDto;
import cn.fyg.schedule.pojo.fyapp.FGroupList;
import cn.fyg.schedule.service.fyapp.FGroupListS;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("group")
public class FGroupController {
    private final FGroupListS s;
    public FGroupController(FGroupListS s) {
        this.s = s;
    }

    @PostMapping("save")
    BaseResponse save(FGroupListDto dto) {
        FGroupList data = FGroupList.initialize(dto);
        return s.save(data);
    }

    @PostMapping("find/key")
    BaseResponse findByGroupKey(String groupKey) {
        return s.findByGroupKey(groupKey);
    }

    @PostMapping("delete")
    BaseResponse delete(Integer id) {
        return s.delete(id);
    }

    @PostMapping("list")
    BaseResponse listAll() {
        return s.listAll();
    }

    @PostMapping("find/id")
    BaseResponse findById(Integer id) {
        return s.findById(id);
    }
}
