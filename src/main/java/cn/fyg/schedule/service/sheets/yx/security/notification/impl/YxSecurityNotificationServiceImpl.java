package cn.fyg.schedule.service.sheets.yx.security.notification.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.yuanxin.security.notification.YxSecurityNotificationRepository;
import cn.fyg.schedule.pojo.dto.project.yx.security.notification.YxSecurityNotificationDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.pojo.project.yuanXin.security.notification.YxSecurityNotification;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.sheets.yx.security.notification.YxSecurityNotificationService;
import cn.fyg.schedule.specification.yx.YxSecurityNotificationSpecification;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class YxSecurityNotificationServiceImpl implements YxSecurityNotificationService {
    private final YxSecurityNotificationRepository r;

    public YxSecurityNotificationServiceImpl(YxSecurityNotificationRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(YxSecurityNotificationDto dto) {
        YxSecurityNotification data = YxSecurityNotification.initialize(dto);
        return BaseService.save(data, r, YxSecurityNotification.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findById(Integer id) {
        BaseResponse response = BaseResponse.initialize();
        YxSecurityNotification data = getDataById(id);
        if (data != null) {
            response.setResult(data);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    private YxSecurityNotification getDataById(Integer id) {
        try {
            Optional<YxSecurityNotification> optional = r.findById(id);
            if (optional.isPresent()) {
                return optional.get();
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    @Override
    public BaseResponse filter(YxSecurityNotificationDto dto, Pagination pagination) {
        BaseResponse response = BaseResponse.initialize();
        try {
            Sort sort = Sort.unsorted();
            if (!StrUtil.isEmpty(pagination.getColumnKey())) {
                if (!pagination.getOrder().equals("false")) {
                    sort = Sort.by(pagination.getOrder().equals("ascend") ? Sort.Direction.ASC : Sort.Direction.DESC, pagination.getColumnKey());
                }
            }
            Pageable pageable = PageRequest.of(pagination.getCurrentPage(), pagination.getPageSize(), sort);
            Specification<YxSecurityNotification> specification = getSpecification(dto);
            Page<YxSecurityNotification> data = r.findAll(specification, pageable);
            response.setResult(data);
        } catch (Exception e) {
            log.info(e.getMessage());
            response.setCode(1);
            response.setMsg("failed");
        }
        return response;
    }

    @Override
    public BaseResponse getProjectNameList(Integer type) {
        BaseResponse response = BaseResponse.initialize();
        try {
            List<String> list = r.getProjectNameHistoryList(type);
            response.setResult(list);
        } catch (Exception e) {
            response.setCode(1);
            response.setMsg("查询出错");
        }
        return response;
    }

    @Override
    public BaseResponse getEventNameList(Integer type) {
        BaseResponse response = BaseResponse.initialize();
        try {
            List<String> list = r.getEventNameHistoryList(type);
            response.setResult(list);
        } catch (Exception e) {
            response.setCode(1);
            response.setMsg("查询出错");
        }
        return response;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public YxSecurityNotification getById(Integer id) {
        try {
            Optional<YxSecurityNotification> optional = r.findById(id);
            if (optional.isPresent()) {
                YxSecurityNotification data = optional.get();
                // 预加载懒加载的集合，避免LazyInitializationException
                preloadLazyCollections(data);
                return data;
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    @Override
    public BaseResponse findByIdIn(Integer type, List<Integer> ids) {
        BaseResponse response = BaseResponse.initialize();
        List<YxSecurityNotification> list = listByIds(type, ids);
        if (list != null) {
            response.setResult(list);
        } else {
            response.setCode(1);
            response.setMsg("查询失败");
        }
        return response;
    }

    private List<YxSecurityNotification> listByIds(Integer type, List<Integer> ids) {
        try {
            List<YxSecurityNotification> list = r.findByDataStatusAndTypeAndIdIn(0, type, ids);
            if (list.size() > 0) {
                return list;
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }


    private Specification<YxSecurityNotification> getSpecification(YxSecurityNotificationDto dto) {
        Specification<YxSecurityNotification> specification = Specification.where(YxSecurityNotificationSpecification.ifEq("type", dto.getType()))
                .and(dto.getDataStatus() == null ? null : YxSecurityNotificationSpecification.ifEq("dataStatus", dto.getDataStatus()))
                .and(dto.getEventStatus() == null ? null : YxSecurityNotificationSpecification.ifEq("eventStatus", dto.getEventStatus()))
                .and(StrUtil.isEmpty(dto.getCreatorName()) ? null : YxSecurityNotificationSpecification.ifContain( "creatorName", dto.getCreatorName()))
                .and(StrUtil.isEmpty(dto.getYxProjectName()) ? null : YxSecurityNotificationSpecification.ifContain("yxProjectName", dto.getYxProjectName()))
                .and(StrUtil.isEmpty(dto.getYxProblemDescription()) ? null : YxSecurityNotificationSpecification.ifContain("yxProblemDescription", dto.getYxProblemDescription()))
                .and(dto.getInspectionDate() == null ? null : YxSecurityNotificationSpecification.ifDateEq("inspectionDate", DateUtil.date(dto.getInspectionDate())));
        return specification;
    }

    /**
     * 预加载懒加载的集合，避免LazyInitializationException
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    private void preloadLazyCollections(YxSecurityNotification data) {
        if (data != null) {
            // 触发懒加载 - 我们只需要触发加载，不需要使用返回值
            if (data.getItems() != null) {
                data.getItems().size(); // 触发懒加载
            }
            if (data.getSignatures() != null) {
                data.getSignatures().size(); // 触发懒加载
            }
        }
    }

}
