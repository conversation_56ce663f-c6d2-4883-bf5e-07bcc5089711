package cn.fyg.schedule.service.project.management.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.projectManagement.FProjectProgressItemRepository;
import cn.fyg.schedule.pojo.project.management.FProjectProgressItem;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.project.management.FProjectProgressItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;

@Service
@Slf4j
public class FProjectProgressItemServiceImpl implements FProjectProgressItemService {
    private final FProjectProgressItemRepository r;

    public FProjectProgressItemServiceImpl(FProjectProgressItemRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(FProjectProgressItem data) {
        return BaseService.save(data, r, FProjectProgressItem.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findByParentId(Integer parentId) {
        BaseResponse response = BaseResponse.initialize();
        List<FProjectProgressItem> list = getItemsByParentId(parentId);
        if (list != null) {
            response.setResult(list);
        } else {
            response.setCode(1);
            response.setMsg("查询出错");
        }
        return response;
    }

    private List<FProjectProgressItem> getItemsByParentId(Integer parentId) {
        try {
            List<FProjectProgressItem> items = r.findByParentId(parentId);
            return items;
        } catch (Exception e) {
            log.error(e.getLocalizedMessage());
        }
        return null;
    }
}
