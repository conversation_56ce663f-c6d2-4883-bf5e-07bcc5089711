package cn.fyg.schedule.service.fyg.accounting;

import cn.fyg.schedule.pojo.dto.fyg.accounting.report.summary.DailyReportSummaryDTO;
import cn.fyg.schedule.pojo.dto.fyg.accounting.report.summary.DailyReportSummaryQuery;
import cn.fyg.schedule.reponse.BaseResponse;
import org.springframework.data.domain.Page;

public interface FygDailyReportSummaryService extends FygBaseService<DailyReportSummaryDTO,
        DailyReportSummaryDTO, Page<DailyReportSummaryDTO>, DailyReportSummaryQuery> {
    BaseResponse<DailyReportSummaryDTO> updateDailyReportSummary(DailyReportSummaryDTO dto);
}
