package cn.fyg.schedule.pojo.dto.fyapp;

import lombok.Data;

@Data
public class AppDTO {
    private Integer id;
    private String name;
    private String appValue;
    private String appSrc;
    private Integer status;
    private String appGroup;
    private Integer groupId;
    private Integer appSort;

    public AppDTO() {
        super();
    }
    public AppDTO(Integer id, String name, String appValue, String appSrc, Integer status, String appGroup, Integer groupId, Integer appSort) {
        super();
        this.id = id;
        this.name = name;
        this.appValue = appValue;
        this.appSrc = appSrc;
        this.status = status;
        this.appGroup = appGroup;
        this.groupId = groupId;
        this.appSort = appSort;
    }
}
