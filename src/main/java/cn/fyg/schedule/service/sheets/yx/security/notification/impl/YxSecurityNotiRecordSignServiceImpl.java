package cn.fyg.schedule.service.sheets.yx.security.notification.impl;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.dao.documnets.yuanxin.security.notification.YxSecurityNotiRecordSignRepository;
import cn.fyg.schedule.pojo.project.yuanXin.security.notification.YxSecurityNotiRecordSign;
import cn.fyg.schedule.service.BaseService;
import cn.fyg.schedule.service.sheets.yx.security.notification.YxSecurityNotiRecordSignService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;

@Service
@Slf4j
public class YxSecurityNotiRecordSignServiceImpl implements YxSecurityNotiRecordSignService {
    private final YxSecurityNotiRecordSignRepository r;

    public YxSecurityNotiRecordSignServiceImpl(YxSecurityNotiRecordSignRepository r) {
        this.r = r;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse save(YxSecurityNotiRecordSign data) {
        return BaseService.save(data, r, YxSecurityNotiRecordSign.class);
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse delete(Integer id) {
        return BaseService.delete(id, r);
    }

    @Override
    public BaseResponse findByParentId(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        List<YxSecurityNotiRecordSign> list = listSignature(id);
        if (list != null) {
            baseResponse.setResult(list);
        } else {
            baseResponse.setCode(1);
            baseResponse.setMsg("查询失败");
        }
        return baseResponse;
    }

    private List<YxSecurityNotiRecordSign> listSignature(Integer id) {
        try {
            List<YxSecurityNotiRecordSign> list = r.findByParentId(id);
            if (!list.isEmpty()) {
                return list;
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    @Override
    @Transactional(Transactional.TxType.SUPPORTS)
    public BaseResponse deleteByParentId(Integer id) {
        BaseResponse baseResponse = BaseResponse.initialize();
        try {
            r.deleteByParentId(id);
        } catch (Exception e) {
            baseResponse.setCode(1);
            baseResponse.setMsg("发生错误:" + e.getMessage());
        }
        return baseResponse;
    }
}
