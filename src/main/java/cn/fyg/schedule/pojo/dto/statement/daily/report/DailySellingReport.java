package cn.fyg.schedule.pojo.dto.statement.daily.report;

import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

public interface DailySellingReport {
    Integer getId();
    String getFname_l2();
    Integer getSumnum();
    Double getZhz();
    Integer getYtnum();
    Integer getJrysnum();
    Double getJrxsje(); //今日销售额
    Double getJrhk(); //今日回款
    Integer getJnysnum(); //今年已售套数
    Double getJnxsje(); //今年销售额
    Double getJnhk(); //今年回款
    Integer getYsnum(); //总已售套数
    Double getFdealtotalamount(); //总销售额
    Double getLjhk(); //累计回款
    Double getZpqh(); //整盘销售套数去化
    Double getYsks(); //已售可收
    Double getWsks(); //未售可收
    String getCreator();
    @Temporal(TemporalType.TIMESTAMP)
    Date getCreate_date();
    @Temporal(TemporalType.TIMESTAMP)
    Date getUpdate_date();
    @Temporal(TemporalType.DATE)
    Date getData_date();
}
