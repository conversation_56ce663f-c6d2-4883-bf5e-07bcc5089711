package cn.fyg.schedule.controller.yuanxin;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.project.yuanXin.FSealControl;
import cn.fyg.schedule.pojo.dto.FSealControlDto;
import cn.fyg.schedule.pojo.dto.query.FScheduleQuery;
import cn.fyg.schedule.pojo.project.yuanXin.FSealControlItem;
import cn.fyg.schedule.pojo.project.yuanXin.FSealControlSignature;
import cn.fyg.schedule.service.sheets.FSealControlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("seal")
public class FSealControlController {
    @Autowired private FSealControlService s;

    @PostMapping("save")
    public BaseResponse save(FSealControlDto dto) {
        FSealControl data = FSealControl.initialize(dto);
        return s.save(data);
    }

    @PostMapping("delete")
    public BaseResponse delete(Integer id) {
        return s.delete(id);
    }

    @PostMapping("find")
    public BaseResponse findById(Integer id) {
        return s.findById(id);
    }

    @PostMapping("paging")
    public BaseResponse listAll(FScheduleQuery query) {
        return s.findAll(query.getPage(), query.getSize());
    }

    @PostMapping("list")
    public BaseResponse listAll() {
        return s.findAll();
    }

    @PostMapping("history/departmentname")
    public BaseResponse getDepartmentNameList() {
        return s.getDepartmentNameHistoryList();
    }

    @PostMapping("history/sealtype")
    public BaseResponse getSealTypeList() {
        return s.getSealTypeHistoryList();
    }

    @PostMapping("create/item")
    public BaseResponse createItem(FSealControlItem data) {
        return s.saveItem(data);
    }

    @PostMapping("delete/item")
    public BaseResponse deleteItem(Integer id) {
        return s.deleteItem(id);
    }

    @PostMapping("find/item")
    public BaseResponse findItem(Integer id) { return s.findItems(id); }

    @PostMapping("create/signature")
    public BaseResponse createSignature(FSealControlSignature data) {
        return s.saveSignature(data);
    }

    @PostMapping("delete/signature")
    public BaseResponse deleteSignature(Integer id) {
        return s.deleteSignature(id);
    }

    @PostMapping("find/signature")
    public BaseResponse findSignature(Integer id) { return s.findSignatureList(id); }
}
