package cn.fyg.schedule.service.news.awards;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.news.awads.FPersonalAwards;

import java.util.Date;
import java.util.List;

public interface FPersonalAwardsS {
    BaseResponse save(FPersonalAwards data);
    BaseResponse findById(Integer id);
    BaseResponse delete(Integer id);
    BaseResponse getCompanyHistoryList();
    BaseResponse listAll();
    BaseResponse listByAwardDate(Date begin, Date end);
    BaseResponse listCompanyIn(List<String> companies);
}
