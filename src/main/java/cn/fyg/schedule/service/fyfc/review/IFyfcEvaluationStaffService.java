package cn.fyg.schedule.service.fyfc.review;

import cn.fyg.schedule.pojo.dto.fyfc.review.*;

import java.util.List;

/**
 * FYFC 评价员工服务接口
 * 对应 staff/dashboard 和 staff/edit 页面的业务逻辑
 */
public interface IFyfcEvaluationStaffService {

    // ==================== Staff Dashboard 相关方法 ====================

    /**
     * 员工查询自己的评价历史
     * @param userName 用户名
     * @param queryDto 查询条件
     * @return 统一API响应格式的分页结果
     */
    FyfcApiResponseDto<FyfcPaginatedResponseDto<FyfcEvaluationDto>> getEvaluationHistory(String userName, FyfcEvaluationQueryDto queryDto);

    /**
     * 员工查询待办评价
     * @param userName 用户名
     * @return 统一API响应格式的待办评价列表
     */
    FyfcApiResponseDto<List<FyfcEvaluationDto>> getPendingEvaluations(String userName);

    /**
     * 获取员工评价统计
     * @param userName 用户名
     * @return 统一API响应格式的统计数据
     */
    FyfcApiResponseDto<FyfcEvaluationStatsDto> getEvaluationStats(String userName);

    // ==================== Staff Edit 相关方法 ====================

    /**
     * 创建新的评价
     * @param formDto 评价表单数据
     * @param creatorName 创建人
     * @return 统一API响应格式的创建结果
     */
    FyfcApiResponseDto<FyfcEvaluationDto> createEvaluation(FyfcEvaluationFormDto formDto, String creatorName);

    /**
     * 更新评价基本信息
     * @param updateDto 更新数据
     * @param updaterName 更新人
     * @return 统一API响应格式
     */
    FyfcApiResponseDto<Boolean> updateEvaluation(FyfcEvaluationUpdateDto updateDto, String updaterName);

    /**
     * 获取评价详情
     * @param evaluationId 评价ID
     * @param userName 用户名
     * @return 统一API响应格式的评价详情
     */
    FyfcApiResponseDto<FyfcEvaluationDetailDto> getEvaluationDetail(Integer evaluationId, String userName);

    /**
     * 提交自评分数
     * @param scoreForm 评分表单
     * @param evaluatorName 评价人
     * @return 统一API响应格式
     */
    FyfcApiResponseDto<Boolean> submitSelfScore(FyfcScoreFormDto scoreForm, String evaluatorName);

    /**
     * 提交同事评分
     * @param scoreForm 评分表单
     * @param evaluatorName 评价人
     * @return 统一API响应格式
     */
    FyfcApiResponseDto<Boolean> submitColleagueScore(FyfcScoreFormDto scoreForm, String evaluatorName);

    /**
     * 删除评价（仅限创建人）
     * @param evaluationId 评价ID
     * @param operatorName 操作人
     * @return 统一API响应格式
     */
    FyfcApiResponseDto<Boolean> deleteEvaluation(Integer evaluationId, String operatorName);

    /**
     * 检查用户是否有权限访问指定评价
     * @param evaluationId 评价ID
     * @param userName 用户名
     * @return 统一API响应格式的权限检查结果
     */
    FyfcApiResponseDto<Boolean> checkUserPermission(Integer evaluationId, String userName);

    /**
     * 获取评价表单初始数据
     * @param userName 用户名
     * @return 统一API响应格式的表单数据
     */
    FyfcApiResponseDto<Object> getFormInitData(String userName);
}
