package cn.fyg.schedule.controller.mini.app;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.FScheduleItem;
import cn.fyg.schedule.pojo.dto.FScheduleItemDto;
import cn.fyg.schedule.service.FScheduleItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("scheduleitem")
public class FScheduleItemController {
    @Autowired private FScheduleItemService s;

    @PostMapping("save")
    public BaseResponse save(FScheduleItemDto dto) {
        FScheduleItem data = FScheduleItem.initialize(dto);
        return s.save(data);
    }

    @PostMapping("delete")
    public BaseResponse delete(Integer id) {
        return s.delete(id);
    }

    @PostMapping("find")
    public BaseResponse findById(Integer id) {
        return s.findById(id);
    }
    @PostMapping("list")
    public BaseResponse findByScheduleId(Integer id) {
        return s.findByScheduleId(id);
    }
}
