package cn.fyg.schedule.service.sheets.yx.security.notification;

import cn.fyg.schedule.base.BaseResponse;
import cn.fyg.schedule.pojo.dto.project.yx.security.notification.YxSecurityNotificationDto;
import cn.fyg.schedule.pojo.dto.query.Pagination;
import cn.fyg.schedule.pojo.project.yuanXin.security.notification.YxSecurityNotification;

import java.util.List;

public interface YxSecurityNotificationService {
    BaseResponse save(YxSecurityNotificationDto dto);
    BaseResponse delete(Integer id);
    BaseResponse findById(Integer id);
    BaseResponse filter(YxSecurityNotificationDto dto, Pagination pagination);
    BaseResponse getProjectNameList(Integer type);
    BaseResponse getEventNameList(Integer type);
    YxSecurityNotification getById(Integer id);
    BaseResponse findByIdIn(Integer type, List<Integer> ids);
}
