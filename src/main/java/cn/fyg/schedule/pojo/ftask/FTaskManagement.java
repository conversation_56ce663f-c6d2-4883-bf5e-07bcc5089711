package cn.fyg.schedule.pojo.ftask;

import cn.fyg.schedule.pojo.dto.ftask.FTaskManagementDto;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Setter
@Getter
@ToString
@Table(name = "f_task_management")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class FTaskManagement {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Column(name = "type_key")
    private String typeKey;
    @Column(name = "type_ch")
    private String typeCh;
    private String title;
    private String creator;
    private String creatorName;
    private String content;
    private String blockName;
    private String roomNumber;
    private String contacts;
    private String phoneNumber;
    private Boolean needSign;
    private Boolean isJudge;
    @Column(name = "create_date")
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @Column(name = "update_date")
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "task_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    @ToString.Exclude
    private List<FTaskAttachment> items = new ArrayList<>();

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "task_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    @ToString.Exclude
    private List<FTaskApprove> comments = new ArrayList<>();

    public static FTaskManagement initialize(FTaskManagementDto dto) {
        FTaskManagement data = new FTaskManagement();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        data.setTypeKey(dto.getTypeKey());
        data.setTypeCh(dto.getTypeCh());
        data.setTitle(dto.getTitle());
        data.setCreator(dto.getCreator());
        data.setCreatorName(dto.getCreatorName());
        data.setContent(dto.getContent());
        data.setNeedSign(dto.getNeedSign());
        data.setIsJudge(dto.getIsJudge());
        data.setContacts(dto.getContacts());
        data.setBlockName(dto.getBlockName());
        data.setPhoneNumber(dto.getPhoneNumber());
        data.setRoomNumber(dto.getRoomNumber());
        if (dto.getCreateDate() != null) {
            data.setCreateDate(DateUtil.parse(dto.getCreateDate()));
        }
        return data;
    }
}
