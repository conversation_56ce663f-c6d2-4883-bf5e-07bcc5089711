package cn.fyg.schedule.pojo.dto.statement.daily.report;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FDailySellingReportResult {
    private Integer id;
    private String fname_l2; //项目名称
    private Integer sumnum; //总套数
    private Double zhz; //总货值
    private Integer ytnum; //已开盘套数
    private Integer jrysnum; //今日已售套数
    private Double jrxsje; //今日销售额
    private Double jrhk; //今日回款
    private Integer jnysnum; //今年已售套数
    private Double jnxsje; //今年销售额
    private Double jnhk; //今年回款
    private Integer ysnum; //总已售套数
    private Double fdealtotalamount; //总销售额
    private Double ljhk; //累计回款
    private Double zpqh; //整盘销售套数去化
    private Double ysks; //已售可收
    private Double wsks; //未售可收
    private String creator;
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;
    @Temporal(TemporalType.DATE)
    private Date dataDate;
}
