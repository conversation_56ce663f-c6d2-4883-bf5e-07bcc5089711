package cn.fyg.schedule.pojo.fyjs.bidding;

import cn.fyg.schedule.pojo.dto.fyjs.bidding.FyjsBiddingTendererDto;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Setter
@Getter
@ToString
@Table(name = "fyjs_bidding_tenderer")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class FyjsBiddingTenderer {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Column(name = "parent_id")
    private Integer parentId;
    private String creator;
    private String lastEditor;
    private String tendererId;
    private String tendererName;
    private Integer projectTimeLimit;
    private BigDecimal price;
    private Integer winner;
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;
    private String avatar;

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "parent_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    @ToString.Exclude
    List<FyjsBiddingDocument> documents = new ArrayList<>();

    public static FyjsBiddingTenderer initialized(FyjsBiddingTendererDto dto) {
        FyjsBiddingTenderer data = new FyjsBiddingTenderer();
        if (dto.getId() != null) {
            data.setId(dto.getId());
        }
        data.setCreator(dto.getCreator());
        data.setLastEditor(dto.getLastEditor());
        if (dto.getCreateDate() != null) {
            data.setCreateDate(DateUtil.date(dto.getCreateDate()));
        }
        data.setParentId(dto.getParentId());
        data.setTendererId(dto.getTendererId());
        data.setTendererName(dto.getTendererName());
        data.setProjectTimeLimit(dto.getProjectTimeLimit());
        data.setPrice(dto.getPrice());
        data.setWinner(dto.getWinner());
        data.setAvatar(dto.getAvatar());
        return data;
    }
}
